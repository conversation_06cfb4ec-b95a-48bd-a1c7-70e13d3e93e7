# Environment variables
.env
.env.*
!.env.example

# macOS system files
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
.env3/
.pyenv/

# Jupyter Notebook
.ipynb_checkpoints

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.spyderproject
.spyproject
.ropeproject

# Logs and databases
*.log
*.sqlite
*.db

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Local development configuration
.local/

# Distribution / packaging
*.tar.gz
*.tgz
*.zip 