import 'package:flutter_dotenv/flutter_dotenv.dart';

/// API Keys configuration using environment variables
///
/// IMPORTANT: API keys are loaded from .env file
/// Never commit real API keys to version control!
///
/// To get your Google AI API key:
/// 1. Go to https://makersuite.google.com/app/apikey
/// 2. Create a new API key
/// 3. Add it to .env file as GEMINI_API_KEY=your_key_here
///
class ApiKeys {
  /// Initialize environment variables
  static Future<void> initialize() async {
    try {
      await dotenv.load(fileName: ".env");
    } catch (e) {
      print('Warning: Could not load .env file: $e');
    }
  }

  /// Google AI API Key for Gemini models from environment
  static String get googleAI => dotenv.env['GEMINI_API_KEY'] ?? '';

  /// Check if Google AI API key is configured
  static bool get isGoogleAIConfigured =>
      googleAI.isNotEmpty && googleAI != 'YOUR_GOOGLE_AI_API_KEY_HERE';

  /// Get Gemini model name from environment - Updated to use Gemma 3N
  static String get geminiModel => dotenv.env['GEMINI_MODEL_NAME'] ?? 'gemma-3n-e4b-it';

  /// Get temperature setting from environment
  static double get temperature =>
      double.tryParse(dotenv.env['TEMPERATURE'] ?? '0.1') ?? 0.1;

  /// Get max tokens from environment
  static int get maxTokens =>
      int.tryParse(dotenv.env['MAX_NEW_TOKENS'] ?? '2048') ?? 2048;

  /// Get debug mode setting
  static bool get debugMode =>
      dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';

  /// Get app name from environment
  static String get appName => dotenv.env['APP_NAME'] ?? 'SimulTrans AI';

  /// Get app version from environment
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
}

/// Instructions for getting API keys
class ApiKeyInstructions {
  static const String googleAI = '''
🔑 Para configurar sua chave da API do Google AI:

1. 🌐 Acesse: https://makersuite.google.com/app/apikey
2. 👤 Faça login com sua conta Google
3. ➕ Clique em "Create API Key"
4. 📋 Copie a chave gerada
5. 📁 Abra o arquivo .env na raiz do projeto
6. ✏️ Substitua YOUR_GOOGLE_AI_API_KEY_HERE pela sua chave real

Exemplo no arquivo .env:
GEMINI_API_KEY=AIzaSyD...sua_chave_aqui

🎯 Modelos Gemma 3N suportados:
- gemma-3n-e4b-it (padrão - 4B parâmetros efetivos)
- gemma-3n-e2b-it (2B parâmetros efetivos)

🚀 Recursos avançados do Gemma 3N:
- Entrada multimodal (texto, imagem, áudio)
- Arquitetura MatFormer otimizada
- PLE caching para eficiência de memória
- Codificador MobileNet-V5 para visão
- Suporte a 140+ idiomas

⚙️ Configurações opcionais no .env:
GEMINI_MODEL_NAME=gemma-3n-e4b-it
TEMPERATURE=0.1
MAX_NEW_TOKENS=2048

⚠️ IMPORTANTE:
- Nunca compartilhe sua chave da API publicamente!
- O arquivo .env deve estar no .gitignore
- Reinicie o app após alterar o .env
''';
}
