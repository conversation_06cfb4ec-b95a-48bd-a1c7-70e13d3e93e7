/// API Keys configuration
/// 
/// IMPORTANT: Never commit real API keys to version control!
/// This file should be added to .gitignore in production.
/// 
/// To get your Google AI API key:
/// 1. Go to https://makersuite.google.com/app/apikey
/// 2. Create a new API key
/// 3. Replace the placeholder below with your actual key
/// 
class ApiKeys {
  /// Google AI API Key for Gemini models
  /// Get your key from: https://makersuite.google.com/app/apikey
  static const String googleAI = 'YOUR_GOOGLE_AI_API_KEY_HERE';
  
  /// Check if Google AI API key is configured
  static bool get isGoogleAIConfigured => googleAI != 'YOUR_GOOGLE_AI_API_KEY_HERE';
  
  /// Other API keys can be added here
  // static const String openAI = 'YOUR_OPENAI_API_KEY_HERE';
  // static const String azure = 'YOUR_AZURE_API_KEY_HERE';
}

/// Instructions for getting API keys
class ApiKeyInstructions {
  static const String googleAI = '''
Para obter sua chave da API do Google AI:

1. Acesse: https://makersuite.google.com/app/apikey
2. Faça login com sua conta Google
3. Clique em "Create API Key"
4. Copie a chave gerada
5. Cole no arquivo lib/core/config/api_keys.dart
6. Substitua 'YOUR_GOOGLE_AI_API_KEY_HERE' pela sua chave real

Exemplo:
static const String googleAI = 'AIzaSyD...sua_chave_aqui';

IMPORTANTE: Nunca compartilhe sua chave da API publicamente!
''';
}
