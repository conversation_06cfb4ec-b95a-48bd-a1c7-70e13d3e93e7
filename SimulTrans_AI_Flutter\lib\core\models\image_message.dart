import 'dart:typed_data';

/// Represents a message that can contain both text and image
class ImageMessage {
  final String text;
  final bool isUser;
  final Uint8List? imageBytes;
  final DateTime timestamp;
  final String? imageName;

  ImageMessage({
    required this.text,
    this.isUser = false,
    this.imageBytes,
    DateTime? timestamp,
    this.imageName,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Check if this message has an image
  bool get hasImage => imageBytes != null;

  /// Create a text-only message
  factory ImageMessage.text({
    required String text,
    bool isUser = false,
    DateTime? timestamp,
  }) {
    return ImageMessage(
      text: text,
      isUser: isUser,
      timestamp: timestamp,
    );
  }

  /// Create a message with both text and image
  factory ImageMessage.withImage({
    required String text,
    required Uint8List imageBytes,
    bool isUser = false,
    DateTime? timestamp,
    String? imageName,
  }) {
    return ImageMessage(
      text: text,
      imageBytes: imageBytes,
      isUser: isUser,
      timestamp: timestamp,
      imageName: imageName,
    );
  }

  /// Create an image-only message
  factory ImageMessage.imageOnly({
    required Uint8List imageBytes,
    bool isUser = false,
    DateTime? timestamp,
    String? imageName,
    String text = '',
  }) {
    return ImageMessage(
      text: text,
      imageBytes: imageBytes,
      isUser: isUser,
      timestamp: timestamp,
      imageName: imageName,
    );
  }

  /// Create a copy with modified properties
  ImageMessage copyWith({
    String? text,
    bool? isUser,
    Uint8List? imageBytes,
    DateTime? timestamp,
    String? imageName,
  }) {
    return ImageMessage(
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      imageBytes: imageBytes ?? this.imageBytes,
      timestamp: timestamp ?? this.timestamp,
      imageName: imageName ?? this.imageName,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'imageName': imageName,
      'hasImage': hasImage,
    };
  }

  /// Create from JSON
  factory ImageMessage.fromJson(Map<String, dynamic> json) {
    return ImageMessage(
      text: json['text'] as String,
      isUser: json['isUser'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      imageName: json['imageName'] as String?,
      // Note: imageBytes are not serialized to JSON for security/size reasons
    );
  }

  @override
  String toString() {
    return 'ImageMessage(text: $text, isUser: $isUser, hasImage: $hasImage, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageMessage &&
        other.text == text &&
        other.isUser == isUser &&
        other.timestamp == timestamp &&
        other.imageName == imageName &&
        _listEquals(other.imageBytes, imageBytes);
  }

  @override
  int get hashCode {
    return Object.hash(
      text,
      isUser,
      timestamp,
      imageName,
      imageBytes?.length,
    );
  }

  /// Helper method to compare Uint8List
  static bool _listEquals(Uint8List? a, Uint8List? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}
