import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';

/// Audio translation page with recording and file upload support
class AudioTranslationPage extends ConsumerStatefulWidget {
  const AudioTranslationPage({super.key});

  @override
  ConsumerState<AudioTranslationPage> createState() => _AudioTranslationPageState();
}

class _AudioTranslationPageState extends ConsumerState<AudioTranslationPage>
    with AutomaticKeepAliveClientMixin {
  bool _isRecording = false;
  bool _hasAudioFile = false;
  bool _isProcessing = false;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Recording Section
          _buildRecordingSection(theme),
          
          const SizedBox(height: 16),
          
          // File Upload Section
          _buildFileUploadSection(theme),
          
          const SizedBox(height: 16),
          
          // Coming Soon Notice
          _buildComingSoonNotice(theme),
        ],
      ),
    );
  }

  Widget _buildRecordingSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              _isRecording ? Icons.mic : Icons.mic_none,
              size: 64,
              color: _isRecording 
                  ? AppTheme.errorColor 
                  : AppTheme.primaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _isRecording ? 'Gravando...' : 'Gravação de Áudio',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: _isRecording ? AppTheme.errorColor : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _isRecording 
                  ? 'Toque para parar a gravação'
                  : 'Toque para começar a gravar',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: 120,
              height: 120,
              child: ElevatedButton(
                onPressed: _toggleRecording,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isRecording 
                      ? AppTheme.errorColor 
                      : AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  shape: const CircleBorder(),
                  elevation: _isRecording ? 8 : 4,
                ),
                child: Icon(
                  _isRecording ? Icons.stop : Icons.mic,
                  size: 48,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileUploadSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.upload_file,
              size: 48,
              color: AppTheme.primaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Upload de Arquivo de Áudio',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Suporte para MP3, WAV, M4A, FLAC',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _uploadAudioFile,
              icon: const Icon(Icons.folder_open),
              label: const Text('Selecionar Arquivo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoonNotice(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.infoColor.withOpacity(0.1),
            AppTheme.primaryColor.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.construction,
            size: 48,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Funcionalidade em Desenvolvimento',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'A tradução de áudio com Gemma 3N está sendo implementada. Em breve você poderá:',
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFeatureItem('🎙️ Gravar áudio em tempo real'),
              _buildFeatureItem('📁 Fazer upload de arquivos de áudio'),
              _buildFeatureItem('🔤 Transcrever fala automaticamente'),
              _buildFeatureItem('🌍 Traduzir para 140+ idiomas'),
              _buildFeatureItem('👥 Detectar múltiplos falantes'),
              _buildFeatureItem('🔇 Redução de ruído inteligente'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }

  void _toggleRecording() {
    setState(() {
      _isRecording = !_isRecording;
    });
    
    if (_isRecording) {
      _showInfoSnackBar('Gravação iniciada (simulação)');
    } else {
      _showInfoSnackBar('Gravação parada (simulação)');
    }
  }

  void _uploadAudioFile() {
    _showInfoSnackBar('Seleção de arquivo em desenvolvimento');
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
