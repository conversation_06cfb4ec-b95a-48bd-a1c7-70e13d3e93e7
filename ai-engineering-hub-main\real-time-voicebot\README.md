# [Realtime Voice Bot](https://blog.dailydoseofds.com/p/assemblyai-voicebot)

This application provides a real-time, conversational travel guide for tourists visiting London, UK. Powered by AssemblyAI, ElevenLabs, and OpenAI, it transcribes your speech, generates AI responses, and plays them back as audio. It serves as a friendly assistant to help plan your trip, providing concise and conversational guidance.

## Demo Video

Click below to watch the demo video of the AI Assistant in action:

[Watch the video](Voicebot%20video.MP4)

## Features
- Real-time speech-to-text transcription using AssemblyAI.
- AI-generated responses using OpenAI's GPT-3.5-Turbo.
- Voice synthesis and playback with ElevenLabs.

## API Key Setup
Before running the application, you need API keys for the following services:

- [Get the API key for AssemblyAI here →](https://www.assemblyai.com/dashboard/signup)
- [Get the API key for OpenAI here →](https://platform.openai.com/api-keys)
- [Get the API key for ElevenLabs here →](https://elevenlabs.io/app/sign-in)

Update the API keys in the code by replacing the placeholders in the `AI_Assistant` class.

## Run the application

```bash
python app.py
```
---

## 📬 Stay Updated with Our Newsletter!
**Get a FREE Data Science eBook** 📖 with 150+ essential lessons in Data Science when you subscribe to our newsletter! Stay in the loop with the latest tutorials, insights, and exclusive resources. [Subscribe now!](https://join.dailydoseofds.com)

[![Daily Dose of Data Science Newsletter](https://github.com/patchy631/ai-engineering/blob/main/resources/join_ddods.png)](https://join.dailydoseofds.com)

## Contribution
Contributions are welcome! Please fork the repository and submit a pull request with your improvements.


