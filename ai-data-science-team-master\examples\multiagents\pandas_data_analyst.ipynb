{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pandas Data Analyst Agent\n", "\n", "### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multi-Agents for Pandas Data Analysis\n", "\n", "**The Pandas Data Analysis agent** we build in this tutorial is a multi-agent that can perform Pandas data wrangling and analysis on a data set and optionally visualize the results. The agent combines 2 subagents:\n", "\n", "1. **Data Wrangling Agent:** Handles data wrangling, transformations, and analysis using Pandas.\n", "2. **Data Visualization Agent:** Visualize the results if a user requests a plot.\n", "\n", "This tutorial will show you how to perform Data Analysis using Pandas and data visualizations, **all in one agent.** \n", "\n", "### Data Set\n", "\n", "The `churn_data.csv` data set contains customer data for a telecommunications company. "]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "bat"}}, "source": ["# Table of Contents\n", "\n", "1. [Pandas Data Analyst Agent](#pandas-data-analyst-agent)\n", "2. [Multi-Agents for Pandas Data Analysis](#multi-agents-for-pandas-data-analysis)\n", "3. [Build Pandas Data Analysis Agents](#build-pandas-data-analysis-agents)\n", "    1. [Load Libraries](#load-libraries)\n", "    2. [Setup AI and Logging](#setup-ai-and-logging)\n", "    3. [Read a Data Set](#read-a-data-set)\n", "    4. [Create The Agent](#create-the-agent)\n", "    5. [Viewing Subagents With xray](#viewing-subagents-with-xray)\n", "    6. [Run the Agent](#run-the-agent)\n", "        1. [Example 1: What are the first 5 rows of the data?](#example-1-what-are-the-first-5-rows-of-the-data)\n", "        2. [Example 2: Plot a boxplot with violin between monthly payment and churn](#example-2-plot-a-boxplot-with-violin-between-monthly-payment-and-churn)\n", "    7. [Response](#response)\n", "    8. [Pandas Data Frame](#pandas-data-frame)\n", "    9. [Pandas Code](#pandas-code)\n", "    10. [Workflow Summary](#workflow-summary)\n", "4. [Want To Become A Full-Stack Generative AI Data Scientist?](#want-to-become-a-full-stack-generative-ai-data-scientist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# * Libraries\n", "\n", "from langchain_openai import ChatOpenAI\n", "import pandas as pd\n", "import os\n", "import yaml\n", "from pprint import pprint\n", "\n", "from ai_data_science_team import PandasDataAnalyst, DataWranglingAgent, DataVisualizationAgent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup AI and Logging\n", "\n", "This section of code sets up the LLM inputs and the logging information. Logging is used to store AI-generated code and files during the AI Data Science Teams processing of files. \n", "\n", "*Important Note:* This example uses OpenAI's API. But any LLM can be used such as Anthropic or local LLMs with Ollama."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x7f9760038820>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x7f97600390c0>, root_client=<openai.OpenAI object at 0x7f9760039810>, root_async_client=<openai.AsyncOpenAI object at 0x7f9760038130>, model_name='gpt-4o-mini', model_kwargs={}, openai_api_key=SecretStr('**********'))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# * Setup\n", "\n", "MODEL    = \"gpt-4o-mini\"\n", "LOG      = False\n", "LOG_PATH = os.path.join(os.getcwd(), \"logs/\")\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = yaml.safe_load(open('../credentials.yml'))['openai']\n", "\n", "llm = ChatOpenAI(model = MODEL)\n", "\n", "llm\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read a Data Set\n", "\n", "Next, let's read a dataset. The `churn_data.csv` data set contains customer data for a telecommunications company."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "customerID", "rawType": "object", "type": "string"}, {"name": "gender", "rawType": "object", "type": "string"}, {"name": "SeniorCitizen", "rawType": "int64", "type": "integer"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "tenure", "rawType": "int64", "type": "integer"}, {"name": "PhoneService", "rawType": "object", "type": "string"}, {"name": "MultipleLines", "rawType": "object", "type": "string"}, {"name": "InternetService", "rawType": "object", "type": "string"}, {"name": "OnlineSecurity", "rawType": "object", "type": "string"}, {"name": "OnlineBackup", "rawType": "object", "type": "string"}, {"name": "DeviceProtection", "rawType": "object", "type": "string"}, {"name": "TechSupport", "rawType": "object", "type": "string"}, {"name": "StreamingTV", "rawType": "object", "type": "string"}, {"name": "StreamingMovies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "PaperlessBilling", "rawType": "object", "type": "string"}, {"name": "PaymentMethod", "rawType": "object", "type": "string"}, {"name": "MonthlyCharges", "rawType": "float64", "type": "float"}, {"name": "TotalCharges", "rawType": "object", "type": "string"}, {"name": "Churn", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "cc712650-dac1-446f-977b-eb232a14a0b5", "rows": [["0", "7590-VHVEG", "Female", "0", "Yes", "No", "1", "No", "No phone service", "DSL", "No", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "29.85", "29.85", "No"], ["1", "5575-GNVDE", "Male", "0", "No", "No", "34", "Yes", "No", "DSL", "Yes", "No", "Yes", "No", "No", "No", "One year", "No", "Mailed check", "56.95", "1889.5", "No"], ["2", "3668-QPYBK", "Male", "0", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed check", "53.85", "108.15", "Yes"], ["3", "7795-CFOCW", "Male", "0", "No", "No", "45", "No", "No phone service", "DSL", "Yes", "No", "Yes", "Yes", "No", "No", "One year", "No", "Bank transfer (automatic)", "42.3", "1840.75", "No"], ["4", "9237-HQITU", "Female", "0", "No", "No", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "70.7", "151.65", "Yes"], ["5", "9305-CDSKC", "Female", "0", "No", "No", "8", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "99.65", "820.5", "Yes"], ["6", "1452-KIOVK", "Male", "0", "No", "Yes", "22", "Yes", "Yes", "Fiber optic", "No", "Yes", "No", "No", "Yes", "No", "Month-to-month", "Yes", "Credit card (automatic)", "89.1", "1949.4", "No"], ["7", "6713-OKOMC", "Female", "0", "No", "No", "10", "No", "No phone service", "DSL", "Yes", "No", "No", "No", "No", "No", "Month-to-month", "No", "Mailed check", "29.75", "301.9", "No"], ["8", "7892-POOKP", "Female", "0", "Yes", "No", "28", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "104.8", "3046.05", "Yes"], ["9", "6388-TABGU", "Male", "0", "No", "Yes", "62", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "One year", "No", "Bank transfer (automatic)", "56.15", "3487.95", "No"], ["10", "9763-GRSKD", "Male", "0", "Yes", "Yes", "13", "Yes", "No", "DSL", "Yes", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed check", "49.95", "587.45", "No"], ["11", "7469-LKBCI", "Male", "0", "No", "No", "16", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Two year", "No", "Credit card (automatic)", "18.95", "326.8", "No"], ["12", "8091-TTVAX", "Male", "0", "Yes", "No", "58", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "One year", "No", "Credit card (automatic)", "100.35", "5681.1", "No"], ["13", "0280-XJGEX", "Male", "0", "No", "No", "49", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank transfer (automatic)", "103.7", "5036.3", "Yes"], ["14", "5129-JLPIS", "Male", "0", "No", "No", "25", "Yes", "No", "Fiber optic", "Yes", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "105.5", "2686.05", "No"], ["15", "3655-SNQYZ", "Female", "0", "Yes", "Yes", "69", "Yes", "Yes", "Fiber optic", "Yes", "Yes", "Yes", "Yes", "Yes", "Yes", "Two year", "No", "Credit card (automatic)", "113.25", "7895.15", "No"], ["16", "8191-XWSZG", "Female", "0", "No", "No", "52", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "One year", "No", "Mailed check", "20.65", "1022.95", "No"], ["17", "9959-WOFKT", "Male", "0", "No", "Yes", "71", "Yes", "Yes", "Fiber optic", "Yes", "No", "Yes", "No", "Yes", "Yes", "Two year", "No", "Bank transfer (automatic)", "106.7", "7382.25", "No"], ["18", "4190-MFLUW", "Female", "0", "Yes", "Yes", "10", "Yes", "No", "DSL", "No", "No", "Yes", "Yes", "No", "No", "Month-to-month", "No", "Credit card (automatic)", "55.2", "528.35", "Yes"], ["19", "4183-MYFRB", "Female", "0", "No", "No", "21", "Yes", "No", "Fiber optic", "No", "Yes", "Yes", "No", "No", "Yes", "Month-to-month", "Yes", "Electronic check", "90.05", "1862.9", "No"], ["20", "8779-QRDMV", "Male", "1", "No", "No", "1", "No", "No phone service", "DSL", "No", "No", "Yes", "No", "No", "Yes", "Month-to-month", "Yes", "Electronic check", "39.65", "39.65", "Yes"], ["21", "1680-VDCWW", "Male", "0", "Yes", "No", "12", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "One year", "No", "Bank transfer (automatic)", "19.8", "202.25", "No"], ["22", "1066-JKSGK", "Male", "0", "No", "No", "1", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Month-to-month", "No", "Mailed check", "20.15", "20.15", "Yes"], ["23", "3638-WEABW", "Female", "0", "Yes", "No", "58", "Yes", "Yes", "DSL", "No", "Yes", "No", "Yes", "No", "No", "Two year", "Yes", "Credit card (automatic)", "59.9", "3505.1", "No"], ["24", "6322-HRPFA", "Male", "0", "Yes", "Yes", "49", "Yes", "No", "DSL", "Yes", "Yes", "No", "Yes", "No", "No", "Month-to-month", "No", "Credit card (automatic)", "59.6", "2970.3", "No"], ["25", "6865-JZNKO", "Female", "0", "No", "No", "30", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Bank transfer (automatic)", "55.3", "1530.6", "No"], ["26", "6467-CHFZW", "Male", "0", "Yes", "Yes", "47", "Yes", "Yes", "Fiber optic", "No", "Yes", "No", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "99.35", "4749.15", "Yes"], ["27", "8665-UTDHZ", "Male", "0", "Yes", "Yes", "1", "No", "No phone service", "DSL", "No", "Yes", "No", "No", "No", "No", "Month-to-month", "No", "Electronic check", "30.2", "30.2", "Yes"], ["28", "5248-YGIJN", "Male", "0", "Yes", "No", "72", "Yes", "Yes", "DSL", "Yes", "Yes", "Yes", "Yes", "Yes", "Yes", "Two year", "Yes", "Credit card (automatic)", "90.25", "6369.45", "No"], ["29", "8773-HHUOZ", "Female", "0", "No", "Yes", "17", "Yes", "No", "DSL", "No", "No", "No", "No", "Yes", "Yes", "Month-to-month", "Yes", "Mailed check", "64.7", "1093.1", "Yes"], ["30", "3841-NFECX", "Female", "1", "Yes", "No", "71", "Yes", "Yes", "Fiber optic", "Yes", "Yes", "Yes", "Yes", "No", "No", "Two year", "Yes", "Credit card (automatic)", "96.35", "6766.95", "No"], ["31", "4929-XIHVW", "Male", "1", "Yes", "No", "2", "Yes", "No", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Credit card (automatic)", "95.5", "181.65", "No"], ["32", "6827-IEAUQ", "Female", "0", "Yes", "Yes", "27", "Yes", "No", "DSL", "Yes", "Yes", "Yes", "Yes", "No", "No", "One year", "No", "Mailed check", "66.15", "1874.45", "No"], ["33", "7310-EGVHZ", "Male", "0", "No", "No", "1", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Month-to-month", "No", "Bank transfer (automatic)", "20.2", "20.2", "No"], ["34", "3413-BMNZE", "Male", "1", "No", "No", "1", "Yes", "No", "DSL", "No", "No", "No", "No", "No", "No", "Month-to-month", "No", "Bank transfer (automatic)", "45.25", "45.25", "No"], ["35", "6234-RAAPL", "Female", "0", "Yes", "Yes", "72", "Yes", "Yes", "Fiber optic", "Yes", "Yes", "No", "Yes", "Yes", "No", "Two year", "No", "Bank transfer (automatic)", "99.9", "7251.7", "No"], ["36", "6047-YHPVI", "Male", "0", "No", "No", "5", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "69.7", "316.9", "Yes"], ["37", "6572-ADK<PERSON>", "Female", "0", "No", "No", "46", "Yes", "No", "Fiber optic", "No", "No", "Yes", "No", "No", "No", "Month-to-month", "Yes", "Credit card (automatic)", "74.8", "3548.3", "No"], ["38", "5380-WJKOV", "Male", "0", "No", "No", "34", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "106.35", "3549.25", "Yes"], ["39", "8168-UQWWF", "Female", "0", "No", "No", "11", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank transfer (automatic)", "97.85", "1105.4", "Yes"], ["40", "8865-TNMNX", "Male", "0", "Yes", "Yes", "10", "Yes", "No", "DSL", "No", "Yes", "No", "No", "No", "No", "One year", "No", "Mailed check", "49.55", "475.7", "No"], ["41", "9489-<PERSON><PERSON><PERSON>", "Female", "0", "Yes", "Yes", "70", "Yes", "Yes", "DSL", "Yes", "Yes", "No", "No", "Yes", "No", "Two year", "Yes", "Credit card (automatic)", "69.2", "4872.35", "No"], ["42", "9867-JCZSP", "Female", "0", "Yes", "Yes", "17", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "One year", "No", "Mailed check", "20.75", "418.25", "No"], ["43", "4671-VJLCL", "Female", "0", "No", "No", "63", "Yes", "Yes", "DSL", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "Two year", "Yes", "Credit card (automatic)", "79.85", "4861.45", "No"], ["44", "4080-IIARD", "Female", "0", "Yes", "No", "13", "Yes", "Yes", "DSL", "Yes", "Yes", "No", "Yes", "Yes", "No", "Month-to-month", "Yes", "Electronic check", "76.2", "981.45", "No"], ["45", "3714-NTNFO", "Female", "0", "No", "No", "49", "Yes", "Yes", "Fiber optic", "No", "No", "No", "No", "No", "Yes", "Month-to-month", "Yes", "Electronic check", "84.5", "3906.7", "No"], ["46", "5948-UJZLF", "Male", "0", "No", "No", "2", "Yes", "No", "DSL", "No", "Yes", "No", "No", "No", "No", "Month-to-month", "No", "Mailed check", "49.25", "97", "No"], ["47", "7760-OYPDY", "Female", "0", "No", "No", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "Yes", "No", "Month-to-month", "Yes", "Electronic check", "80.65", "144.15", "Yes"], ["48", "7639-LIAYI", "Male", "0", "No", "No", "52", "Yes", "Yes", "DSL", "Yes", "No", "No", "Yes", "Yes", "Yes", "Two year", "Yes", "Credit card (automatic)", "79.75", "4217.8", "No"], ["49", "2954-PIB<PERSON>", "Female", "0", "Yes", "Yes", "69", "Yes", "Yes", "DSL", "Yes", "No", "Yes", "Yes", "No", "No", "Two year", "Yes", "Credit card (automatic)", "64.15", "4254.1", "No"]], "shape": {"columns": 21, "rows": 7043}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customerID</th>\n", "      <th>gender</th>\n", "      <th>SeniorCitizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>tenure</th>\n", "      <th>PhoneService</th>\n", "      <th>MultipleLines</th>\n", "      <th>InternetService</th>\n", "      <th>OnlineSecurity</th>\n", "      <th>...</th>\n", "      <th>DeviceProtection</th>\n", "      <th>TechSupport</th>\n", "      <th>StreamingTV</th>\n", "      <th>StreamingMovies</th>\n", "      <th>Contract</th>\n", "      <th>PaperlessBilling</th>\n", "      <th>PaymentMethod</th>\n", "      <th>MonthlyCharges</th>\n", "      <th>TotalCharges</th>\n", "      <th><PERSON>rn</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7590-VHVEG</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.85</td>\n", "      <td>29.85</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5575-GNVDE</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>34</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>56.95</td>\n", "      <td>1889.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3668-QPYBK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7795-CFOCW</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>45</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>42.30</td>\n", "      <td>1840.75</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9237-HQITU</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7038</th>\n", "      <td>6840-RESVB</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>24</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>84.80</td>\n", "      <td>1990.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7039</th>\n", "      <td>2234-XADUH</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>72</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>103.20</td>\n", "      <td>7362.9</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7040</th>\n", "      <td>4801-JZAZL</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>11</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.60</td>\n", "      <td>346.45</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7041</th>\n", "      <td>8361-LTMKD</td>\n", "      <td>Male</td>\n", "      <td>1</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>4</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>74.40</td>\n", "      <td>306.6</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7042</th>\n", "      <td>3186-AJIEK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>66</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Two year</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>105.65</td>\n", "      <td>6844.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7043 rows × 21 columns</p>\n", "</div>"], "text/plain": ["      customerID  gender  SeniorCitizen Partner Dependents  tenure  \\\n", "0     7590-VHVEG  Female              0     Yes         No       1   \n", "1     5575-GNVDE    Male              0      No         No      34   \n", "2     3668-QPYBK    Male              0      No         No       2   \n", "3     7795-CFOCW    Male              0      No         No      45   \n", "4     9237-HQITU  Female              0      No         No       2   \n", "...          ...     ...            ...     ...        ...     ...   \n", "7038  6840-RESVB    Male              0     Yes        Yes      24   \n", "7039  2234-XADUH  Female              0     Yes        Yes      72   \n", "7040  4801-JZAZL  Female              0     Yes        Yes      11   \n", "7041  8361-LTMKD    Male              1     Yes         No       4   \n", "7042  3186-AJIEK    Male              0      No         No      66   \n", "\n", "     PhoneService     MultipleLines InternetService OnlineSecurity  ...  \\\n", "0              No  No phone service             DSL             No  ...   \n", "1             Yes                No             DSL            Yes  ...   \n", "2             Yes                No             DSL            Yes  ...   \n", "3              No  No phone service             DSL            Yes  ...   \n", "4             Yes                No     Fiber optic             No  ...   \n", "...           ...               ...             ...            ...  ...   \n", "7038          Yes               Yes             DSL            Yes  ...   \n", "7039          Yes               Yes     Fiber optic             No  ...   \n", "7040           No  No phone service             DSL            Yes  ...   \n", "7041          Yes               Yes     Fiber optic             No  ...   \n", "7042          Yes                No     Fiber optic            Yes  ...   \n", "\n", "     DeviceProtection TechSupport StreamingTV StreamingMovies        Contract  \\\n", "0                  No          No          No              No  Month-to-month   \n", "1                 Yes          No          No              No        One year   \n", "2                  No          No          No              No  Month-to-month   \n", "3                 Yes         Yes          No              No        One year   \n", "4                  No          No          No              No  Month-to-month   \n", "...               ...         ...         ...             ...             ...   \n", "7038              Yes         Yes         Yes             Yes        One year   \n", "7039              Yes          No         Yes             Yes        One year   \n", "7040               No          No          No              No  Month-to-month   \n", "7041               No          No          No              No  Month-to-month   \n", "7042              Yes         Yes         Yes             Yes        Two year   \n", "\n", "     PaperlessBilling              PaymentMethod MonthlyCharges  TotalCharges  \\\n", "0                 Yes           Electronic check          29.85         29.85   \n", "1                  No               Mailed check          56.95        1889.5   \n", "2                 Yes               Mailed check          53.85        108.15   \n", "3                  No  Bank transfer (automatic)          42.30       1840.75   \n", "4                 Yes           Electronic check          70.70        151.65   \n", "...               ...                        ...            ...           ...   \n", "7038              Yes               Mailed check          84.80        1990.5   \n", "7039              Yes    Credit card (automatic)         103.20        7362.9   \n", "7040              Yes           Electronic check          29.60        346.45   \n", "7041              Yes               Mailed check          74.40         306.6   \n", "7042              Yes  Bank transfer (automatic)         105.65        6844.5   \n", "\n", "     Churn  \n", "0       No  \n", "1       No  \n", "2      Yes  \n", "3       No  \n", "4      Yes  \n", "...    ...  \n", "7038    No  \n", "7039    No  \n", "7040    No  \n", "7041   Yes  \n", "7042    No  \n", "\n", "[7043 rows x 21 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# * Data\n", "\n", "df = pd.read_csv(\"data/churn_data.csv\")\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create The Agent\n", "\n", "Run this code to create the agent with `PandasDataAnalyst()`. This will create a multi-agent that can perform Pandas Data Analysis on a dataset and optionally visualize the results. This agent combines 2 subagents:\n", "\n", "#### DataWranglingAgent\n", "\n", "The `DataWranglingAgent` is a subagent that handles data wrangling, transformations, and analysis using Pandas.\n", "\n", "#### DataVisualizationAgent\n", "\n", "The `DataVisualizationAgent` is a subagent that visualizes the results if a user requests a plot.\n", "\n", "Run this code to create the multi-agent:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<ai_data_science_team.multiagents.pandas_data_analyst.PandasDataAnalyst object at 0x7f97d0426aa0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pandas_data_analyst = PandasDataAnalyst(\n", "    model = llm,\n", "    data_wrangling_agent = DataWranglingAgent(\n", "        model = llm,\n", "        log = LOG,\n", "        log_path = LOG_PATH,\n", "        bypass_recommended_steps=True,\n", "    ),\n", "    data_visualization_agent = DataVisualizationAgent(\n", "        model = llm,\n", "        n_samples = 10,\n", "        log = LOG,\n", "        log_path = LOG_PATH,\n", "    ),\n", ")\n", "\n", "pandas_data_analyst"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Viewing Subagents With xray\n", "\n", "Keep in mind that this agent is actually a multi-agent that combines 2 subagents. We can view the subagents by running the following code:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pandas_data_analyst.show(xray=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run the Agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The main inputs to the SQL DB Agent are:\n", "\n", "- **user_instructions**: What actions you'd like to take on the data. \n", "- **data_raw**: The data set to perform the actions on.\n", "\n", "Let's start with a simple question that a user might want to know about the database:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example 1: What are the first 5 rows of the data?"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---PANDAS DATA ANALYST---\n", "*************************\n", "---PREPROCESS ROUTER---\n", "---DATA WRANGLING AGENT----\n", "    * CREATE DATA WRANGLER CODE\n", "    * EXECUTING AGENT CODE\n", "    * REPORT AGENT OUTPUTS\n", "---ROUTER: CHART OR TABLE---\n", "---ROUTE PRINTER---\n", "    Route: table\n", "---END---\n"]}], "source": ["\n", "pandas_data_analyst.invoke_agent(\n", "    user_instructions = \"What are the first 5 rows of the data?\",\n", "    data_raw=df,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Response\n", "\n", "The response produced contains everything we need to understand the data cleaning decisions made and get the cleaned dataset. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["['messages',\n", " 'user_instructions',\n", " 'user_instructions_data_wrangling',\n", " 'user_instructions_data_visualization',\n", " 'routing_preprocessor_decision',\n", " 'data_raw',\n", " 'data_wrangled',\n", " 'data_wrangler_function',\n", " 'data_visualization_function',\n", " 'plotly_graph',\n", " 'plotly_error',\n", " 'max_retries',\n", " 'retry_count']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pandas_data_analyst.get_state_keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Not all fields will be filled if the agent did not need to use them.\n", "\n", "- **data_wrangler_function**: The Python function that was generated by the Data Wrangling Agent.\n", "- **data_wrangled**: The Pandas data frame that was generated by the agent.\n", "- **data_visualization_function**: The Python function that was generated by the Data Visualization Agent.\n", "- **plotly_graph**: The Plotly graph that was generated by the Data Visualization Agent."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Pandas Data Frame"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "customerID", "rawType": "object", "type": "string"}, {"name": "gender", "rawType": "object", "type": "string"}, {"name": "SeniorCitizen", "rawType": "int64", "type": "integer"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "tenure", "rawType": "int64", "type": "integer"}, {"name": "PhoneService", "rawType": "object", "type": "string"}, {"name": "MultipleLines", "rawType": "object", "type": "string"}, {"name": "InternetService", "rawType": "object", "type": "string"}, {"name": "OnlineSecurity", "rawType": "object", "type": "string"}, {"name": "OnlineBackup", "rawType": "object", "type": "string"}, {"name": "DeviceProtection", "rawType": "object", "type": "string"}, {"name": "TechSupport", "rawType": "object", "type": "string"}, {"name": "StreamingTV", "rawType": "object", "type": "string"}, {"name": "StreamingMovies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "PaperlessBilling", "rawType": "object", "type": "string"}, {"name": "PaymentMethod", "rawType": "object", "type": "string"}, {"name": "MonthlyCharges", "rawType": "float64", "type": "float"}, {"name": "TotalCharges", "rawType": "object", "type": "string"}, {"name": "Churn", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "33de6a63-e7ac-489f-aed7-b518c24d8e00", "rows": [["0", "7590-VHVEG", "Female", "0", "Yes", "No", "1", "No", "No phone service", "DSL", "No", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "29.85", "29.85", "No"], ["1", "5575-GNVDE", "Male", "0", "No", "No", "34", "Yes", "No", "DSL", "Yes", "No", "Yes", "No", "No", "No", "One year", "No", "Mailed check", "56.95", "1889.5", "No"], ["2", "3668-QPYBK", "Male", "0", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed check", "53.85", "108.15", "Yes"], ["3", "7795-CFOCW", "Male", "0", "No", "No", "45", "No", "No phone service", "DSL", "Yes", "No", "Yes", "Yes", "No", "No", "One year", "No", "Bank transfer (automatic)", "42.3", "1840.75", "No"], ["4", "9237-HQITU", "Female", "0", "No", "No", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "70.7", "151.65", "Yes"]], "shape": {"columns": 21, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customerID</th>\n", "      <th>gender</th>\n", "      <th>SeniorCitizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>tenure</th>\n", "      <th>PhoneService</th>\n", "      <th>MultipleLines</th>\n", "      <th>InternetService</th>\n", "      <th>OnlineSecurity</th>\n", "      <th>...</th>\n", "      <th>DeviceProtection</th>\n", "      <th>TechSupport</th>\n", "      <th>StreamingTV</th>\n", "      <th>StreamingMovies</th>\n", "      <th>Contract</th>\n", "      <th>PaperlessBilling</th>\n", "      <th>PaymentMethod</th>\n", "      <th>MonthlyCharges</th>\n", "      <th>TotalCharges</th>\n", "      <th><PERSON>rn</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7590-VHVEG</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.85</td>\n", "      <td>29.85</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5575-GNVDE</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>34</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>56.95</td>\n", "      <td>1889.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3668-QPYBK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7795-CFOCW</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>45</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>42.30</td>\n", "      <td>1840.75</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9237-HQITU</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   customerID  gender  SeniorCitizen Partner Dependents  tenure PhoneService  \\\n", "0  7590-VHVEG  Female              0     Yes         No       1           No   \n", "1  5575-GNVDE    Male              0      No         No      34          Yes   \n", "2  3668-QPYBK    Male              0      No         No       2          Yes   \n", "3  7795-CFOCW    Male              0      No         No      45           No   \n", "4  9237-HQITU  Female              0      No         No       2          Yes   \n", "\n", "      MultipleLines InternetService OnlineSecurity  ... DeviceProtection  \\\n", "0  No phone service             DSL             No  ...               No   \n", "1                No             DSL            Yes  ...              Yes   \n", "2                No             DSL            Yes  ...               No   \n", "3  No phone service             DSL            Yes  ...              Yes   \n", "4                No     Fiber optic             No  ...               No   \n", "\n", "  TechSupport StreamingTV StreamingMovies        Contract PaperlessBilling  \\\n", "0          No          No              No  Month-to-month              Yes   \n", "1          No          No              No        One year               No   \n", "2          No          No              No  Month-to-month              Yes   \n", "3         Yes          No              No        One year               No   \n", "4          No          No              No  Month-to-month              Yes   \n", "\n", "               PaymentMethod MonthlyCharges  TotalCharges Churn  \n", "0           Electronic check          29.85         29.85    No  \n", "1               Mailed check          56.95        1889.5    No  \n", "2               Mailed check          53.85        108.15   Yes  \n", "3  Bank transfer (automatic)          42.30       1840.75    No  \n", "4           Electronic check          70.70        151.65   Yes  \n", "\n", "[5 rows x 21 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pandas_data_analyst.get_data_wrangled()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Pandas Code"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/markdown": ["```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: data_wrangling_agent\n", "# Time Created: 2025-02-24 15:05:10\n", "\n", "def data_wrangler(data_list):\n", "    import pandas as pd\n", "    import numpy as np\n", "    '''\n", "    Wrangle the data provided in data.\n", "    \n", "    data_list: A list of one or more pandas data frames containing the raw data to be wrangled.\n", "    '''\n", "\n", "\n", "    # Check if data_list is not a list, convert it into a list\n", "    if not isinstance(data_list, list):\n", "        data_list = [data_list]\n", "\n", "    # Assuming the first dataset is the one we want to wrangle\n", "    main_data = data_list[0]\n", "\n", "    # Convert 'TotalCharges' to numeric, coercing errors to NaN\n", "    main_data['TotalCharges'] = pd.to_numeric(main_data['TotalCharges'], errors='coerce')\n", "\n", "    # Display the first 5 rows of the dataset as per user instruction\n", "    data_wrangled = main_data.head(5)\n", "\n", "    # Return the first 5 rows of the DataFrame\n", "    return data_wrangled\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pandas_data_analyst.get_data_wrangler_function(markdown=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example 2: Plot a boxplot with violin between monthly payment and churn."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---PANDAS DATA ANALYST---\n", "*************************\n", "---PREPROCESS ROUTER---\n", "---DATA WRANGLING AGENT----\n", "    * CREATE DATA WRANGLER CODE\n", "    * EXECUTING AGENT CODE\n", "    * REPORT AGENT OUTPUTS\n", "---ROUTER: CHART OR TABLE---\n", "---DATA VISUALIZATION AGENT----\n", "    * CREATE CHART GENERATOR INSTRUCTIONS\n", "    * CREATE DATA VISUALIZATION CODE\n", "    * EXECUTING AGENT CODE\n", "    * REPORT AGENT OUTPUTS\n", "---ROUTE PRINTER---\n", "    Route: chart\n", "---END---\n"]}], "source": ["pandas_data_analyst.invoke_agent(\n", "    user_instructions = \"Plot a boxplot with violin between monthly payment and churn.\",\n", "    data_raw=df,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"box": {"visible": true}, "hoverinfo": "y", "line": {"color": "#3381ff"}, "name": "Not Churned", "points": "all", "type": "violin", "y": [29.85, 56.95, 42.3, 89.1, 29.75, 56.15, 49.95, 18.95, 100.35, 105.5, 113.25, 20.65, 106.7, 90.05, 19.8, 59.9, 59.6, 55.3, 90.25, 96.35, 95.5, 66.15, 20.2, 45.25, 99.9, 74.8, 49.55, 69.2, 20.75, 79.85, 76.2, 84.5, 49.25, 79.75, 64.15, 90.25, 69.5, 74.85, 99.65, 108.45, 24.95, 107.5, 100.5, 89.9, 42.1, 54.4, 75.3, 78.9, 79.2, 20.15, 79.85, 49.05, 20.4, 111.6, 24.25, 64.5, 110.5, 55.65, 54.65, 74.75, 25.9, 50.55, 103.8, 20.15, 99.3, 62.15, 20.65, 19.95, 33.75, 82.05, 74.7, 84, 111.05, 100.9, 66.85, 21, 20.2, 19.45, 95, 45.55, 24.3, 104.15, 30.15, 94.35, 19.4, 57.95, 91.65, 54.6, 89.85, 31.05, 20.65, 85.2, 99.8, 20.7, 50.7, 20.85, 88.95, 23.55, 19.75, 56.45, 85.95, 50.55, 25.7, 75, 20.2, 19.6, 88.05, 101.05, 84.3, 23.95, 99.05, 19.6, 64.5, 69.5, 68.55, 108.15, 86.1, 19.7, 80.9, 84.15, 20.15, 64.25, 25.7, 56, 69.7, 73.9, 20.6, 19.9, 89.05, 20.4, 84.25, 81.95, 20.55, 24.7, 105.35, 20.55, 45.3, 74.75, 51.8, 30.4, 19.65, 56.6, 71.9, 19.75, 109.7, 19.3, 96.55, 24.1, 111.35, 112.25, 20.75, 101.9, 80.05, 105.55, 78.3, 68.85, 79.95, 55.45, 102.45, 25.25, 19.75, 20, 86.8, 58.75, 45.25, 56.6, 84.2, 80, 24.75, 20.2, 19.35, 50.6, 81.15, 89.9, 85.3, 108, 93.5, 20.25, 25.15, 29.6, 73.15, 19.75, 86.6, 109.2, 74.7, 94.4, 54.8, 75.35, 65, 99, 93.5, 83.7, 19.85, 59.55, 115.1, 114.35, 45, 89.85, 49.85, 113.3, 88.1, 24.9, 105, 24.25, 59.75, 107.05, 64.95, 55, 50.55, 55.15, 51.2, 25.4, 54.45, 76, 44.35, 61.2, 86.8, 89.35, 19.7, 20.25, 76.05, 100.8, 74.55, 64.9, 90.4, 60.3, 81.85, 24.8, 75.55, 101.15, 78.75, 19.25, 89.05, 115.05, 69.35, 110.05, 19.9, 91.5, 60, 44.8, 48.6, 60.05, 102.7, 82.9, 35.9, 82.65, 19.85, 19.2, 94.9, 88.15, 94.8, 54.65, 85.75, 67.45, 20.5, 20.25, 72.1, 19.45, 44.95, 97, 62.8, 44.6, 89.15, 54.1, 105.25, 97.1, 20.2, 98.8, 50.3, 20.55, 75.9, 59.95, 19.15, 98.65, 112.6, 20.6, 35.75, 99.75, 96.1, 85.1, 25.35, 89.65, 86.75, 86.2, 64.8, 108.1, 54.75, 90.4, 44, 95.6, 84.8, 44.3, 19.9, 95.05, 90.05, 109.9, 54.6, 20.05, 19.75, 20.05, 55.9, 19.7, 19.8, 95.4, 93.95, 24.45, 74.95, 87.35, 70.65, 73.25, 98.7, 24.8, 24.3, 69.85, 100.55, 25.7, 40.7, 51.65, 105.1, 85.95, 75.6, 58.25, 65.2, 53.45, 19.75, 44.45, 20.85, 114.05, 89.85, 55.05, 112.95, 101.55, 114.65, 64.8, 80.4, 25.05, 94.75, 105.5, 24.7, 69.75, 60.2, 24.4, 104.15, 92.9, 80.8, 20, 75.1, 19.65, 69.45, 116.05, 40.05, 102.1, 89.7, 19.9, 20.65, 70.05, 53.6, 76.05, 75.7, 19.65, 81.45, 108.5, 84.5, 100.15, 88.6, 52.55, 104.8, 59, 64.05, 20.4, 60.9, 19.8, 116.25, 80.7, 65.2, 84.05, 79.45, 78, 19.85, 94.3, 106.45, 105.45, 95, 54.3, 70.05, 20.05, 105.4, 51.6, 85.5, 91.25, 115.75, 94.7, 19.6, 99.9, 21.1, 20.05, 79.95, 107.15, 89.55, 81.55, 58.45, 95.65, 80.6, 113.1, 58.95, 19.55, 78.95, 105.05, 101.9, 19.75, 110.3, 115.6, 19.35, 25.6, 68.75, 19.9, 70.6, 70.2, 49.3, 107.25, 23.6, 69.7, 99.5, 64.3, 70.85, 101.9, 73.5, 40.4, 19.25, 59.6, 64.9, 110.85, 81.05, 98.05, 70.5, 94.55, 19.65, 19, 20, 63.25, 20.1, 99.15, 90.4, 111.9, 24.9, 83.5, 84.3, 45.6, 54.85, 65.55, 90.35, 20.4, 74.55, 19.95, 74.25, 108.65, 109.55, 86.65, 81, 47.85, 114.55, 20.55, 109.8, 69.5, 48.85, 25.25, 102.85, 87.55, 78.55, 34.55, 92.05, 85.05, 19.7, 95.15, 84.25, 104.6, 111.65, 55, 89.85, 20.35, 54.55, 99.45, 70.9, 25.4, 89.85, 25.25, 69.65, 70, 20.1, 24.8, 95.1, 88.85, 78.8, 20.35, 45.25, 20.05, 69.55, 19.5, 74.75, 30.2, 57.8, 19.85, 25.55, 24.85, 110.35, 24.55, 107.95, 81.4, 73.8, 64.4, 103.75, 71.1, 49.9, 24.6, 30.1, 83.4, 20.45, 75.25, 20.55, 20.05, 20.65, 85.15, 84.95, 66.5, 63.3, 83.15, 84.9, 20.55, 49.25, 59.6, 104.65, 75.3, 80.1, 81, 24.7, 86, 25.4, 89.15, 58.25, 85.65, 50.35, 80.35, 20.2, 20.55, 21.25, 26.25, 80.85, 74.2, 20.35, 75.5, 79.05, 90.15, 50.6, 110.45, 101, 79.35, 89.85, 65, 80.45, 24.1, 44.05, 110.8, 114.95, 75.05, 19.25, 90.05, 56.7, 80.15, 71.35, 20.25, 90.35, 19.7, 19.85, 90.35, 20.8, 66.85, 19.9, 35.8, 78.85, 20.4, 74.25, 64.8, 20.45, 110.65, 40.3, 107, 73.35, 44.8, 54.75, 40.6, 55.3, 60.85, 78.4, 69.65, 59.85, 76.9, 19.85, 67.65, 45, 64.2, 81.7, 25.55, 20, 75.65, 23.8, 64.2, 76.8, 55.2, 108.55, 25.25, 30.4, 20.05, 84.6, 103.7, 88, 106.35, 79.15, 103.1, 63.95, 25.8, 25.55, 44.85, 25.05, 74.1, 88.8, 93.25, 71.4, 79.2, 20.4, 105, 30.85, 20.55, 84.85, 33.15, 92, 89.8, 115.8, 85.15, 24.85, 64.35, 20.5, 86.05, 89, 64.8, 19.8, 93.4, 39, 20.5, 26.4, 98.2, 97.55, 19.95, 99.7, 34.8, 60.15, 64.75, 54.65, 19.3, 111.25, 35.8, 20.05, 84.35, 110.5, 91.2, 89.3, 81.1, 81.2, 94.3, 116.1, 105.55, 94.4, 19.5, 105.6, 81.35, 56.4, 65.35, 19.95, 111.25, 89, 106.1, 20.05, 25.2, 75.4, 65.55, 80.7, 104.55, 24.15, 20.45, 75.4, 79.7, 81.7, 76.3, 103.75, 86.45, 75.1, 80.6, 19.3, 33.6, 83.25, 80.85, 108.05, 19.9, 21.05, 30.15, 79.85, 65.5, 104.1, 20.5, 91.35, 20.5, 75.6, 55.1, 58.95, 95.1, 25.45, 56.75, 81.75, 86.1, 29.8, 20.5, 60.9, 73.25, 20.85, 77.35, 93.85, 70.1, 75.95, 75.25, 54.95, 19.5, 19.6, 47.85, 23.75, 43.8, 19.75, 19.15, 19.6, 80.3, 24.35, 25.25, 26.1, 20, 20.7, 70.3, 69.55, 19.85, 20, 95.85, 20.75, 50.15, 58.65, 95.9, 49.5, 80.1, 24.4, 40.05, 19.5, 51.05, 54.35, 84.7, 86.1, 70.35, 110, 94.9, 69.75, 71.6, 23.65, 81.85, 25.1, 114.7, 49.15, 80.9, 19.3, 70.2, 54.25, 99.3, 74, 50.25, 19.8, 35.5, 80.75, 19.55, 20.05, 112.4, 50.2, 62.25, 55.7, 19.65, 89.25, 54, 56.75, 21.1, 96.65, 24.5, 79.2, 69.55, 20.05, 98.85, 25.75, 19.6, 87.65, 74.75, 107.45, 75.35, 64.95, 100.45, 68.5, 80.55, 81.25, 89.55, 55.7, 24.8, 20, 105.2, 19.55, 79.75, 97.45, 24.25, 24.6, 50.15, 39.6, 89.85, 98.85, 53.85, 24.25, 89.45, 105.25, 59.5, 70.55, 82.5, 61.6, 49.05, 66.25, 19.4, 86.05, 19.15, 19.25, 81.95, 114.65, 20, 19.8, 65.15, 19.65, 88.95, 20.2, 75.2, 56.8, 75.5, 35.6, 60.25, 40.35, 18.85, 54.85, 64.3, 24.65, 76.1, 18.7, 97.95, 94.1, 95.1, 72.35, 82.7, 19.9, 53.8, 51.55, 19.65, 44.05, 114, 100.4, 54.25, 80, 79.2, 94.3, 49.8, 53.75, 93.45, 87.9, 61.05, 104.05, 99.25, 85.7, 104.85, 69.15, 74.45, 50.45, 60, 85.25, 19.45, 20.75, 78.9, 104.5, 49.4, 25, 25.55, 70.15, 69.4, 93.15, 69.55, 20.2, 20.4, 23.75, 70.45, 65.75, 24.6, 69.25, 24.65, 90.4, 100.85, 75.35, 87.2, 64.4, 24.7, 105.85, 98.3, 19.45, 58.7, 20.15, 64.5, 28.5, 90.45, 105.15, 83.15, 103.2, 19.45, 88.8, 85.9, 34.2, 20.15, 50.3, 80.15, 51.25, 95.2, 94.8, 80.25, 76.1, 115.55, 24.65, 53.6, 19.45, 88.2, 101.15, 56.8, 99.4, 20.1, 60.7, 20.95, 114.85, 19.25, 62.8, 105.5, 19.85, 74.1, 107.5, 19.55, 68.8, 84.45, 84.5, 111.2, 80.6, 80.7, 75.6, 57.6, 110.6, 58.2, 81, 19.7, 59.55, 75.55, 45.1, 70.95, 20.9, 19.95, 24.6, 66.7, 94.8, 65.85, 19.95, 24.65, 51.25, 54.25, 19.4, 56.25, 25.15, 23.95, 35.4, 75.35, 20.4, 56.05, 20, 73.05, 20.5, 100.75, 19.95, 49.65, 65.65, 20.45, 60.95, 20.35, 19.5, 75.2, 111.45, 70.15, 92, 85.5, 82.15, 84.4, 60.9, 20.25, 95.3, 19.85, 84.35, 19.85, 82.3, 66.8, 44.6, 98.45, 70.7, 24.95, 102.5, 86.55, 24.3, 58.35, 68.75, 85.8, 20.1, 20.35, 110.8, 82.85, 84.35, 19.55, 19.95, 66.25, 23.3, 25.3, 44.55, 104.1, 92.55, 101.45, 94.55, 95.5, 100.3, 55.5, 49.85, 89.55, 19.15, 99.8, 113.05, 19.95, 74.15, 92, 73.85, 24.45, 24.8, 64.85, 20.75, 68.95, 109.4, 49, 50.25, 19.9, 97.8, 100.3, 55.8, 111.15, 98.55, 50.05, 20.85, 19.5, 19.35, 69.5, 48.8, 94.5, 20.65, 106.05, 108.3, 99.65, 95.9, 20, 64.95, 74.6, 49.2, 73.75, 92.3, 19.2, 88.65, 95.95, 105.4, 20.25, 49.05, 35.55, 65.1, 96.85, 69.75, 99.2, 106.8, 51.25, 57.75, 70.85, 19.55, 79.5, 98.15, 20.25, 79.15, 94.25, 40.2, 19.95, 55.35, 102.15, 71.1, 54.1, 19.65, 88.45, 80.4, 19.25, 84.8, 19.5, 68.6, 92.6, 100.55, 20.55, 19.6, 67.45, 43.55, 109.85, 20.65, 95.4, 21, 56.2, 18.4, 25.75, 19.6, 19.8, 64.2, 75.75, 78.95, 50.3, 80.3, 19.85, 21.1, 50, 104.75, 19.85, 85.9, 80.8, 25.25, 80.55, 81.5, 20.9, 67.25, 20.35, 45.05, 34.65, 69.35, 81.55, 75.4, 67.8, 111.4, 46.3, 20.4, 20.05, 96.1, 19.65, 60.65, 71.7, 36, 65.2, 48.95, 53.5, 80.45, 109.05, 26.3, 106.8, 64.95, 19.35, 21.1, 18.85, 26, 70.35, 96.9, 19.55, 80.4, 94.65, 95.75, 19.55, 104.1, 20.1, 111.55, 60.5, 90.95, 19.7, 50.95, 20.05, 19.4, 59.45, 81.5, 29.05, 70.6, 97.2, 59.2, 75.9, 90.05, 70.95, 102.6, 43.8, 59, 69.95, 24.35, 29.45, 84.4, 20.65, 87.1, 19.85, 90.35, 65.5, 80.95, 56.15, 34.4, 20.75, 18.8, 90.8, 25.6, 70.8, 25.4, 108.8, 76.85, 20.25, 24.8, 115.65, 74.6, 103.15, 72.1, 25.1, 80.15, 25.4, 105.4, 45.75, 24.45, 25, 85.25, 19.6, 50.15, 70.55, 26.4, 20.15, 58.85, 97.55, 19.65, 25.25, 114.45, 70.7, 75.55, 84.8, 20.65, 20.45, 35.65, 90.45, 97.65, 73.85, 69.1, 82.75, 24.4, 55.25, 61.35, 76.75, 19.4, 54.75, 19.7, 19.9, 107.95, 83.8, 56.4, 20.1, 94.9, 94.2, 49.9, 71.05, 81.65, 89.45, 99, 19.05, 114.45, 44.25, 90.55, 20.4, 71.4, 24.85, 104.45, 19.8, 116.45, 20.05, 110.75, 89.7, 48.7, 96.6, 74.3, 54.3, 74.85, 79.95, 20.05, 19.4, 54.9, 24.45, 89.65, 45.4, 75.7, 110.65, 20.55, 115.15, 58.55, 93.25, 19.35, 48.75, 109.05, 25, 54.9, 24.75, 91.15, 104.35, 66.05, 92.2, 105.2, 19.6, 30.4, 61.5, 69.4, 24.75, 91.05, 73.65, 19.4, 26.2, 43.85, 69.7, 38.55, 53.1, 20.65, 64.45, 25.1, 95.15, 79.35, 96.65, 75.5, 19.7, 20.5, 19.2, 98.35, 74.35, 51.35, 45.65, 20.3, 54.2, 90.65, 50.9, 25.05, 74.85, 20.5, 63.55, 47.95, 45, 90.05, 25.3, 24.3, 75.95, 19.7, 66.4, 35.75, 18.8, 19.4, 19.3, 67.45, 20.1, 58.9, 19.45, 50.5, 25.1, 60.7, 99, 104.4, 44.05, 24.1, 45.55, 93.8, 19.7, 70.65, 86.45, 114.1, 95.2, 88.55, 20.75, 44.65, 60.2, 55.45, 70.3, 60.4, 55.8, 31.1, 50.95, 69.1, 43.95, 86.5, 69.95, 90.95, 19.9, 20.15, 90.6, 92, 24.85, 36, 78.5, 19.95, 20.65, 30.5, 106.1, 20.5, 95.5, 64.6, 51.1, 89.1, 54.95, 20.45, 85.95, 60.35, 19.8, 85.35, 72.1, 81.05, 20.5, 111.8, 20.2, 19.7, 19.85, 60.5, 19.55, 20.9, 21.05, 71.5, 54.65, 19.2, 49.8, 20.5, 90.4, 90.25, 104.6, 50.2, 95.5, 95.4, 101.3, 53.1, 84.85, 34.25, 88.6, 60.15, 99.95, 70.7, 54.8, 54.8, 100.3, 53.6, 19.35, 85.6, 80.8, 19.6, 90.7, 69.75, 20, 95.25, 19.95, 80.85, 93.3, 46.35, 78.75, 83.55, 19.6, 67.85, 105.65, 75.5, 20.15, 45.2, 79.25, 85.7, 20.1, 61.8, 49.9, 20.4, 75.4, 108.15, 86.25, 95.7, 116.85, 105.75, 20.15, 19.6, 60.95, 25.05, 60.3, 63.95, 74.3, 70.6, 90.55, 19.45, 64.45, 69.65, 19.5, 110.5, 24.7, 77.4, 85.4, 47.6, 19.4, 103.85, 108.45, 81, 86.65, 92.95, 90.35, 25.15, 76.4, 19.55, 85.35, 24.8, 103.15, 100.75, 94.1, 19.35, 19.9, 101.05, 59.1, 55.85, 106.05, 84.1, 75.3, 24.7, 55.8, 39.7, 29.5, 20.15, 79.55, 24.8, 19.65, 94.05, 90.75, 78.85, 99.5, 80.55, 70.2, 59.45, 93.35, 44.95, 26.1, 20.2, 21.25, 59.4, 95, 61.9, 118.65, 64.45, 80.15, 20.2, 21, 20.45, 75.85, 80.45, 75.5, 44.45, 74.55, 48.15, 19.65, 20.15, 106.6, 91, 25.4, 69.95, 66.85, 20.15, 64.85, 74.85, 50.5, 72.9, 115.05, 19, 19.55, 101.1, 84.1, 24.15, 50.1, 74.6, 19.75, 106.8, 84.5, 25.05, 83.7, 96.6, 101.1, 20.2, 94.05, 81, 60.25, 60.85, 43.95, 86.05, 20.25, 19.4, 102.65, 19.9, 19.55, 95.5, 84.15, 103.2, 50.2, 19.95, 116.25, 31.2, 24.45, 84.2, 85.65, 21.2, 25.55, 20.2, 63.85, 61.95, 25.75, 58.2, 85.85, 104.9, 99.85, 19.55, 104, 104.4, 19.5, 25.25, 49.85, 108.95, 89.9, 82, 89.95, 79.35, 64.05, 101.15, 39.1, 34.6, 19.55, 104.45, 70.5, 20.35, 19.45, 69.9, 59.7, 78.35, 71.45, 45.85, 95.85, 35.7, 89.55, 24.95, 24.85, 100.8, 105.35, 19.65, 54.45, 70.5, 20.1, 69.35, 19.8, 74.4, 93.05, 51.2, 65.6, 80.55, 52.7, 20.85, 52.15, 114.95, 104.45, 113.65, 20.6, 91.55, 49.85, 19.8, 104.15, 48.2, 25.1, 100.15, 55.9, 64.4, 85.3, 107.45, 91.3, 85.95, 45.2, 79.2, 55.5, 90.25, 91.25, 100.9, 97.7, 69.85, 65.6, 104.65, 90.45, 63.7, 104.5, 20.1, 104.3, 93.25, 73.45, 20.7, 25.25, 100.5, 90.6, 89.4, 95.45, 20.45, 19.95, 109.15, 85.7, 102.05, 94.7, 64.4, 26.8, 66.05, 65.2, 85.05, 55.8, 19.95, 45, 114.9, 106.4, 46.1, 39.7, 20.05, 95.75, 24.4, 33.6, 90.45, 84, 67.4, 19.7, 80.35, 19.6, 54.2, 45.2, 75.1, 19.7, 72.75, 20.05, 39.2, 44.75, 82.65, 93.9, 117.15, 99.25, 112.55, 25.7, 90.3, 49.4, 19.4, 109.7, 61.25, 55.3, 103.75, 19.5, 39.5, 26.05, 91.05, 29.65, 50.2, 105.3, 55.45, 85.45, 19.8, 59.25, 90.7, 79.05, 90.7, 95, 30.25, 49.85, 93, 54.55, 19.7, 84.8, 94.45, 20.85, 60, 80.45, 84.95, 49.65, 20.2, 100.5, 35.75, 86.45, 53.8, 38.55, 39.9, 70.05, 20.1, 20.3, 35.65, 82.95, 55.65, 25.2, 50.8, 19.65, 59.8, 73.55, 61.4, 19.9, 19.45, 81.5, 109.55, 74.4, 74.9, 59.65, 110.45, 74.45, 24.55, 24.55, 90.65, 105.05, 20.45, 19.55, 19.7, 70.45, 85.65, 20.55, 97.95, 20, 25.25, 70.9, 19.85, 106.35, 99.5, 84.7, 86.05, 44.55, 75.85, 25, 45, 20.5, 90.45, 60.45, 78.45, 100.55, 20.35, 90.75, 20.25, 20.05, 19.6, 53.8, 70.2, 75.5, 20.35, 26.05, 20.6, 20.1, 24.3, 24.5, 110.5, 25.25, 90.1, 68.75, 19.2, 115.1, 99.65, 91.45, 84.75, 78.75, 20.25, 19.9, 97.75, 100.4, 24.45, 101.1, 50.9, 107.2, 92.2, 113.4, 40.55, 26, 111.95, 53.8, 72.1, 78.85, 70.75, 76.15, 39.1, 69.95, 20.05, 20.05, 19.45, 26.9, 19.2, 50, 60, 84.55, 45.45, 20.05, 115.55, 99, 50.55, 25, 91.55, 19.35, 24.85, 100.4, 25, 19.25, 108.25, 20.15, 101.3, 20, 105.3, 69.85, 65.25, 19.8, 19.6, 20.05, 49.4, 88.4, 100.6, 19.45, 20.3, 107.65, 80.45, 58.85, 109.6, 75.15, 73, 70.1, 98.65, 111.45, 114.9, 100.55, 20.4, 104.35, 80.45, 91.35, 19.9, 68.35, 79.1, 51, 80.55, 66.7, 86.4, 50.05, 25.7, 83.4, 84.65, 64.75, 100.15, 25.25, 113, 40.65, 94.95, 59.9, 19.8, 81.8, 20, 59.6, 25, 84.35, 55.55, 75.35, 59.3, 66.1, 18.8, 86.45, 52.1, 47.4, 109.15, 94.95, 93.55, 79.5, 115.05, 95.15, 105.4, 20.1, 20.05, 20.7, 20.35, 19.7, 85.45, 40.4, 105.2, 100.65, 91, 116.75, 59.1, 49.8, 19.3, 19.65, 81.4, 38.9, 87.95, 19.85, 96.35, 24.15, 19.1, 44, 60.6, 25.65, 53.95, 20.4, 29.35, 20.45, 95.1, 25.25, 44.9, 92.65, 43.7, 72.6, 18.95, 20.5, 19.95, 24.5, 20.6, 61.05, 106.65, 108.25, 20.4, 55.3, 20.25, 72.95, 89.45, 104.65, 75.2, 101.15, 68.75, 111.05, 99, 21, 19.4, 77.2, 19.45, 24.85, 41.35, 19.6, 84.45, 20.25, 19.65, 20.65, 99.3, 81.05, 67.6, 70.15, 115, 84.8, 19.7, 63.15, 74, 29.1, 50.05, 20, 74.65, 44.4, 85.4, 94.1, 108.9, 56.2, 26.1, 85.45, 88.95, 74.35, 48.85, 80.1, 56.05, 89.8, 19.1, 20.35, 106.05, 19.65, 59.85, 86.1, 19.45, 97.1, 36.65, 103.9, 19.75, 24.55, 48.7, 109.55, 20.65, 55.2, 24.05, 20.45, 19.25, 26.35, 43.8, 50.15, 20.45, 61.4, 70.75, 61.15, 20.25, 63.85, 98.7, 20, 19.3, 84.4, 25.1, 48.25, 19.85, 94.2, 62.15, 79.3, 56.25, 20.3, 99, 90.6, 85.9, 79.2, 70.35, 19.35, 50.15, 63.8, 20.55, 88.55, 101.4, 44.6, 63.75, 109.25, 84.6, 20.45, 85.75, 107.95, 22.95, 19.45, 19.7, 87, 79.95, 64, 64.9, 25.75, 90.15, 116.1, 104.95, 50, 20.45, 19.85, 19.95, 26.45, 63.4, 53.95, 95.1, 74.1, 35.5, 79.2, 48.8, 55.45, 25.4, 93.5, 63.9, 64.85, 63.8, 44.45, 19.95, 43.35, 49.65, 85.1, 89.8, 103.05, 116, 69.9, 95.1, 40.25, 25.75, 105.35, 113.6, 24, 19.4, 86.1, 102.65, 92.85, 97.75, 97.95, 19.95, 24.6, 50.95, 75.6, 80.75, 90.4, 60.25, 20.2, 64.15, 20.25, 99, 80.3, 19.55, 100.75, 53.75, 25.6, 58.35, 46.35, 113.75, 90.4, 109.3, 90.3, 65.25, 60.65, 24.1, 19.5, 85.95, 53.5, 25.45, 20.5, 20.85, 89.9, 26, 113.2, 69.05, 20.1, 109.65, 19.2, 90, 34, 20.4, 38.6, 25.25, 60.6, 74.75, 20.6, 20.4, 81.7, 20.3, 20, 25, 80.45, 19.75, 65.65, 71, 89.2, 86.75, 61.5, 25.1, 34.05, 19.95, 19.95, 89.7, 20.4, 26.3, 20.7, 19.45, 110.8, 69.3, 49.35, 20.35, 105.6, 64.45, 49.9, 65.65, 103.3, 44.45, 89.9, 55.05, 104.1, 106.6, 70.5, 19.6, 24.05, 38.1, 34.25, 100.05, 68.65, 45.8, 75.75, 96.4, 20.55, 50.95, 90.5, 79.4, 58.75, 59.45, 105.7, 53.3, 24.3, 59.9, 23.95, 20.15, 95.65, 81, 82.45, 20.5, 54.4, 58.6, 84.8, 61.4, 20.4, 20.15, 94.45, 79.8, 74.05, 49.15, 19.4, 113.65, 106, 25.95, 19.1, 100.55, 95.4, 75.15, 89.15, 107.9, 19.5, 24.95, 19.5, 69.95, 82.85, 19, 38.85, 20.35, 95, 74.4, 78.45, 74.3, 51.05, 19.2, 109.1, 76.45, 72.8, 18.95, 101.75, 75.45, 64.1, 25.65, 75.1, 95.85, 54.4, 72.75, 19.85, 19.05, 94.85, 46.25, 19.35, 69.6, 90.7, 101.4, 20.25, 48.8, 74.35, 19.35, 68.75, 100.2, 20.85, 19.35, 45, 25.5, 48.9, 19.6, 20, 81.3, 95.2, 83.3, 20.3, 89.85, 19.8, 54.65, 29.35, 19.15, 19.1, 80.55, 20.25, 106, 25.5, 79.6, 55.25, 88.05, 20.4, 117.6, 20, 19.65, 70.55, 65.8, 20.05, 80, 35.4, 79.6, 80.25, 20.45, 79.6, 24.7, 77.3, 29.75, 44.9, 29.8, 71.95, 20.75, 56.3, 105.25, 19.55, 84.45, 53.65, 29.9, 19.7, 43.7, 55.3, 19.85, 19.65, 49.45, 20.45, 39.7, 54.5, 111.6, 55.55, 20.55, 62.1, 104.5, 101.8, 110.6, 84.9, 93.2, 24.4, 70.55, 85, 85.8, 91.1, 20.1, 20.05, 74.8, 24.8, 100.85, 105.1, 20.4, 20, 79.4, 57.2, 58.6, 94.8, 102.5, 20.35, 84.9, 69.2, 20.85, 88.5, 35, 55.15, 50.95, 64, 80.2, 49.3, 84.35, 20.05, 117.2, 20.1, 109.95, 94.75, 80, 79.65, 25.2, 19.9, 44.8, 20.3, 19.2, 80.05, 107.35, 47.85, 70.8, 59.1, 25.55, 20.25, 75.55, 95.3, 70.25, 50.3, 19.85, 19.35, 25, 20.3, 75.35, 88, 43.8, 62.05, 20.1, 101.35, 84.05, 20.9, 105.9, 85.05, 44.1, 90.2, 53.45, 19.95, 74.65, 57.5, 19.65, 93.8, 89.25, 94.15, 55.6, 48.7, 104.9, 19.9, 19.4, 25.05, 84.45, 19.3, 79.85, 25.55, 68.4, 20.65, 55.15, 70.6, 19.95, 19, 44.1, 107.6, 61.55, 90.7, 99.25, 91.7, 100.7, 78.45, 84.3, 19.55, 20.45, 55.6, 86.8, 20.95, 20.05, 113.65, 59.5, 87.8, 41.9, 69.85, 56.3, 109.55, 92.15, 69.5, 97, 58.35, 70.4, 94.3, 19.55, 95.95, 94.8, 107.75, 54.6, 71.3, 19.5, 56.3, 90.55, 60.8, 98.8, 98.15, 35.35, 103.15, 107.75, 81.4, 95.7, 104.8, 70.95, 44.95, 97.65, 35.65, 85.25, 19.5, 25.1, 100.05, 55.7, 91.15, 83.85, 45.9, 25.1, 19.7, 91.5, 51.3, 21.1, 104.75, 85.75, 20.3, 100.75, 74.15, 78.55, 19.85, 50.7, 45, 77.8, 83.45, 94.8, 20.1, 59.9, 90.1, 70.95, 29.2, 46.6, 74.3, 69.3, 94.3, 76.45, 54, 104.25, 19.95, 24.95, 84.75, 19.75, 113.65, 44.9, 75.25, 24.6, 25, 20.95, 110.6, 55.5, 19.45, 84.85, 19.6, 53.45, 19.8, 112.1, 74.2, 69, 19.35, 19.8, 109.2, 79.15, 53.65, 100.2, 108.65, 40.65, 55.35, 105.6, 95.7, 83.2, 90.05, 97.65, 68.05, 102.1, 23.4, 71.05, 19.45, 59.45, 92.2, 19.85, 43.9, 90.5, 90.45, 84.6, 99.15, 19.95, 20.5, 62.1, 79.5, 19.55, 20.35, 51.7, 23.3, 65.1, 81.2, 74.5, 80.5, 60.3, 75, 90.15, 69.05, 59.7, 19.85, 40.75, 95.7, 46.3, 81.3, 20, 66.15, 19.6, 49.8, 101.75, 55.15, 103.95, 99.65, 73.7, 50.05, 60.25, 87.3, 54.25, 85.3, 50, 90.95, 72.25, 96.1, 19.85, 55.3, 20.1, 69.5, 25.15, 79.65, 71.25, 113.8, 24.55, 19.7, 100.5, 74.45, 104.1, 19.05, 25, 19.05, 81.9, 69.7, 90.15, 25.35, 24.65, 19.55, 60, 89.9, 19.4, 49.8, 24.1, 54.25, 109.9, 35.5, 87.55, 88.4, 50.8, 99, 96.55, 59.75, 111.5, 24.25, 20.5, 70.4, 30.55, 84.9, 54.5, 75.35, 44.45, 98.05, 63.9, 69.15, 64.65, 98.85, 89.6, 83.25, 70.25, 24.5, 20.1, 73, 61.4, 84.3, 19.9, 20.4, 50.75, 20.45, 75.75, 65.4, 59.75, 78.5, 48.95, 99.65, 18.25, 54.55, 40.65, 20.45, 24.8, 88.8, 20.05, 69.8, 77.15, 35.05, 108.1, 84.05, 20.2, 49.2, 24.6, 71.65, 104.9, 106.5, 75.5, 58.5, 78.9, 79.2, 109.45, 59.2, 29.15, 20.05, 66.5, 49.55, 73.6, 82.65, 49, 25.2, 25.45, 110.9, 77.75, 26.2, 19.9, 80.85, 56.35, 19.3, 50.4, 55.25, 19.1, 84.05, 105.2, 75.75, 95.3, 19.85, 69.1, 20.25, 54.75, 81.45, 80.2, 100.3, 90.95, 20, 79.85, 73.55, 19.3, 20.15, 44.55, 54.45, 19.65, 105, 88.7, 75.15, 20.25, 109.1, 30.75, 112.9, 94.05, 78.85, 55.3, 19.35, 25.35, 20.45, 19.35, 78.65, 74.75, 19.9, 58.35, 100.5, 20.05, 25.65, 95, 45.45, 20, 49.2, 83.25, 19.25, 19.65, 72.8, 109.65, 65, 114.1, 20.65, 86.95, 94.75, 25.35, 105.45, 25.4, 102.55, 24, 25.6, 73.5, 98.25, 54.4, 103.1, 34.2, 43.75, 100.65, 116.05, 82, 65.15, 44.8, 79.8, 88.85, 74.95, 106.85, 19.3, 56.1, 19.7, 51.3, 118.6, 24.15, 20.3, 115.5, 25.05, 109.1, 19.65, 111.3, 29.9, 80.6, 20.8, 89.95, 116.05, 19.55, 115.25, 24.8, 19.9, 81.25, 69.95, 86.4, 66.3, 94.65, 72.1, 34.7, 95.95, 44.8, 109.4, 71.05, 19.7, 40.25, 19.85, 68.25, 20.15, 50.95, 25.15, 20.25, 44, 20.25, 55.8, 88.9, 57.65, 79.15, 108.05, 94.8, 45.9, 102.6, 61.35, 57.55, 29.25, 19.6, 111.75, 106.5, 107.7, 19.3, 20.05, 69.95, 63.7, 50.9, 60.4, 79.25, 110.1, 90.7, 25.3, 85.2, 24.35, 25.1, 54.55, 96.6, 81.15, 38.5, 92.9, 84.7, 66, 20.75, 61.45, 54.5, 99.75, 109.75, 80.85, 20.3, 67.8, 19.8, 25.7, 56.15, 86.7, 20.4, 19.65, 54.35, 108.1, 54.45, 45.35, 59, 69.45, 64.95, 18.85, 19.8, 25.05, 114.3, 109.2, 45.05, 51, 110.45, 84.65, 60.05, 44.65, 93.25, 20.25, 25.45, 20.6, 94.1, 34.8, 60.75, 51.35, 64.05, 84.8, 50.15, 94.6, 59.75, 100.25, 98.9, 97.7, 60.25, 56.25, 46.2, 24.9, 63.35, 50.1, 50.15, 64.65, 79.6, 19.5, 99.55, 74, 38.9, 79.55, 46.3, 99.35, 95.8, 78.15, 26.1, 40.35, 79.2, 20.9, 49.9, 68.9, 20.25, 76, 74, 82.3, 89.4, 99.15, 29.45, 19.8, 59.15, 44.75, 90.8, 49.55, 106.7, 94.45, 19.45, 67.95, 65.25, 99.45, 20.35, 19.95, 77.4, 19.7, 99.7, 74.8, 19.15, 78.95, 62.85, 71.55, 94.95, 86.1, 19.55, 24.8, 84.05, 36.25, 98.6, 103.65, 92.9, 19.9, 20.1, 80.5, 39.85, 60.5, 103.85, 67.8, 24.85, 19.35, 89, 55, 76.15, 20.3, 117.35, 19.75, 45.2, 25.2, 89.75, 75, 49.95, 65.7, 67.05, 110.9, 87.95, 19.8, 75.7, 62.15, 115.15, 19.5, 86.55, 20.4, 19.8, 45.65, 56.4, 73.3, 101.35, 33.6, 20.7, 104.05, 20.25, 73.7, 108.75, 20.15, 19.75, 25.95, 70.05, 24.05, 84.75, 23.05, 59.95, 19.55, 19.6, 20.05, 85.55, 78.6, 116.8, 43.55, 60.8, 54.9, 65.2, 108.2, 92, 75.1, 25.05, 75.15, 19.5, 19.3, 112.2, 70.3, 19.6, 20.25, 80.65, 115.75, 80.6, 59.55, 19.05, 95.65, 19.95, 19.4, 36.1, 19.75, 64.1, 19.75, 19.7, 110.2, 106.35, 90.55, 65.9, 104.5, 52.5, 56.1, 88.75, 26, 99.4, 73.15, 54.65, 115.55, 104.45, 91.15, 89.7, 92.4, 19.9, 18.85, 25.75, 20.95, 97.05, 25.4, 19.7, 35, 101.25, 70.2, 90.95, 73.85, 88.05, 20.1, 110.3, 85.15, 60.95, 73.55, 46, 58.55, 24.6, 19.75, 86.35, 25.5, 19, 19.55, 110.1, 69.75, 50.6, 65.6, 82.1, 79.1, 90.65, 20.55, 75.75, 110, 20.85, 80.35, 70.15, 67.45, 20.75, 69.9, 51.1, 25.55, 60, 90.55, 76.4, 84.95, 110.1, 99.65, 45.4, 69, 48.65, 59.85, 80.65, 20.55, 66.4, 100.2, 44.55, 20.35, 91.8, 20.2, 50.35, 18.8, 20.45, 64.75, 98.7, 89.45, 58.75, 20.7, 85.6, 80.3, 79.8, 79.85, 54.1, 80.9, 24.5, 20.15, 20.05, 19.6, 114.3, 100.3, 80, 20.85, 89.95, 20, 48.75, 80, 20.35, 20.25, 19.4, 100.4, 57.95, 59.5, 19.2, 59.55, 103.95, 68.95, 103.1, 24.7, 110.2, 62.45, 89.55, 78.9, 20.35, 71.45, 46.35, 94.65, 49.9, 25.45, 20.75, 66.1, 75.4, 21.05, 69.35, 88.85, 97, 66.4, 69.2, 79.5, 100.65, 79.7, 61.4, 69.8, 40.55, 75.65, 90.7, 80.5, 60.6, 101.15, 24.95, 20.3, 60, 20.25, 44.75, 98, 107.7, 104.7, 93.9, 86.45, 19.4, 24.95, 75, 78.2, 94.2, 50.05, 69.55, 90.1, 44.65, 80.35, 98.1, 53.35, 19.55, 48.95, 54.2, 24.45, 40.15, 25.6, 70.35, 91.7, 89.2, 24.1, 53.85, 115.6, 19.75, 24.05, 25.3, 84.3, 89.75, 97.95, 20, 103.9, 20.7, 20.15, 26, 77.35, 66.05, 19.9, 68.15, 80.85, 75.5, 80.6, 83.2, 87.55, 109.4, 45.55, 20.7, 75.3, 93.4, 73.75, 88.15, 49.2, 19.65, 105.15, 49, 49.8, 20.95, 79.3, 19.5, 44.15, 105.5, 92.7, 26.25, 96.95, 20.45, 115.8, 108.2, 20.2, 54.9, 20.15, 90.35, 55.75, 114.6, 66.8, 100.3, 105.35, 85.2, 18.95, 69.8, 106.15, 20.55, 105.75, 25.25, 19.75, 104.85, 60.95, 81.15, 19.1, 20.8, 90.15, 90.1, 74.1, 118.75, 85.9, 95, 20.15, 101.3, 21.2, 24.2, 20.3, 85.3, 89.6, 56.25, 50.95, 115.85, 103.65, 26.1, 35.1, 99.1, 67.25, 25, 59.55, 77.8, 55.1, 24.15, 45.25, 20.25, 64.75, 54.6, 20.7, 94.75, 115.8, 49.45, 83.8, 95.35, 74.05, 89.6, 116.6, 54.2, 19.3, 65.05, 24.05, 18.75, 20.15, 20, 71, 93.6, 24.4, 65.25, 50.55, 70.7, 45.25, 70.3, 108.95, 26.45, 19.65, 19.05, 74.75, 75.8, 25.1, 44.45, 104.3, 89, 20.15, 36.15, 19.2, 19.25, 61.2, 20.45, 35.05, 44, 50.35, 20, 86.4, 58.4, 94.1, 108.9, 107.4, 90.85, 19.9, 66.4, 100.7, 25.6, 19.85, 20.75, 95.8, 94.65, 106.65, 45.85, 104.35, 55.45, 61.15, 78.95, 109.2, 61.3, 96.85, 40.55, 19.8, 108.25, 105.05, 90.45, 86.4, 66.9, 110.7, 20, 102.1, 70.15, 80.05, 49.2, 20.5, 38.25, 54.95, 96.6, 19.9, 19.9, 84.6, 85.25, 81.25, 115.5, 79, 94.65, 20.8, 59.5, 20.05, 100.45, 20.6, 20.3, 39.55, 20.45, 25.25, 91.25, 72.45, 19.7, 75.1, 25, 69.15, 91.55, 35.8, 113.15, 19.85, 19.8, 19.9, 19.7, 59.1, 91.15, 68.95, 51.55, 24.4, 96.8, 70.05, 19.5, 78.75, 69.2, 19.55, 103.65, 54.7, 54.15, 84.85, 20, 99.25, 19.35, 94.75, 114.05, 74.9, 19.8, 80.85, 54.65, 91.7, 118.6, 24.55, 19.45, 116.15, 80.6, 20.3, 99.8, 75, 19.9, 80.3, 84.3, 54.05, 104.9, 97.25, 83.05, 41.1, 45, 74.55, 40.2, 70.5, 19.75, 24.65, 104.25, 78.35, 109.7, 33.45, 94.6, 20.2, 20.3, 39.4, 69.15, 51.35, 100.05, 20.3, 94.45, 46.4, 104.05, 24.9, 59.6, 108.5, 40.55, 58.95, 20.75, 113.15, 48.8, 63.05, 100.85, 80.55, 64.4, 75.2, 84.9, 19.3, 74.65, 59.05, 69.1, 20.55, 76.55, 62.5, 94.9, 111.65, 19.9, 20.45, 106.05, 113.45, 92.55, 65.6, 84.35, 71.1, 85.15, 49.7, 30.2, 56.35, 107.55, 19.85, 95.9, 23.85, 83.85, 84.8, 76.1, 74.55, 39.2, 79.55, 19.6, 19.55, 39.15, 20.1, 99.95, 59.8, 49.75, 108.5, 60.15, 19.05, 84, 44.55, 103.45, 80.65, 57.2, 110.75, 24.7, 97.05, 76.35, 18.9, 74.45, 84.4, 24.4, 20.05, 55.5, 84.3, 100.2, 19.4, 20.4, 94.75, 44.35, 74.55, 73.6, 50.1, 53, 19.85, 24.35, 19.55, 25.05, 93.8, 103.75, 56.75, 20.8, 24.45, 25.6, 59.65, 83.3, 79.55, 24.45, 19.2, 29.8, 45.5, 30.05, 65.65, 74.05, 110.75, 19.7, 49.5, 43.95, 111.15, 20.6, 19.65, 115.8, 88.65, 94.5, 20.1, 34.65, 52.3, 65, 35.45, 19.7, 95.6, 19.85, 81.85, 109.3, 25.4, 69.8, 20, 109.9, 50.3, 101.5, 89.15, 19.4, 29.9, 78.8, 19.3, 96.8, 20.65, 19.8, 104.6, 80.05, 45.15, 73.15, 99.1, 105.35, 45.65, 79.95, 54.45, 25.1, 84.7, 75.85, 48.8, 35.2, 76.25, 24.9, 54.3, 66.3, 20.9, 75.35, 104.45, 49.45, 19.45, 92.15, 19.85, 100.25, 95.7, 93.15, 69.7, 19.8, 71.35, 20.75, 40.6, 20.35, 19.75, 54.4, 20.45, 66.15, 89.85, 45.05, 86.85, 96.75, 106.65, 110.15, 82.85, 20.1, 59.45, 58.6, 49.7, 65.85, 73.5, 113.65, 83.4, 65.65, 61.35, 85.9, 75.65, 70.9, 49.85, 20.1, 103.05, 99.7, 81.9, 66.2, 19.75, 72.6, 116.5, 106.8, 24.95, 89.25, 19.25, 104.55, 87.2, 30.75, 25.7, 86.2, 30.1, 99.35, 19.2, 20.1, 20.35, 25.65, 94.55, 94.4, 56.1, 68.25, 24.75, 76.25, 74.35, 54.15, 19.45, 34.95, 53.65, 104, 70.35, 64.85, 19.65, 45.9, 20, 44.8, 20.35, 45.8, 108.95, 64.35, 90.8, 24.95, 84.7, 70.8, 104.4, 101.5, 54.3, 103.95, 91.1, 19.95, 26.45, 75.1, 108.1, 110.15, 111.5, 106.5, 19.9, 111.1, 70.7, 24.85, 91.2, 65.6, 59.45, 109.95, 38.5, 92.55, 24.5, 19.7, 20.6, 58, 107.45, 65.5, 25.45, 100.15, 104.45, 21.15, 96.2, 44.4, 94.35, 20.3, 105.75, 81.15, 89.55, 54.75, 53.75, 105.75, 105.85, 64.2, 87.7, 89.3, 20.15, 20.05, 67.2, 94.55, 107.5, 73, 114.75, 76.05, 77.9, 90.65, 110.45, 68.7, 44.85, 29.8, 88.9, 58.75, 19.85, 86.9, 59.65, 66.4, 20.15, 108.1, 56.9, 109.6, 25.15, 79.15, 66.75, 48.8, 80.7, 20.55, 115.1, 59.7, 86.45, 33.7, 80.1, 104.05, 108.75, 41.1, 20.35, 105.9, 65.5, 40.45, 70.45, 78.8, 90.1, 82.45, 20.25, 66.25, 89.7, 64.55, 93.65, 73.6, 109.75, 61.45, 106.4, 81.9, 105.2, 54.6, 20.55, 19.7, 66.05, 54.05, 58.9, 96.9, 19.1, 50, 45.4, 85.45, 84.1, 66.25, 76.9, 74.6, 116.95, 40.65, 114.35, 69.7, 98.65, 61.65, 89.35, 95.4, 35.4, 19.95, 19.25, 20.4, 24.75, 25.35, 20, 59.75, 82.5, 20.35, 90.8, 104.95, 105.25, 23.75, 61.3, 75.8, 98, 52, 64.4, 45.8, 30.5, 69.15, 49.25, 39.35, 105.1, 20.1, 19.75, 19.75, 70.4, 20.45, 20.35, 86.2, 95.65, 103.8, 97.2, 63.55, 24.95, 99, 85.55, 94, 50.3, 95, 61.4, 80.55, 78.5, 114.3, 20.05, 62.65, 92.7, 100.45, 75.2, 84.75, 79.5, 19.8, 100.9, 95.3, 90.95, 54.5, 49.6, 25, 45.45, 107.75, 89.1, 44.75, 101.6, 103.15, 95.65, 75.1, 61.35, 19.7, 51, 88.85, 20.05, 65.1, 70.15, 20.75, 56.05, 19.95, 98.6, 79, 89.45, 74.2, 81, 49.6, 84.6, 84.2, 106.3, 69.05, 45.4, 99.35, 50.75, 87.1, 20.15, 98.7, 25.2, 55.7, 65.35, 25.3, 24.25, 60.5, 25.1, 20.05, 30.25, 20.2, 59.9, 25.15, 101.3, 76.95, 55.3, 92.45, 48.45, 19.35, 86.7, 55.7, 84.25, 64.65, 69.2, 54.65, 24.75, 23.95, 105, 59.85, 20.05, 92.15, 44.8, 20.9, 95.4, 80.35, 85.1, 115.05, 19.95, 86.15, 78.85, 86.55, 42.4, 24.25, 20.5, 19.6, 20.25, 20.6, 19.8, 80.2, 116.4, 31.65, 94.15, 20.65, 76.85, 20.15, 55.25, 82.15, 103, 95.1, 95.15, 79.8, 74.8, 20.45, 78.35, 19.1, 20, 19.95, 24, 19.15, 91.3, 19.15, 19.75, 75.5, 83.75, 19.4, 26.5, 19.15, 40.9, 80.25, 70.8, 60.2, 55.2, 54.15, 100.4, 62.55, 70.45, 85.5, 54.5, 20.75, 20.35, 91, 104.8, 51.1, 89.8, 20.55, 64.05, 74.85, 25, 20.3, 26.35, 54.7, 90.25, 20.65, 25.45, 19.5, 66.15, 69.1, 39.1, 20.05, 59.8, 48.6, 105.35, 25.1, 49.75, 94.75, 93, 71.9, 77.55, 19.85, 95.25, 25.05, 53.15, 20.15, 101.25, 100.55, 25.3, 71.8, 19.7, 49.85, 69.6, 19.75, 80.8, 64.2, 35, 19.2, 90.65, 20, 74.65, 61.2, 19.95, 54.8, 73.45, 51.45, 80.45, 85.3, 79.3, 76.5, 25.4, 86.85, 19.65, 45.55, 78.1, 19.3, 110.5, 20.3, 81.35, 55.3, 56.55, 19.7, 104.05, 52.85, 80.65, 24.65, 21.3, 110.2, 51.05, 19.8, 19.9, 87.3, 19.85, 89.4, 20, 20.05, 83.25, 102.9, 39.1, 114.5, 20.2, 55.8, 24.2, 72.8, 99.85, 99.5, 20.25, 26, 19.9, 19.05, 96.5, 19.85, 25.7, 20.3, 91.55, 39.4, 105.7, 70.25, 93.75, 60, 59.8, 90.65, 109, 68.1, 20.4, 81.95, 60.55, 65.6, 82.5, 82.3, 68.15, 20.3, 20.2, 89.2, 74.8, 20.2, 84.4, 25.15, 19.8, 50.85, 102.4, 55.5, 109.75, 106.4, 60, 88.8, 80.05, 75.55, 49.55, 23.9, 66.4, 18.8, 108.4, 85.95, 80.9, 111.8, 20.6, 44.6, 105.1, 115.15, 59.8, 26.3, 70.55, 20.05, 79.85, 90.05, 24.45, 59.95, 25.35, 34.3, 105.05, 19.3, 19.15, 51.4, 71.85, 75.4, 49.7, 78.75, 81.6, 70.4, 76.1, 94, 103.95, 19.95, 110.8, 96.1, 48.8, 50.55, 44.65, 19.45, 89.3, 19.25, 70.5, 19.65, 20.85, 19.65, 19.35, 44, 94.4, 75.4, 71, 21.2, 61.05, 79.95, 19.7, 20.3, 24.35, 19.75, 50.3, 50.25, 85.35, 51.65, 24, 59.85, 25.45, 23.9, 24.15, 75.7, 50.85, 91.6, 98.9, 85, 44.3, 80.2, 60.9, 34.2, 87.15, 54.3, 19.1, 112.75, 19.95, 19.5, 65.55, 78.8, 78.2, 105.25, 89.25, 20.65, 68.7, 78.65, 24.75, 19.75, 89.1, 84.7, 59.9, 19.95, 108.9, 33.6, 85.85, 34.85, 95.3, 84.6, 44.95, 24.7, 100.3, 25.45, 50.7, 55, 68.4, 55.05, 19.8, 84.45, 35.9, 80.75, 78.65, 61.75, 63.7, 87.6, 89.15, 20, 104.4, 20.05, 34.3, 20.65, 84.25, 19.65, 79.85, 20.2, 19.8, 50.35, 74.6, 79.15, 20.35, 21.05, 94.7, 74.95, 111.95, 19.85, 89.75, 20.05, 108.95, 19.65, 24.9, 93.2, 84.8, 71.75, 30.35, 54.85, 19.5, 24.2, 19.35, 100.65, 94.1, 74.55, 56.15, 20.35, 80.55, 61.25, 20.45, 18.9, 19.6, 45.2, 19.45, 25.45, 94.9, 29.3, 20.25, 110.5, 109.4, 19.95, 19.6, 76.6, 19.6, 85.3, 65.85, 20.05, 99.4, 20, 78.45, 25.1, 55, 71.1, 61.55, 45.9, 40.3, 87.1, 49.5, 73.8, 19.2, 25, 35.3, 76.75, 81, 105.55, 18.8, 24.9, 64.9, 61.35, 113.95, 90.15, 54.1, 49.8, 24.4, 95, 69.9, 39.95, 103.25, 94.25, 47.05, 20.55, 19.65, 70.2, 81, 75.9, 24.7, 110.25, 85, 19.75, 23.9, 19.95, 25.15, 54.15, 59.8, 83.85, 104.9, 75.3, 66.65, 109.5, 73.85, 19.3, 118.2, 51.45, 59.45, 19.5, 19.55, 93.55, 59.3, 109.8, 78.1, 39.9, 64.9, 53.4, 24.9, 44.7, 114, 20.25, 83.85, 20.2, 19.95, 20.35, 90, 54.2, 99.1, 66.9, 25.85, 91.05, 20.95, 109.2, 85.8, 19.65, 20.5, 89.65, 74.35, 49.45, 89.1, 75.15, 70.65, 90.05, 19.4, 88.75, 91, 90.8, 18.95, 102.4, 99.9, 88.7, 54.3, 55.7, 103.95, 20.15, 20.05, 91.95, 55.65, 74.7, 104.15, 83.65, 110.05, 25.5, 19.5, 80.7, 105.1, 25.15, 95.65, 80.8, 24.85, 54.75, 50.75, 20.15, 20.05, 71.6, 81.45, 58.4, 53.7, 19.6, 89.4, 84.2, 106.1, 25.75, 64.95, 85.45, 20.05, 20.7, 25.3, 100.6, 74, 99.4, 107.45, 83.6, 99.05, 80.1, 65.3, 89.55, 60.8, 74.5, 99.15, 19.25, 39.45, 44.85, 97.2, 110.55, 19.9, 76.95, 35.4, 20.45, 96.75, 54.2, 100.1, 45.25, 20.85, 33.45, 20.2, 85.9, 61, 86.9, 69.4, 20.35, 104.3, 44.95, 49.45, 20.6, 19.55, 93.5, 54.55, 20.05, 79.45, 79.85, 100, 19.6, 20.2, 50.4, 113.35, 80, 80.95, 24.9, 54.9, 109.25, 116.3, 19.9, 70.35, 25.6, 44.45, 100.15, 73.85, 70.1, 25.25, 21.05, 24.95, 64.5, 105.95, 75.85, 43.6, 91.25, 89.75, 104.4, 90.15, 40.3, 105.25, 104, 69.65, 74.3, 100.9, 20.25, 96.9, 104.1, 20.1, 56.55, 68.6, 69.05, 19.7, 20.05, 94.4, 54.95, 93.7, 110.25, 98.9, 80.45, 79.4, 62.8, 74.9, 74.85, 25.85, 68.3, 48.4, 105.05, 25.15, 19.5, 92.95, 20.7, 19.35, 104.35, 19.55, 74.05, 40.1, 20.1, 83.55, 56.85, 19.55, 106.15, 78.95, 49.75, 92.4, 58.2, 91.95, 65.25, 73.1, 59.75, 59.8, 116.6, 109.3, 101.4, 50.65, 56.15, 19.2, 83, 70.1, 108.3, 25.25, 45.35, 43.9, 79.3, 84.9, 79.25, 71.05, 53.75, 24.25, 44.25, 50.05, 20.15, 69.35, 19.35, 19.15, 61, 20.5, 50.2, 79.6, 24.9, 106.9, 101.35, 55.35, 50.55, 19.5, 90.65, 89.85, 79, 19.55, 19.9, 116.25, 87.75, 81.3, 44.3, 70.35, 44.45, 49.15, 29.45, 85.3, 69.1, 70.35, 20.6, 74.15, 75.05, 44.6, 21.45, 43.45, 20.05, 94.15, 19.55, 75.9, 64.15, 109.55, 110.8, 53.45, 69.95, 97, 90.6, 73.55, 94.35, 19.4, 19.75, 54.6, 29.8, 103.05, 20.3, 35.1, 105.7, 56.25, 60.35, 59.8, 99.65, 50.65, 60.9, 59.65, 64.7, 54.85, 91.35, 25.1, 34, 45.9, 20.5, 20.35, 36.1, 65.8, 20.35, 105.8, 96.75, 24.4, 73.05, 64.35, 20.5, 54.75, 51.15, 41.95, 54.35, 96, 61.45, 19.65, 19, 100, 98.7, 19.8, 73.8, 20.05, 106.2, 116.55, 99.7, 19.7, 19.5, 29.15, 55, 90.8, 51, 90.1, 59.05, 20.3, 72.95, 73.55, 84.3, 78, 72.1, 106.75, 19.25, 20.55, 20, 24.65, 103.5, 23.85, 25.8, 59.45, 20.05, 82.55, 81.25, 74.3, 109.7, 96.35, 66.6, 44.5, 80.1, 69.05, 20.4, 19.7, 50.1, 83.45, 86.65, 20.15, 19.4, 60.05, 20.35, 94.05, 84.1, 78.75, 55.55, 20.1, 70.3, 53.65, 20.75, 103.4, 50.8, 79, 74.6, 96.5, 20.1, 19.4, 77.55, 20.05, 19.85, 20.2, 67.45, 18.55, 29.75, 24.2, 23.55, 20.45, 92.3, 53.65, 39.65, 54.65, 104.8, 29.3, 83.85, 103.65, 99.05, 73.35, 100.05, 20.35, 43.95, 23.5, 70.7, 94.3, 29.15, 20.85, 37.7, 92.45, 44.15, 36.05, 50.25, 109.75, 20.3, 112.35, 94.3, 41.15, 74.65, 48.25, 76.15, 71.1, 96.55, 79.3, 89.6, 20.5, 106.3, 100.35, 85.6, 106.15, 51.1, 19.9, 25.7, 99.4, 69.7, 98.35, 85.45, 95.9, 100.75, 89.2, 25.75, 84.1, 79.3, 107.05, 20.05, 19.5, 45.3, 115.15, 72.95, 19.65, 19.55, 89.55, 50.35, 87.25, 20.8, 109.25, 20.35, 55.9, 79.2, 24, 101.35, 35.45, 79.4, 35.2, 19.65, 49.85, 68.75, 61.9, 79.9, 89.75, 19.4, 93.65, 19.9, 72.9, 25.6, 19.75, 55.7, 117.5, 19.85, 78.9, 20.65, 19.65, 79.75, 79.95, 29.9, 19.75, 45, 44.8, 51.1, 53.15, 24.7, 109.95, 20.8, 25.6, 108.4, 19.55, 85.1, 69.05, 70.15, 111.15, 89.35, 89.1, 91.25, 90.35, 105.55, 19.1, 20.4, 100.45, 85.7, 94, 69.85, 25.85, 71.1, 93.35, 50.55, 81.3, 20.7, 79.05, 19.05, 19.6, 20.2, 20.9, 103.6, 38.8, 88.4, 79.7, 19.3, 55.75, 19.95, 89.65, 45.85, 55.95, 69, 83.55, 65.7, 94.9, 61.9, 20, 67.7, 25.15, 92.85, 111.3, 60.6, 65.5, 19.95, 74.6, 94.6, 81.15, 89.05, 49.2, 19.45, 104.3, 86.05, 25.2, 35.15, 99.65, 105.35, 24.3, 80.7, 89.85, 61.1, 29.05, 99.7, 46, 80.4, 100.05, 94, 68.95, 68.45, 69, 43.85, 44.5, 18.7, 53.55, 114.6, 20.1, 85.5, 108.75, 97.85, 19.55, 84.05, 89.4, 19.7, 79.85, 74.45, 74.1, 18.8, 64.4, 55.8, 20.05, 99.15, 56.75, 104.15, 110.8, 35.75, 69.9, 89.2, 55.65, 50.7, 20, 19.1, 45.55, 101.05, 103.7, 36.25, 49.4, 19.9, 19.8, 45.05, 64.55, 86.25, 19.75, 89.1, 95.55, 102.6, 56.3, 94.2, 43.05, 94, 98.85, 64.35, 72, 49.7, 80.7, 24.2, 65.45, 74.35, 83.2, 25, 40.2, 108.35, 69.5, 76, 93.6, 100.55, 24.45, 89.55, 76.1, 80.5, 20.55, 105.4, 35.75, 95.1, 19.3, 63.1, 84.95, 93.4, 89.2, 85.2, 49.95, 20.65, 20.15, 19.2, 104.95, 103.5, 84.8, 95.05, 73.35, 64.1, 44.4, 20.05, 60, 69.5, 78.7, 60.65, 21.15, 84.8, 103.2, 29.6, 105.65]}, {"box": {"visible": true}, "hoverinfo": "y", "line": {"color": "#3381ff"}, "name": "Chu<PERSON>", "points": "all", "type": "violin", "y": [53.85, 70.7, 99.65, 104.8, 103.7, 55.2, 39.65, 20.15, 99.35, 30.2, 64.7, 69.7, 106.35, 97.85, 80.65, 99.1, 80.65, 95.45, 94.4, 79.35, 75.15, 78.95, 21.05, 98.5, 110, 96.75, 76.5, 100.25, 74.4, 78.05, 58.6, 35.45, 44.35, 70.45, 71.15, 45.65, 95, 82.4, 70.9, 45.3, 104.4, 94.85, 74.45, 76.45, 29.95, 84.5, 79.25, 24.8, 91, 79.9, 106.6, 46, 70.15, 50.05, 55.2, 84.6, 54.4, 95, 74.4, 48.55, 70.4, 40.2, 44.6, 41.15, 106.9, 19.35, 94.45, 24.8, 70.6, 85.4, 105.05, 95.15, 70, 74.5, 44.85, 76.1, 73.6, 95.45, 74.9, 80.6, 80.3, 93.15, 82.45, 70.35, 73.85, 80.6, 75.8, 104.6, 103.4, 90.4, 84.8, 41.9, 80.25, 30.75, 96.5, 85.65, 104.95, 50.65, 90.85, 19.95, 85.45, 73.95, 99.45, 19.9, 19.6, 81.35, 83.3, 75.3, 19.4, 45.4, 105.9, 69.55, 81.05, 101.15, 99.8, 55.95, 55, 74.7, 80.25, 96.1, 69, 45.3, 83.55, 74.35, 74.4, 43.75, 28.45, 99.7, 94.1, 94.2, 80.5, 74.35, 104.8, 75.2, 75.6, 100.05, 85, 86.05, 45.55, 86.3, 80.35, 100.25, 100.3, 19, 75.3, 89.2, 85.7, 61.65, 105.25, 29.95, 65, 20, 90.05, 110.75, 105.5, 104.55, 85.25, 56.15, 89.55, 94.55, 45.7, 89.5, 69.55, 74.6, 19.65, 19.85, 24.25, 69.65, 45.65, 75.05, 49.15, 34.7, 80, 49.25, 75.1, 50.15, 79.85, 19.55, 85.95, 45.35, 94.5, 91.7, 87.25, 98.55, 98.55, 85.9, 89.25, 70.3, 93.35, 19.9, 88.9, 95.8, 82, 45.35, 52.2, 110, 96.75, 98.5, 85.35, 101.3, 69.55, 103.25, 104, 86.2, 111.2, 89.45, 95.6, 90.95, 108.55, 78.85, 44.4, 100, 19.8, 89.9, 100.15, 50.8, 73.65, 95.1, 94.65, 80.6, 85.55, 50.8, 105.1, 110.1, 83.9, 100.55, 103.85, 24.6, 98.9, 98.3, 93.85, 100.5, 72.85, 73.55, 79.4, 81.15, 84.6, 79.05, 74.4, 99.05, 44.95, 44.7, 45.7, 100.3, 19.25, 96, 90.55, 30.35, 108.05, 69.9, 103.75, 86.6, 80.6, 85.3, 70, 94.3, 95.35, 75.5, 90.1, 68.95, 99.55, 57.45, 53.65, 100.6, 83.75, 88.3, 92.1, 79.45, 90.45, 69.75, 19.65, 43.65, 39.5, 97.1, 80, 84.7, 89.55, 90.6, 90.05, 99.05, 69.75, 49.05, 98.05, 114.5, 80.95, 74.3, 89.7, 100.45, 90.4, 56.15, 94.4, 78.95, 44.85, 105.65, 74.65, 64.7, 104.05, 35.55, 95.15, 96.65, 80.4, 31.35, 89.75, 94.4, 19.85, 109.9, 101.35, 60.05, 60.15, 90.45, 94.25, 74.9, 80.25, 69, 66.35, 86, 80.3, 90.55, 75.9, 45.85, 49.95, 78.3, 76.95, 96.15, 45.3, 19.4, 90.15, 45.05, 75.8, 79.3, 30.9, 95.25, 89.6, 110.15, 89.5, 75, 44.75, 44.05, 85.6, 115.55, 86.6, 85.2, 97.65, 109.55, 89.55, 19.45, 20.35, 69.25, 99.5, 25.2, 45, 20.15, 105, 54.7, 87.25, 79.95, 88.35, 94.75, 95.05, 78.45, 70.2, 41.05, 85.6, 79.2, 70, 49.95, 69.25, 94.25, 73, 100.05, 99.8, 35, 76, 93.85, 84.3, 84.4, 101.1, 50.45, 99.95, 91.4, 75.55, 80.8, 100, 20.55, 85.3, 70.4, 98.8, 74.4, 98.75, 106, 104.7, 96.7, 55.05, 88.2, 19.75, 75.65, 74.7, 76.65, 25.8, 42.6, 68.85, 90, 75.35, 100.85, 69.95, 107.5, 45.85, 106.1, 91.7, 95.6, 74.95, 95.35, 45, 99.5, 98.6, 59.5, 80.45, 77.95, 74.7, 88.8, 90.25, 64.65, 89.05, 87.4, 94.75, 86.45, 98.25, 75.75, 85.35, 106.1, 45.05, 109.8, 84.65, 79.5, 85.8, 79.1, 44.3, 105.95, 69.75, 94.65, 96.05, 50.15, 113.6, 78.9, 60.05, 34.7, 85.3, 102.45, 104.4, 99.75, 74.4, 74.25, 59.85, 69.6, 45.4, 19.5, 69.9, 87.15, 84.75, 89.95, 113.2, 90.5, 79, 20.15, 71.65, 20.35, 84.25, 78.1, 89.65, 98.7, 76.35, 79.15, 85, 85.3, 86.55, 73.85, 44.85, 45.1, 96, 20.05, 108.65, 45.55, 35.1, 46.2, 45.15, 43.3, 57.15, 73.2, 85.35, 45.95, 83.75, 70.05, 86, 100.5, 72.65, 21, 45.1, 50.4, 78.95, 94.45, 84.8, 50.9, 99.8, 107.35, 19.55, 79.1, 25.5, 80.75, 91.85, 75.35, 75.45, 49.55, 78.6, 81.1, 74.95, 93.55, 102.1, 90.9, 29.2, 89.15, 108.85, 84.75, 45.7, 69.95, 44.6, 74.95, 95.25, 89.85, 100.45, 47.15, 80.2, 87.1, 75.9, 98.75, 86.45, 45.3, 104.1, 81, 90.6, 88.15, 20.2, 90.8, 79.35, 96.8, 83.35, 49.4, 79.2, 48.7, 95.6, 59.75, 108.15, 71.35, 20.15, 69.75, 93.2, 80.85, 33.65, 79.95, 19.3, 99.2, 85.2, 75.25, 54.35, 24.95, 42.35, 75.3, 94.8, 70.55, 86.15, 85, 80.55, 75.8, 98.5, 95.25, 74.4, 85.15, 88.55, 54.75, 91.3, 79.5, 70.1, 111.3, 95.25, 86.25, 100.8, 86.3, 89.95, 76.45, 70, 64.4, 102.45, 80.1, 80.2, 98.15, 112.95, 70.9, 86.85, 99.85, 74.5, 109.15, 65.2, 99.5, 71.55, 93.9, 108.4, 48.75, 85.65, 106.7, 25.15, 110.35, 103.25, 47.8, 98.6, 83.05, 70.4, 104.75, 94.25, 45.95, 70.15, 85.55, 70.3, 106.35, 103.7, 88.35, 94.2, 96.25, 70.7, 33.55, 94.55, 112.95, 35.9, 99.25, 24.45, 103.35, 84.8, 99.95, 90, 104.85, 106.1, 74.2, 89.35, 77.15, 35.25, 48.55, 98.4, 93.85, 100.7, 80.45, 55.25, 54.45, 75.35, 75.7, 74.25, 89.7, 96.4, 69.5, 85.25, 19.4, 83.3, 80.1, 62.7, 25.3, 98.15, 93.7, 105.95, 82, 95.75, 94.05, 54.75, 95.65, 94.6, 98.9, 76.05, 69.75, 34.5, 105.55, 30.1, 70.3, 80.2, 94.35, 44.6, 19.6, 110.45, 70.7, 99.25, 84.8, 105, 54.45, 85.3, 83.35, 33.5, 90.35, 90.75, 89.6, 49.25, 19.75, 95.15, 101.35, 70.05, 74.65, 50.4, 79.65, 79.85, 78.75, 80.45, 50.1, 76.4, 98.7, 100.8, 90.1, 51.55, 79.25, 94.85, 85.7, 44.4, 89.5, 86.05, 44.55, 35.4, 95.65, 20.95, 34.7, 19.75, 92.55, 60.05, 74.3, 85.35, 74.3, 98.1, 109.65, 74.55, 100.95, 94.9, 104.9, 24.1, 104.05, 88.35, 94.65, 74.4, 79.9, 69.7, 98.1, 20.5, 99.6, 81.95, 69.35, 91.1, 86.1, 94.7, 85.1, 99.15, 102.95, 45.05, 71, 70.55, 79.7, 59, 60.35, 69.25, 70.95, 89, 99.4, 73.5, 95.5, 92.35, 74.55, 83.8, 54.45, 99.8, 105.85, 75.45, 93.85, 100.75, 31, 80, 70.25, 100.15, 94.5, 33.9, 89.95, 84.45, 79.5, 89.15, 74.95, 74.4, 55.3, 55.15, 84.95, 43.25, 48.35, 79.55, 71.05, 84.5, 108.6, 30.3, 30.4, 45.4, 84.15, 19.75, 85.4, 75.2, 55.85, 106.4, 84.4, 56.25, 85.55, 68.65, 77.85, 105.35, 87.05, 53.5, 25.1, 79.65, 54.2, 19.45, 103.4, 84.45, 85.95, 59.4, 30.6, 99.55, 70, 45.3, 29.85, 95.1, 19.8, 76.65, 99.15, 44.95, 49.55, 95.9, 81.5, 84.1, 36.45, 25.05, 49.85, 55.55, 69.5, 104.3, 93.85, 50.45, 74.65, 94.2, 106.55, 20.1, 83.8, 55.15, 86.65, 106.75, 78.45, 87.45, 70.75, 101.35, 81.7, 68.25, 79.15, 95.45, 100.95, 69.1, 69.6, 103.45, 77.95, 78.45, 29.5, 70.75, 84.45, 85.65, 70.15, 97.8, 46.3, 106.3, 89.4, 83.15, 74.15, 99.5, 44.15, 53.9, 85.45, 50.85, 59.2, 83.2, 54.9, 103.9, 19.25, 75.45, 54.85, 95.1, 75.5, 73.75, 96.05, 88.95, 50.7, 53.4, 101.9, 83, 50.6, 89.5, 69.8, 93.8, 101.05, 94.7, 104.15, 61.45, 90.55, 88.8, 85.2, 91.4, 106.15, 45.3, 73.25, 51.05, 85.35, 75.35, 75.2, 20.9, 43.3, 109.5, 84.8, 95.05, 50.35, 74.6, 19.7, 59.45, 105.2, 45.15, 93.8, 96.2, 79.6, 85.25, 80.5, 89.8, 50.75, 89.65, 65.4, 72.9, 40, 99.45, 86.25, 45.65, 70.1, 70.2, 55.35, 84.2, 45.85, 75.25, 100.15, 105.75, 48.35, 24.4, 20.95, 49.55, 20.25, 50.15, 95.9, 25.25, 45.15, 84.4, 75.1, 70.15, 101.75, 45.8, 20.1, 40.65, 101, 69.1, 75, 100, 71.15, 54.15, 108.75, 49.15, 19.4, 79.15, 80.4, 102, 20.65, 70.8, 89.05, 96.6, 104.5, 50.6, 49.35, 94.25, 68.95, 93.85, 76.05, 24.45, 89.35, 80.35, 55.8, 79.05, 95, 25.2, 98.4, 79.4, 101.4, 89.8, 109.75, 19.3, 91.25, 49.1, 65.25, 85.45, 94.1, 71.65, 104.65, 74.25, 74.2, 101, 100.2, 89.05, 70.1, 105.65, 96.5, 70.85, 85.95, 73.9, 109.45, 100.2, 74.05, 101.55, 111.95, 55.95, 45.75, 74.95, 80.15, 109.25, 35.2, 78.8, 106.4, 49.4, 69.1, 90.2, 93.55, 80.85, 82.05, 20.55, 78.55, 78.65, 42.9, 34.25, 58.5, 96.2, 74.4, 105.3, 73.85, 84.55, 24.75, 85.8, 24.45, 105.7, 24.25, 76.5, 93.5, 101.5, 74.9, 69.6, 24.05, 50.55, 100.55, 20.5, 74.8, 24.45, 71, 40.3, 50.6, 84.85, 65.7, 70.5, 94.85, 19.75, 65.45, 98.7, 67.5, 69.6, 84.35, 100.2, 78.05, 73.6, 74.75, 20.2, 93.55, 25.05, 95.55, 39.3, 20.25, 23.9, 85.45, 99.9, 84.8, 75.2, 49.35, 74.9, 101.25, 18.95, 28.6, 24.35, 98.65, 79.9, 103.3, 96.2, 104.15, 102.95, 90.6, 50.8, 90.05, 75.85, 68.5, 73.5, 69.95, 70.05, 94, 61.15, 84.45, 75.3, 109.55, 19.6, 66.4, 100.05, 102, 90.2, 25.15, 79.85, 49.6, 105.95, 91.85, 40.1, 73.9, 89.8, 72.25, 96.55, 40.1, 101.25, 79.55, 84.05, 89.1, 94.4, 78.25, 44.15, 75.75, 19.1, 80.3, 74.9, 80.85, 24.75, 90.85, 79.7, 57.55, 86.5, 103.95, 25.1, 48.95, 83.55, 89.15, 70.45, 60.3, 24.75, 103.3, 78.5, 19.85, 79.9, 99.7, 58.6, 98.5, 50.45, 94.65, 100.25, 88.45, 69.85, 81.7, 79.9, 25.4, 83.75, 20.9, 69.4, 74.9, 74.15, 70.1, 78.3, 96.8, 94.4, 84.3, 92.45, 99.55, 81.25, 19.95, 99.25, 80.45, 79.35, 79.75, 100.05, 69.35, 85.8, 79.7, 50.55, 80.55, 84.5, 84.3, 67.75, 85.25, 80.05, 20, 48.8, 85.05, 102.8, 99.95, 117.8, 79.5, 79.65, 94.7, 92.5, 19.45, 75.55, 70, 74.8, 104.4, 86.2, 51.2, 19.5, 74.9, 74.9, 100.25, 102.8, 100, 99.85, 94.2, 83.85, 88.3, 104.05, 94.7, 100.65, 80.55, 78.85, 44.45, 84.9, 20.25, 74.35, 62.05, 80, 104.1, 39.1, 76.5, 49.2, 23.15, 80.85, 60.1, 78.95, 45.15, 79.4, 53.95, 99.3, 80.65, 71.1, 106.25, 20.8, 94, 89.85, 46, 66.25, 90, 70.45, 19.75, 53.95, 105.5, 81, 69.8, 73.75, 54.55, 76.25, 93.9, 70.4, 91.15, 70.95, 99.5, 83.9, 117.45, 104.4, 29.4, 44.65, 25.25, 84.05, 85.7, 74.7, 90.8, 106.15, 85.35, 90.85, 35.75, 46, 89.4, 19.8, 50.9, 81, 98.35, 51, 91.65, 90.85, 69.4, 94.45, 20.15, 95.7, 74.95, 47.95, 63.6, 36.85, 44.1, 50.75, 104.4, 39.3, 106.45, 96.05, 75.1, 44.7, 55, 74.35, 104.7, 55.7, 19.85, 70.3, 85.55, 94.5, 85.35, 79.65, 79.6, 90.6, 20.2, 106.05, 99.15, 55.9, 82.35, 40.4, 75.35, 85.15, 93.8, 20.4, 94.7, 30.5, 77, 20.1, 75.3, 99.2, 85.5, 20.05, 70.4, 49.75, 75.3, 94, 118.35, 30.45, 96.1, 104.25, 80.2, 104.2, 69.65, 80.8, 80.3, 84.1, 69.35, 79.6, 36.45, 89.4, 80.35, 40.65, 60.45, 84.9, 73.55, 20.15, 34.7, 107.55, 98.75, 101.15, 88.7, 79.75, 94.55, 69.05, 96.25, 101.1, 104.7, 55.25, 90.1, 53.75, 89.3, 95.2, 45.7, 74.5, 79.65, 101.3, 80.05, 89.2, 83.65, 19.5, 51.25, 45.6, 49.65, 20, 34, 92.5, 88.35, 107.95, 74.45, 64.75, 89.8, 95.5, 29.65, 84.5, 90.7, 70.3, 103.95, 74.75, 50.8, 80.25, 78.9, 84.75, 85.45, 19.9, 99.45, 70.6, 81, 84.85, 89.15, 24.8, 105.65, 80.85, 89.45, 72.15, 76.4, 61.6, 79.9, 96.15, 65.3, 19.65, 84.65, 69.55, 31.05, 51, 44.35, 79.7, 55, 84.85, 73.65, 73.9, 77.75, 84.35, 84.95, 73.85, 51.8, 46, 79.4, 71.8, 88.4, 46, 51.75, 94.4, 70.15, 34.7, 81.1, 20.55, 106.6, 106.75, 89.45, 97.9, 55.7, 79.8, 39.05, 83.9, 69.85, 53.55, 93.9, 113.15, 84.95, 80.5, 19.3, 49.65, 54.35, 88.45, 90.5, 94.85, 69.95, 48.6, 55.8, 80.15, 75.5, 20.2, 74.75, 104.05, 96.65, 20.05, 103.45, 19.9, 46.35, 19.95, 79.6, 75.9, 76.2, 19.25, 84.3, 79, 70.25, 84.6, 24.1, 60, 86.55, 20.85, 50.75, 105.5, 85.15, 54.2, 109.5, 104.4, 105.1, 75.7, 90.8, 97.95, 108.15, 80.5, 104.3, 71.35, 89.4, 20.6, 99.95, 81, 70.15, 70.15, 96.55, 95.55, 69.65, 89.3, 87.55, 96.3, 85.2, 35.1, 81.3, 19.6, 85.45, 71, 85.05, 44.4, 70.3, 79.35, 90.8, 70.45, 45.25, 75.8, 71.3, 69.1, 88.25, 70, 97.35, 25.9, 55.65, 69.65, 100.6, 86, 106.95, 29.6, 59.9, 95.6, 41.6, 100.85, 40.2, 84.5, 78.95, 20.2, 85.2, 98, 94.45, 105, 93.85, 84, 48.75, 84.85, 56.65, 73.9, 24.5, 89.9, 78.55, 99.45, 25.2, 74.05, 55, 89.75, 85.15, 94.6, 94.25, 72.45, 105.2, 82.85, 103.85, 83.6, 108.45, 91.5, 80.85, 49.05, 105.3, 88.95, 110.85, 114.2, 36.5, 70.75, 40.15, 94.45, 97.35, 45.3, 94.95, 44.55, 23.45, 29.7, 101.1, 50.65, 55.4, 90.6, 86.85, 99.05, 111.25, 55.1, 102.25, 95.9, 95.05, 53.85, 104.2, 50.25, 99.5, 71, 93.2, 19.35, 19.85, 104.2, 79.25, 44.9, 70.1, 29.65, 77.85, 54.3, 95.15, 110.85, 80.5, 72.2, 51.5, 89.55, 77.5, 95.25, 85, 85.75, 98.25, 25.7, 69, 46.05, 76.4, 100.5, 40.05, 69.95, 93.3, 49.15, 35.05, 73, 83.85, 70.1, 70.65, 20.35, 99, 83.95, 116.2, 93.7, 19.7, 75.55, 77.65, 95, 105.4, 95.85, 79.15, 19.65, 79, 91.85, 106, 49.9, 100.35, 74.95, 49.25, 103.7, 89.75, 20.3, 101.95, 94, 89.3, 74.3, 44.65, 84.05, 80.7, 101.7, 20.4, 102.6, 106, 55.1, 106.5, 91.05, 77.5, 54.2, 69.25, 50.5, 74.4, 79.45, 104.65, 100.05, 100.55, 95.65, 94.4, 55, 101.45, 67.95, 69.5, 18.85, 69.2, 69.65, 101.85, 82.3, 79.25, 84.6, 93.4, 94.2, 25.05, 25.1, 48.95, 45.3, 85.85, 95.2, 100.6, 55.3, 74.85, 102.35, 115.65, 79.85, 76, 104.75, 74.65, 56.25, 106.1, 79.75, 68.65, 20.25, 100.2, 74.9, 70.85, 69.8, 54.55, 70.75, 94.1, 29.7, 110.9, 105, 25.3, 55.15, 20.1, 69.9, 101.4, 80.8, 62.05, 76.45, 91.3, 95.75, 62.65, 74.5, 102.1, 50.15, 86.5, 81.45, 69.15, 79.55, 95.5, 91.05, 79.2, 45.25, 74.3, 74.1, 100.6, 75, 70.2, 70.75, 50.25, 79.2, 96, 100.1, 56.5, 85, 59.3, 49.4, 55, 69.2, 62.3, 92.5, 69.65, 111.6, 48.55, 20.2, 39.65, 24.9, 56.7, 105.95, 74.95, 29.7, 50.35, 47.85, 70.3, 98.8, 99.85, 80.3, 80.45, 86.8, 84.2, 99, 100.75, 91.75, 79.55, 111.1, 89.1, 101.9, 91.65, 88.85, 25.3, 95.45, 91.1, 54.15, 69.7, 89.5, 35.15, 73.75, 101.35, 55.9, 105.9, 43.95, 45.1, 70.25, 55.35, 103, 103.75, 69.35, 73.85, 75.15, 69.6, 80.15, 30.5, 98.3, 107.4, 82, 75.4, 101.25, 89.5, 74.4, 20.5, 74.35, 99.75, 111.95, 39, 94.1, 95.65, 88.05, 66.5, 35.45, 49.9, 104.5, 75.05, 81, 74.45, 60.4, 70.65, 59.8, 44.2, 75.75, 102.95, 74.4]}, {"line": {"width": 0.65}, "marker": {"color": "#3381ff"}, "name": "Not Churned", "type": "box", "y": [29.85, 56.95, 42.3, 89.1, 29.75, 56.15, 49.95, 18.95, 100.35, 105.5, 113.25, 20.65, 106.7, 90.05, 19.8, 59.9, 59.6, 55.3, 90.25, 96.35, 95.5, 66.15, 20.2, 45.25, 99.9, 74.8, 49.55, 69.2, 20.75, 79.85, 76.2, 84.5, 49.25, 79.75, 64.15, 90.25, 69.5, 74.85, 99.65, 108.45, 24.95, 107.5, 100.5, 89.9, 42.1, 54.4, 75.3, 78.9, 79.2, 20.15, 79.85, 49.05, 20.4, 111.6, 24.25, 64.5, 110.5, 55.65, 54.65, 74.75, 25.9, 50.55, 103.8, 20.15, 99.3, 62.15, 20.65, 19.95, 33.75, 82.05, 74.7, 84, 111.05, 100.9, 66.85, 21, 20.2, 19.45, 95, 45.55, 24.3, 104.15, 30.15, 94.35, 19.4, 57.95, 91.65, 54.6, 89.85, 31.05, 20.65, 85.2, 99.8, 20.7, 50.7, 20.85, 88.95, 23.55, 19.75, 56.45, 85.95, 50.55, 25.7, 75, 20.2, 19.6, 88.05, 101.05, 84.3, 23.95, 99.05, 19.6, 64.5, 69.5, 68.55, 108.15, 86.1, 19.7, 80.9, 84.15, 20.15, 64.25, 25.7, 56, 69.7, 73.9, 20.6, 19.9, 89.05, 20.4, 84.25, 81.95, 20.55, 24.7, 105.35, 20.55, 45.3, 74.75, 51.8, 30.4, 19.65, 56.6, 71.9, 19.75, 109.7, 19.3, 96.55, 24.1, 111.35, 112.25, 20.75, 101.9, 80.05, 105.55, 78.3, 68.85, 79.95, 55.45, 102.45, 25.25, 19.75, 20, 86.8, 58.75, 45.25, 56.6, 84.2, 80, 24.75, 20.2, 19.35, 50.6, 81.15, 89.9, 85.3, 108, 93.5, 20.25, 25.15, 29.6, 73.15, 19.75, 86.6, 109.2, 74.7, 94.4, 54.8, 75.35, 65, 99, 93.5, 83.7, 19.85, 59.55, 115.1, 114.35, 45, 89.85, 49.85, 113.3, 88.1, 24.9, 105, 24.25, 59.75, 107.05, 64.95, 55, 50.55, 55.15, 51.2, 25.4, 54.45, 76, 44.35, 61.2, 86.8, 89.35, 19.7, 20.25, 76.05, 100.8, 74.55, 64.9, 90.4, 60.3, 81.85, 24.8, 75.55, 101.15, 78.75, 19.25, 89.05, 115.05, 69.35, 110.05, 19.9, 91.5, 60, 44.8, 48.6, 60.05, 102.7, 82.9, 35.9, 82.65, 19.85, 19.2, 94.9, 88.15, 94.8, 54.65, 85.75, 67.45, 20.5, 20.25, 72.1, 19.45, 44.95, 97, 62.8, 44.6, 89.15, 54.1, 105.25, 97.1, 20.2, 98.8, 50.3, 20.55, 75.9, 59.95, 19.15, 98.65, 112.6, 20.6, 35.75, 99.75, 96.1, 85.1, 25.35, 89.65, 86.75, 86.2, 64.8, 108.1, 54.75, 90.4, 44, 95.6, 84.8, 44.3, 19.9, 95.05, 90.05, 109.9, 54.6, 20.05, 19.75, 20.05, 55.9, 19.7, 19.8, 95.4, 93.95, 24.45, 74.95, 87.35, 70.65, 73.25, 98.7, 24.8, 24.3, 69.85, 100.55, 25.7, 40.7, 51.65, 105.1, 85.95, 75.6, 58.25, 65.2, 53.45, 19.75, 44.45, 20.85, 114.05, 89.85, 55.05, 112.95, 101.55, 114.65, 64.8, 80.4, 25.05, 94.75, 105.5, 24.7, 69.75, 60.2, 24.4, 104.15, 92.9, 80.8, 20, 75.1, 19.65, 69.45, 116.05, 40.05, 102.1, 89.7, 19.9, 20.65, 70.05, 53.6, 76.05, 75.7, 19.65, 81.45, 108.5, 84.5, 100.15, 88.6, 52.55, 104.8, 59, 64.05, 20.4, 60.9, 19.8, 116.25, 80.7, 65.2, 84.05, 79.45, 78, 19.85, 94.3, 106.45, 105.45, 95, 54.3, 70.05, 20.05, 105.4, 51.6, 85.5, 91.25, 115.75, 94.7, 19.6, 99.9, 21.1, 20.05, 79.95, 107.15, 89.55, 81.55, 58.45, 95.65, 80.6, 113.1, 58.95, 19.55, 78.95, 105.05, 101.9, 19.75, 110.3, 115.6, 19.35, 25.6, 68.75, 19.9, 70.6, 70.2, 49.3, 107.25, 23.6, 69.7, 99.5, 64.3, 70.85, 101.9, 73.5, 40.4, 19.25, 59.6, 64.9, 110.85, 81.05, 98.05, 70.5, 94.55, 19.65, 19, 20, 63.25, 20.1, 99.15, 90.4, 111.9, 24.9, 83.5, 84.3, 45.6, 54.85, 65.55, 90.35, 20.4, 74.55, 19.95, 74.25, 108.65, 109.55, 86.65, 81, 47.85, 114.55, 20.55, 109.8, 69.5, 48.85, 25.25, 102.85, 87.55, 78.55, 34.55, 92.05, 85.05, 19.7, 95.15, 84.25, 104.6, 111.65, 55, 89.85, 20.35, 54.55, 99.45, 70.9, 25.4, 89.85, 25.25, 69.65, 70, 20.1, 24.8, 95.1, 88.85, 78.8, 20.35, 45.25, 20.05, 69.55, 19.5, 74.75, 30.2, 57.8, 19.85, 25.55, 24.85, 110.35, 24.55, 107.95, 81.4, 73.8, 64.4, 103.75, 71.1, 49.9, 24.6, 30.1, 83.4, 20.45, 75.25, 20.55, 20.05, 20.65, 85.15, 84.95, 66.5, 63.3, 83.15, 84.9, 20.55, 49.25, 59.6, 104.65, 75.3, 80.1, 81, 24.7, 86, 25.4, 89.15, 58.25, 85.65, 50.35, 80.35, 20.2, 20.55, 21.25, 26.25, 80.85, 74.2, 20.35, 75.5, 79.05, 90.15, 50.6, 110.45, 101, 79.35, 89.85, 65, 80.45, 24.1, 44.05, 110.8, 114.95, 75.05, 19.25, 90.05, 56.7, 80.15, 71.35, 20.25, 90.35, 19.7, 19.85, 90.35, 20.8, 66.85, 19.9, 35.8, 78.85, 20.4, 74.25, 64.8, 20.45, 110.65, 40.3, 107, 73.35, 44.8, 54.75, 40.6, 55.3, 60.85, 78.4, 69.65, 59.85, 76.9, 19.85, 67.65, 45, 64.2, 81.7, 25.55, 20, 75.65, 23.8, 64.2, 76.8, 55.2, 108.55, 25.25, 30.4, 20.05, 84.6, 103.7, 88, 106.35, 79.15, 103.1, 63.95, 25.8, 25.55, 44.85, 25.05, 74.1, 88.8, 93.25, 71.4, 79.2, 20.4, 105, 30.85, 20.55, 84.85, 33.15, 92, 89.8, 115.8, 85.15, 24.85, 64.35, 20.5, 86.05, 89, 64.8, 19.8, 93.4, 39, 20.5, 26.4, 98.2, 97.55, 19.95, 99.7, 34.8, 60.15, 64.75, 54.65, 19.3, 111.25, 35.8, 20.05, 84.35, 110.5, 91.2, 89.3, 81.1, 81.2, 94.3, 116.1, 105.55, 94.4, 19.5, 105.6, 81.35, 56.4, 65.35, 19.95, 111.25, 89, 106.1, 20.05, 25.2, 75.4, 65.55, 80.7, 104.55, 24.15, 20.45, 75.4, 79.7, 81.7, 76.3, 103.75, 86.45, 75.1, 80.6, 19.3, 33.6, 83.25, 80.85, 108.05, 19.9, 21.05, 30.15, 79.85, 65.5, 104.1, 20.5, 91.35, 20.5, 75.6, 55.1, 58.95, 95.1, 25.45, 56.75, 81.75, 86.1, 29.8, 20.5, 60.9, 73.25, 20.85, 77.35, 93.85, 70.1, 75.95, 75.25, 54.95, 19.5, 19.6, 47.85, 23.75, 43.8, 19.75, 19.15, 19.6, 80.3, 24.35, 25.25, 26.1, 20, 20.7, 70.3, 69.55, 19.85, 20, 95.85, 20.75, 50.15, 58.65, 95.9, 49.5, 80.1, 24.4, 40.05, 19.5, 51.05, 54.35, 84.7, 86.1, 70.35, 110, 94.9, 69.75, 71.6, 23.65, 81.85, 25.1, 114.7, 49.15, 80.9, 19.3, 70.2, 54.25, 99.3, 74, 50.25, 19.8, 35.5, 80.75, 19.55, 20.05, 112.4, 50.2, 62.25, 55.7, 19.65, 89.25, 54, 56.75, 21.1, 96.65, 24.5, 79.2, 69.55, 20.05, 98.85, 25.75, 19.6, 87.65, 74.75, 107.45, 75.35, 64.95, 100.45, 68.5, 80.55, 81.25, 89.55, 55.7, 24.8, 20, 105.2, 19.55, 79.75, 97.45, 24.25, 24.6, 50.15, 39.6, 89.85, 98.85, 53.85, 24.25, 89.45, 105.25, 59.5, 70.55, 82.5, 61.6, 49.05, 66.25, 19.4, 86.05, 19.15, 19.25, 81.95, 114.65, 20, 19.8, 65.15, 19.65, 88.95, 20.2, 75.2, 56.8, 75.5, 35.6, 60.25, 40.35, 18.85, 54.85, 64.3, 24.65, 76.1, 18.7, 97.95, 94.1, 95.1, 72.35, 82.7, 19.9, 53.8, 51.55, 19.65, 44.05, 114, 100.4, 54.25, 80, 79.2, 94.3, 49.8, 53.75, 93.45, 87.9, 61.05, 104.05, 99.25, 85.7, 104.85, 69.15, 74.45, 50.45, 60, 85.25, 19.45, 20.75, 78.9, 104.5, 49.4, 25, 25.55, 70.15, 69.4, 93.15, 69.55, 20.2, 20.4, 23.75, 70.45, 65.75, 24.6, 69.25, 24.65, 90.4, 100.85, 75.35, 87.2, 64.4, 24.7, 105.85, 98.3, 19.45, 58.7, 20.15, 64.5, 28.5, 90.45, 105.15, 83.15, 103.2, 19.45, 88.8, 85.9, 34.2, 20.15, 50.3, 80.15, 51.25, 95.2, 94.8, 80.25, 76.1, 115.55, 24.65, 53.6, 19.45, 88.2, 101.15, 56.8, 99.4, 20.1, 60.7, 20.95, 114.85, 19.25, 62.8, 105.5, 19.85, 74.1, 107.5, 19.55, 68.8, 84.45, 84.5, 111.2, 80.6, 80.7, 75.6, 57.6, 110.6, 58.2, 81, 19.7, 59.55, 75.55, 45.1, 70.95, 20.9, 19.95, 24.6, 66.7, 94.8, 65.85, 19.95, 24.65, 51.25, 54.25, 19.4, 56.25, 25.15, 23.95, 35.4, 75.35, 20.4, 56.05, 20, 73.05, 20.5, 100.75, 19.95, 49.65, 65.65, 20.45, 60.95, 20.35, 19.5, 75.2, 111.45, 70.15, 92, 85.5, 82.15, 84.4, 60.9, 20.25, 95.3, 19.85, 84.35, 19.85, 82.3, 66.8, 44.6, 98.45, 70.7, 24.95, 102.5, 86.55, 24.3, 58.35, 68.75, 85.8, 20.1, 20.35, 110.8, 82.85, 84.35, 19.55, 19.95, 66.25, 23.3, 25.3, 44.55, 104.1, 92.55, 101.45, 94.55, 95.5, 100.3, 55.5, 49.85, 89.55, 19.15, 99.8, 113.05, 19.95, 74.15, 92, 73.85, 24.45, 24.8, 64.85, 20.75, 68.95, 109.4, 49, 50.25, 19.9, 97.8, 100.3, 55.8, 111.15, 98.55, 50.05, 20.85, 19.5, 19.35, 69.5, 48.8, 94.5, 20.65, 106.05, 108.3, 99.65, 95.9, 20, 64.95, 74.6, 49.2, 73.75, 92.3, 19.2, 88.65, 95.95, 105.4, 20.25, 49.05, 35.55, 65.1, 96.85, 69.75, 99.2, 106.8, 51.25, 57.75, 70.85, 19.55, 79.5, 98.15, 20.25, 79.15, 94.25, 40.2, 19.95, 55.35, 102.15, 71.1, 54.1, 19.65, 88.45, 80.4, 19.25, 84.8, 19.5, 68.6, 92.6, 100.55, 20.55, 19.6, 67.45, 43.55, 109.85, 20.65, 95.4, 21, 56.2, 18.4, 25.75, 19.6, 19.8, 64.2, 75.75, 78.95, 50.3, 80.3, 19.85, 21.1, 50, 104.75, 19.85, 85.9, 80.8, 25.25, 80.55, 81.5, 20.9, 67.25, 20.35, 45.05, 34.65, 69.35, 81.55, 75.4, 67.8, 111.4, 46.3, 20.4, 20.05, 96.1, 19.65, 60.65, 71.7, 36, 65.2, 48.95, 53.5, 80.45, 109.05, 26.3, 106.8, 64.95, 19.35, 21.1, 18.85, 26, 70.35, 96.9, 19.55, 80.4, 94.65, 95.75, 19.55, 104.1, 20.1, 111.55, 60.5, 90.95, 19.7, 50.95, 20.05, 19.4, 59.45, 81.5, 29.05, 70.6, 97.2, 59.2, 75.9, 90.05, 70.95, 102.6, 43.8, 59, 69.95, 24.35, 29.45, 84.4, 20.65, 87.1, 19.85, 90.35, 65.5, 80.95, 56.15, 34.4, 20.75, 18.8, 90.8, 25.6, 70.8, 25.4, 108.8, 76.85, 20.25, 24.8, 115.65, 74.6, 103.15, 72.1, 25.1, 80.15, 25.4, 105.4, 45.75, 24.45, 25, 85.25, 19.6, 50.15, 70.55, 26.4, 20.15, 58.85, 97.55, 19.65, 25.25, 114.45, 70.7, 75.55, 84.8, 20.65, 20.45, 35.65, 90.45, 97.65, 73.85, 69.1, 82.75, 24.4, 55.25, 61.35, 76.75, 19.4, 54.75, 19.7, 19.9, 107.95, 83.8, 56.4, 20.1, 94.9, 94.2, 49.9, 71.05, 81.65, 89.45, 99, 19.05, 114.45, 44.25, 90.55, 20.4, 71.4, 24.85, 104.45, 19.8, 116.45, 20.05, 110.75, 89.7, 48.7, 96.6, 74.3, 54.3, 74.85, 79.95, 20.05, 19.4, 54.9, 24.45, 89.65, 45.4, 75.7, 110.65, 20.55, 115.15, 58.55, 93.25, 19.35, 48.75, 109.05, 25, 54.9, 24.75, 91.15, 104.35, 66.05, 92.2, 105.2, 19.6, 30.4, 61.5, 69.4, 24.75, 91.05, 73.65, 19.4, 26.2, 43.85, 69.7, 38.55, 53.1, 20.65, 64.45, 25.1, 95.15, 79.35, 96.65, 75.5, 19.7, 20.5, 19.2, 98.35, 74.35, 51.35, 45.65, 20.3, 54.2, 90.65, 50.9, 25.05, 74.85, 20.5, 63.55, 47.95, 45, 90.05, 25.3, 24.3, 75.95, 19.7, 66.4, 35.75, 18.8, 19.4, 19.3, 67.45, 20.1, 58.9, 19.45, 50.5, 25.1, 60.7, 99, 104.4, 44.05, 24.1, 45.55, 93.8, 19.7, 70.65, 86.45, 114.1, 95.2, 88.55, 20.75, 44.65, 60.2, 55.45, 70.3, 60.4, 55.8, 31.1, 50.95, 69.1, 43.95, 86.5, 69.95, 90.95, 19.9, 20.15, 90.6, 92, 24.85, 36, 78.5, 19.95, 20.65, 30.5, 106.1, 20.5, 95.5, 64.6, 51.1, 89.1, 54.95, 20.45, 85.95, 60.35, 19.8, 85.35, 72.1, 81.05, 20.5, 111.8, 20.2, 19.7, 19.85, 60.5, 19.55, 20.9, 21.05, 71.5, 54.65, 19.2, 49.8, 20.5, 90.4, 90.25, 104.6, 50.2, 95.5, 95.4, 101.3, 53.1, 84.85, 34.25, 88.6, 60.15, 99.95, 70.7, 54.8, 54.8, 100.3, 53.6, 19.35, 85.6, 80.8, 19.6, 90.7, 69.75, 20, 95.25, 19.95, 80.85, 93.3, 46.35, 78.75, 83.55, 19.6, 67.85, 105.65, 75.5, 20.15, 45.2, 79.25, 85.7, 20.1, 61.8, 49.9, 20.4, 75.4, 108.15, 86.25, 95.7, 116.85, 105.75, 20.15, 19.6, 60.95, 25.05, 60.3, 63.95, 74.3, 70.6, 90.55, 19.45, 64.45, 69.65, 19.5, 110.5, 24.7, 77.4, 85.4, 47.6, 19.4, 103.85, 108.45, 81, 86.65, 92.95, 90.35, 25.15, 76.4, 19.55, 85.35, 24.8, 103.15, 100.75, 94.1, 19.35, 19.9, 101.05, 59.1, 55.85, 106.05, 84.1, 75.3, 24.7, 55.8, 39.7, 29.5, 20.15, 79.55, 24.8, 19.65, 94.05, 90.75, 78.85, 99.5, 80.55, 70.2, 59.45, 93.35, 44.95, 26.1, 20.2, 21.25, 59.4, 95, 61.9, 118.65, 64.45, 80.15, 20.2, 21, 20.45, 75.85, 80.45, 75.5, 44.45, 74.55, 48.15, 19.65, 20.15, 106.6, 91, 25.4, 69.95, 66.85, 20.15, 64.85, 74.85, 50.5, 72.9, 115.05, 19, 19.55, 101.1, 84.1, 24.15, 50.1, 74.6, 19.75, 106.8, 84.5, 25.05, 83.7, 96.6, 101.1, 20.2, 94.05, 81, 60.25, 60.85, 43.95, 86.05, 20.25, 19.4, 102.65, 19.9, 19.55, 95.5, 84.15, 103.2, 50.2, 19.95, 116.25, 31.2, 24.45, 84.2, 85.65, 21.2, 25.55, 20.2, 63.85, 61.95, 25.75, 58.2, 85.85, 104.9, 99.85, 19.55, 104, 104.4, 19.5, 25.25, 49.85, 108.95, 89.9, 82, 89.95, 79.35, 64.05, 101.15, 39.1, 34.6, 19.55, 104.45, 70.5, 20.35, 19.45, 69.9, 59.7, 78.35, 71.45, 45.85, 95.85, 35.7, 89.55, 24.95, 24.85, 100.8, 105.35, 19.65, 54.45, 70.5, 20.1, 69.35, 19.8, 74.4, 93.05, 51.2, 65.6, 80.55, 52.7, 20.85, 52.15, 114.95, 104.45, 113.65, 20.6, 91.55, 49.85, 19.8, 104.15, 48.2, 25.1, 100.15, 55.9, 64.4, 85.3, 107.45, 91.3, 85.95, 45.2, 79.2, 55.5, 90.25, 91.25, 100.9, 97.7, 69.85, 65.6, 104.65, 90.45, 63.7, 104.5, 20.1, 104.3, 93.25, 73.45, 20.7, 25.25, 100.5, 90.6, 89.4, 95.45, 20.45, 19.95, 109.15, 85.7, 102.05, 94.7, 64.4, 26.8, 66.05, 65.2, 85.05, 55.8, 19.95, 45, 114.9, 106.4, 46.1, 39.7, 20.05, 95.75, 24.4, 33.6, 90.45, 84, 67.4, 19.7, 80.35, 19.6, 54.2, 45.2, 75.1, 19.7, 72.75, 20.05, 39.2, 44.75, 82.65, 93.9, 117.15, 99.25, 112.55, 25.7, 90.3, 49.4, 19.4, 109.7, 61.25, 55.3, 103.75, 19.5, 39.5, 26.05, 91.05, 29.65, 50.2, 105.3, 55.45, 85.45, 19.8, 59.25, 90.7, 79.05, 90.7, 95, 30.25, 49.85, 93, 54.55, 19.7, 84.8, 94.45, 20.85, 60, 80.45, 84.95, 49.65, 20.2, 100.5, 35.75, 86.45, 53.8, 38.55, 39.9, 70.05, 20.1, 20.3, 35.65, 82.95, 55.65, 25.2, 50.8, 19.65, 59.8, 73.55, 61.4, 19.9, 19.45, 81.5, 109.55, 74.4, 74.9, 59.65, 110.45, 74.45, 24.55, 24.55, 90.65, 105.05, 20.45, 19.55, 19.7, 70.45, 85.65, 20.55, 97.95, 20, 25.25, 70.9, 19.85, 106.35, 99.5, 84.7, 86.05, 44.55, 75.85, 25, 45, 20.5, 90.45, 60.45, 78.45, 100.55, 20.35, 90.75, 20.25, 20.05, 19.6, 53.8, 70.2, 75.5, 20.35, 26.05, 20.6, 20.1, 24.3, 24.5, 110.5, 25.25, 90.1, 68.75, 19.2, 115.1, 99.65, 91.45, 84.75, 78.75, 20.25, 19.9, 97.75, 100.4, 24.45, 101.1, 50.9, 107.2, 92.2, 113.4, 40.55, 26, 111.95, 53.8, 72.1, 78.85, 70.75, 76.15, 39.1, 69.95, 20.05, 20.05, 19.45, 26.9, 19.2, 50, 60, 84.55, 45.45, 20.05, 115.55, 99, 50.55, 25, 91.55, 19.35, 24.85, 100.4, 25, 19.25, 108.25, 20.15, 101.3, 20, 105.3, 69.85, 65.25, 19.8, 19.6, 20.05, 49.4, 88.4, 100.6, 19.45, 20.3, 107.65, 80.45, 58.85, 109.6, 75.15, 73, 70.1, 98.65, 111.45, 114.9, 100.55, 20.4, 104.35, 80.45, 91.35, 19.9, 68.35, 79.1, 51, 80.55, 66.7, 86.4, 50.05, 25.7, 83.4, 84.65, 64.75, 100.15, 25.25, 113, 40.65, 94.95, 59.9, 19.8, 81.8, 20, 59.6, 25, 84.35, 55.55, 75.35, 59.3, 66.1, 18.8, 86.45, 52.1, 47.4, 109.15, 94.95, 93.55, 79.5, 115.05, 95.15, 105.4, 20.1, 20.05, 20.7, 20.35, 19.7, 85.45, 40.4, 105.2, 100.65, 91, 116.75, 59.1, 49.8, 19.3, 19.65, 81.4, 38.9, 87.95, 19.85, 96.35, 24.15, 19.1, 44, 60.6, 25.65, 53.95, 20.4, 29.35, 20.45, 95.1, 25.25, 44.9, 92.65, 43.7, 72.6, 18.95, 20.5, 19.95, 24.5, 20.6, 61.05, 106.65, 108.25, 20.4, 55.3, 20.25, 72.95, 89.45, 104.65, 75.2, 101.15, 68.75, 111.05, 99, 21, 19.4, 77.2, 19.45, 24.85, 41.35, 19.6, 84.45, 20.25, 19.65, 20.65, 99.3, 81.05, 67.6, 70.15, 115, 84.8, 19.7, 63.15, 74, 29.1, 50.05, 20, 74.65, 44.4, 85.4, 94.1, 108.9, 56.2, 26.1, 85.45, 88.95, 74.35, 48.85, 80.1, 56.05, 89.8, 19.1, 20.35, 106.05, 19.65, 59.85, 86.1, 19.45, 97.1, 36.65, 103.9, 19.75, 24.55, 48.7, 109.55, 20.65, 55.2, 24.05, 20.45, 19.25, 26.35, 43.8, 50.15, 20.45, 61.4, 70.75, 61.15, 20.25, 63.85, 98.7, 20, 19.3, 84.4, 25.1, 48.25, 19.85, 94.2, 62.15, 79.3, 56.25, 20.3, 99, 90.6, 85.9, 79.2, 70.35, 19.35, 50.15, 63.8, 20.55, 88.55, 101.4, 44.6, 63.75, 109.25, 84.6, 20.45, 85.75, 107.95, 22.95, 19.45, 19.7, 87, 79.95, 64, 64.9, 25.75, 90.15, 116.1, 104.95, 50, 20.45, 19.85, 19.95, 26.45, 63.4, 53.95, 95.1, 74.1, 35.5, 79.2, 48.8, 55.45, 25.4, 93.5, 63.9, 64.85, 63.8, 44.45, 19.95, 43.35, 49.65, 85.1, 89.8, 103.05, 116, 69.9, 95.1, 40.25, 25.75, 105.35, 113.6, 24, 19.4, 86.1, 102.65, 92.85, 97.75, 97.95, 19.95, 24.6, 50.95, 75.6, 80.75, 90.4, 60.25, 20.2, 64.15, 20.25, 99, 80.3, 19.55, 100.75, 53.75, 25.6, 58.35, 46.35, 113.75, 90.4, 109.3, 90.3, 65.25, 60.65, 24.1, 19.5, 85.95, 53.5, 25.45, 20.5, 20.85, 89.9, 26, 113.2, 69.05, 20.1, 109.65, 19.2, 90, 34, 20.4, 38.6, 25.25, 60.6, 74.75, 20.6, 20.4, 81.7, 20.3, 20, 25, 80.45, 19.75, 65.65, 71, 89.2, 86.75, 61.5, 25.1, 34.05, 19.95, 19.95, 89.7, 20.4, 26.3, 20.7, 19.45, 110.8, 69.3, 49.35, 20.35, 105.6, 64.45, 49.9, 65.65, 103.3, 44.45, 89.9, 55.05, 104.1, 106.6, 70.5, 19.6, 24.05, 38.1, 34.25, 100.05, 68.65, 45.8, 75.75, 96.4, 20.55, 50.95, 90.5, 79.4, 58.75, 59.45, 105.7, 53.3, 24.3, 59.9, 23.95, 20.15, 95.65, 81, 82.45, 20.5, 54.4, 58.6, 84.8, 61.4, 20.4, 20.15, 94.45, 79.8, 74.05, 49.15, 19.4, 113.65, 106, 25.95, 19.1, 100.55, 95.4, 75.15, 89.15, 107.9, 19.5, 24.95, 19.5, 69.95, 82.85, 19, 38.85, 20.35, 95, 74.4, 78.45, 74.3, 51.05, 19.2, 109.1, 76.45, 72.8, 18.95, 101.75, 75.45, 64.1, 25.65, 75.1, 95.85, 54.4, 72.75, 19.85, 19.05, 94.85, 46.25, 19.35, 69.6, 90.7, 101.4, 20.25, 48.8, 74.35, 19.35, 68.75, 100.2, 20.85, 19.35, 45, 25.5, 48.9, 19.6, 20, 81.3, 95.2, 83.3, 20.3, 89.85, 19.8, 54.65, 29.35, 19.15, 19.1, 80.55, 20.25, 106, 25.5, 79.6, 55.25, 88.05, 20.4, 117.6, 20, 19.65, 70.55, 65.8, 20.05, 80, 35.4, 79.6, 80.25, 20.45, 79.6, 24.7, 77.3, 29.75, 44.9, 29.8, 71.95, 20.75, 56.3, 105.25, 19.55, 84.45, 53.65, 29.9, 19.7, 43.7, 55.3, 19.85, 19.65, 49.45, 20.45, 39.7, 54.5, 111.6, 55.55, 20.55, 62.1, 104.5, 101.8, 110.6, 84.9, 93.2, 24.4, 70.55, 85, 85.8, 91.1, 20.1, 20.05, 74.8, 24.8, 100.85, 105.1, 20.4, 20, 79.4, 57.2, 58.6, 94.8, 102.5, 20.35, 84.9, 69.2, 20.85, 88.5, 35, 55.15, 50.95, 64, 80.2, 49.3, 84.35, 20.05, 117.2, 20.1, 109.95, 94.75, 80, 79.65, 25.2, 19.9, 44.8, 20.3, 19.2, 80.05, 107.35, 47.85, 70.8, 59.1, 25.55, 20.25, 75.55, 95.3, 70.25, 50.3, 19.85, 19.35, 25, 20.3, 75.35, 88, 43.8, 62.05, 20.1, 101.35, 84.05, 20.9, 105.9, 85.05, 44.1, 90.2, 53.45, 19.95, 74.65, 57.5, 19.65, 93.8, 89.25, 94.15, 55.6, 48.7, 104.9, 19.9, 19.4, 25.05, 84.45, 19.3, 79.85, 25.55, 68.4, 20.65, 55.15, 70.6, 19.95, 19, 44.1, 107.6, 61.55, 90.7, 99.25, 91.7, 100.7, 78.45, 84.3, 19.55, 20.45, 55.6, 86.8, 20.95, 20.05, 113.65, 59.5, 87.8, 41.9, 69.85, 56.3, 109.55, 92.15, 69.5, 97, 58.35, 70.4, 94.3, 19.55, 95.95, 94.8, 107.75, 54.6, 71.3, 19.5, 56.3, 90.55, 60.8, 98.8, 98.15, 35.35, 103.15, 107.75, 81.4, 95.7, 104.8, 70.95, 44.95, 97.65, 35.65, 85.25, 19.5, 25.1, 100.05, 55.7, 91.15, 83.85, 45.9, 25.1, 19.7, 91.5, 51.3, 21.1, 104.75, 85.75, 20.3, 100.75, 74.15, 78.55, 19.85, 50.7, 45, 77.8, 83.45, 94.8, 20.1, 59.9, 90.1, 70.95, 29.2, 46.6, 74.3, 69.3, 94.3, 76.45, 54, 104.25, 19.95, 24.95, 84.75, 19.75, 113.65, 44.9, 75.25, 24.6, 25, 20.95, 110.6, 55.5, 19.45, 84.85, 19.6, 53.45, 19.8, 112.1, 74.2, 69, 19.35, 19.8, 109.2, 79.15, 53.65, 100.2, 108.65, 40.65, 55.35, 105.6, 95.7, 83.2, 90.05, 97.65, 68.05, 102.1, 23.4, 71.05, 19.45, 59.45, 92.2, 19.85, 43.9, 90.5, 90.45, 84.6, 99.15, 19.95, 20.5, 62.1, 79.5, 19.55, 20.35, 51.7, 23.3, 65.1, 81.2, 74.5, 80.5, 60.3, 75, 90.15, 69.05, 59.7, 19.85, 40.75, 95.7, 46.3, 81.3, 20, 66.15, 19.6, 49.8, 101.75, 55.15, 103.95, 99.65, 73.7, 50.05, 60.25, 87.3, 54.25, 85.3, 50, 90.95, 72.25, 96.1, 19.85, 55.3, 20.1, 69.5, 25.15, 79.65, 71.25, 113.8, 24.55, 19.7, 100.5, 74.45, 104.1, 19.05, 25, 19.05, 81.9, 69.7, 90.15, 25.35, 24.65, 19.55, 60, 89.9, 19.4, 49.8, 24.1, 54.25, 109.9, 35.5, 87.55, 88.4, 50.8, 99, 96.55, 59.75, 111.5, 24.25, 20.5, 70.4, 30.55, 84.9, 54.5, 75.35, 44.45, 98.05, 63.9, 69.15, 64.65, 98.85, 89.6, 83.25, 70.25, 24.5, 20.1, 73, 61.4, 84.3, 19.9, 20.4, 50.75, 20.45, 75.75, 65.4, 59.75, 78.5, 48.95, 99.65, 18.25, 54.55, 40.65, 20.45, 24.8, 88.8, 20.05, 69.8, 77.15, 35.05, 108.1, 84.05, 20.2, 49.2, 24.6, 71.65, 104.9, 106.5, 75.5, 58.5, 78.9, 79.2, 109.45, 59.2, 29.15, 20.05, 66.5, 49.55, 73.6, 82.65, 49, 25.2, 25.45, 110.9, 77.75, 26.2, 19.9, 80.85, 56.35, 19.3, 50.4, 55.25, 19.1, 84.05, 105.2, 75.75, 95.3, 19.85, 69.1, 20.25, 54.75, 81.45, 80.2, 100.3, 90.95, 20, 79.85, 73.55, 19.3, 20.15, 44.55, 54.45, 19.65, 105, 88.7, 75.15, 20.25, 109.1, 30.75, 112.9, 94.05, 78.85, 55.3, 19.35, 25.35, 20.45, 19.35, 78.65, 74.75, 19.9, 58.35, 100.5, 20.05, 25.65, 95, 45.45, 20, 49.2, 83.25, 19.25, 19.65, 72.8, 109.65, 65, 114.1, 20.65, 86.95, 94.75, 25.35, 105.45, 25.4, 102.55, 24, 25.6, 73.5, 98.25, 54.4, 103.1, 34.2, 43.75, 100.65, 116.05, 82, 65.15, 44.8, 79.8, 88.85, 74.95, 106.85, 19.3, 56.1, 19.7, 51.3, 118.6, 24.15, 20.3, 115.5, 25.05, 109.1, 19.65, 111.3, 29.9, 80.6, 20.8, 89.95, 116.05, 19.55, 115.25, 24.8, 19.9, 81.25, 69.95, 86.4, 66.3, 94.65, 72.1, 34.7, 95.95, 44.8, 109.4, 71.05, 19.7, 40.25, 19.85, 68.25, 20.15, 50.95, 25.15, 20.25, 44, 20.25, 55.8, 88.9, 57.65, 79.15, 108.05, 94.8, 45.9, 102.6, 61.35, 57.55, 29.25, 19.6, 111.75, 106.5, 107.7, 19.3, 20.05, 69.95, 63.7, 50.9, 60.4, 79.25, 110.1, 90.7, 25.3, 85.2, 24.35, 25.1, 54.55, 96.6, 81.15, 38.5, 92.9, 84.7, 66, 20.75, 61.45, 54.5, 99.75, 109.75, 80.85, 20.3, 67.8, 19.8, 25.7, 56.15, 86.7, 20.4, 19.65, 54.35, 108.1, 54.45, 45.35, 59, 69.45, 64.95, 18.85, 19.8, 25.05, 114.3, 109.2, 45.05, 51, 110.45, 84.65, 60.05, 44.65, 93.25, 20.25, 25.45, 20.6, 94.1, 34.8, 60.75, 51.35, 64.05, 84.8, 50.15, 94.6, 59.75, 100.25, 98.9, 97.7, 60.25, 56.25, 46.2, 24.9, 63.35, 50.1, 50.15, 64.65, 79.6, 19.5, 99.55, 74, 38.9, 79.55, 46.3, 99.35, 95.8, 78.15, 26.1, 40.35, 79.2, 20.9, 49.9, 68.9, 20.25, 76, 74, 82.3, 89.4, 99.15, 29.45, 19.8, 59.15, 44.75, 90.8, 49.55, 106.7, 94.45, 19.45, 67.95, 65.25, 99.45, 20.35, 19.95, 77.4, 19.7, 99.7, 74.8, 19.15, 78.95, 62.85, 71.55, 94.95, 86.1, 19.55, 24.8, 84.05, 36.25, 98.6, 103.65, 92.9, 19.9, 20.1, 80.5, 39.85, 60.5, 103.85, 67.8, 24.85, 19.35, 89, 55, 76.15, 20.3, 117.35, 19.75, 45.2, 25.2, 89.75, 75, 49.95, 65.7, 67.05, 110.9, 87.95, 19.8, 75.7, 62.15, 115.15, 19.5, 86.55, 20.4, 19.8, 45.65, 56.4, 73.3, 101.35, 33.6, 20.7, 104.05, 20.25, 73.7, 108.75, 20.15, 19.75, 25.95, 70.05, 24.05, 84.75, 23.05, 59.95, 19.55, 19.6, 20.05, 85.55, 78.6, 116.8, 43.55, 60.8, 54.9, 65.2, 108.2, 92, 75.1, 25.05, 75.15, 19.5, 19.3, 112.2, 70.3, 19.6, 20.25, 80.65, 115.75, 80.6, 59.55, 19.05, 95.65, 19.95, 19.4, 36.1, 19.75, 64.1, 19.75, 19.7, 110.2, 106.35, 90.55, 65.9, 104.5, 52.5, 56.1, 88.75, 26, 99.4, 73.15, 54.65, 115.55, 104.45, 91.15, 89.7, 92.4, 19.9, 18.85, 25.75, 20.95, 97.05, 25.4, 19.7, 35, 101.25, 70.2, 90.95, 73.85, 88.05, 20.1, 110.3, 85.15, 60.95, 73.55, 46, 58.55, 24.6, 19.75, 86.35, 25.5, 19, 19.55, 110.1, 69.75, 50.6, 65.6, 82.1, 79.1, 90.65, 20.55, 75.75, 110, 20.85, 80.35, 70.15, 67.45, 20.75, 69.9, 51.1, 25.55, 60, 90.55, 76.4, 84.95, 110.1, 99.65, 45.4, 69, 48.65, 59.85, 80.65, 20.55, 66.4, 100.2, 44.55, 20.35, 91.8, 20.2, 50.35, 18.8, 20.45, 64.75, 98.7, 89.45, 58.75, 20.7, 85.6, 80.3, 79.8, 79.85, 54.1, 80.9, 24.5, 20.15, 20.05, 19.6, 114.3, 100.3, 80, 20.85, 89.95, 20, 48.75, 80, 20.35, 20.25, 19.4, 100.4, 57.95, 59.5, 19.2, 59.55, 103.95, 68.95, 103.1, 24.7, 110.2, 62.45, 89.55, 78.9, 20.35, 71.45, 46.35, 94.65, 49.9, 25.45, 20.75, 66.1, 75.4, 21.05, 69.35, 88.85, 97, 66.4, 69.2, 79.5, 100.65, 79.7, 61.4, 69.8, 40.55, 75.65, 90.7, 80.5, 60.6, 101.15, 24.95, 20.3, 60, 20.25, 44.75, 98, 107.7, 104.7, 93.9, 86.45, 19.4, 24.95, 75, 78.2, 94.2, 50.05, 69.55, 90.1, 44.65, 80.35, 98.1, 53.35, 19.55, 48.95, 54.2, 24.45, 40.15, 25.6, 70.35, 91.7, 89.2, 24.1, 53.85, 115.6, 19.75, 24.05, 25.3, 84.3, 89.75, 97.95, 20, 103.9, 20.7, 20.15, 26, 77.35, 66.05, 19.9, 68.15, 80.85, 75.5, 80.6, 83.2, 87.55, 109.4, 45.55, 20.7, 75.3, 93.4, 73.75, 88.15, 49.2, 19.65, 105.15, 49, 49.8, 20.95, 79.3, 19.5, 44.15, 105.5, 92.7, 26.25, 96.95, 20.45, 115.8, 108.2, 20.2, 54.9, 20.15, 90.35, 55.75, 114.6, 66.8, 100.3, 105.35, 85.2, 18.95, 69.8, 106.15, 20.55, 105.75, 25.25, 19.75, 104.85, 60.95, 81.15, 19.1, 20.8, 90.15, 90.1, 74.1, 118.75, 85.9, 95, 20.15, 101.3, 21.2, 24.2, 20.3, 85.3, 89.6, 56.25, 50.95, 115.85, 103.65, 26.1, 35.1, 99.1, 67.25, 25, 59.55, 77.8, 55.1, 24.15, 45.25, 20.25, 64.75, 54.6, 20.7, 94.75, 115.8, 49.45, 83.8, 95.35, 74.05, 89.6, 116.6, 54.2, 19.3, 65.05, 24.05, 18.75, 20.15, 20, 71, 93.6, 24.4, 65.25, 50.55, 70.7, 45.25, 70.3, 108.95, 26.45, 19.65, 19.05, 74.75, 75.8, 25.1, 44.45, 104.3, 89, 20.15, 36.15, 19.2, 19.25, 61.2, 20.45, 35.05, 44, 50.35, 20, 86.4, 58.4, 94.1, 108.9, 107.4, 90.85, 19.9, 66.4, 100.7, 25.6, 19.85, 20.75, 95.8, 94.65, 106.65, 45.85, 104.35, 55.45, 61.15, 78.95, 109.2, 61.3, 96.85, 40.55, 19.8, 108.25, 105.05, 90.45, 86.4, 66.9, 110.7, 20, 102.1, 70.15, 80.05, 49.2, 20.5, 38.25, 54.95, 96.6, 19.9, 19.9, 84.6, 85.25, 81.25, 115.5, 79, 94.65, 20.8, 59.5, 20.05, 100.45, 20.6, 20.3, 39.55, 20.45, 25.25, 91.25, 72.45, 19.7, 75.1, 25, 69.15, 91.55, 35.8, 113.15, 19.85, 19.8, 19.9, 19.7, 59.1, 91.15, 68.95, 51.55, 24.4, 96.8, 70.05, 19.5, 78.75, 69.2, 19.55, 103.65, 54.7, 54.15, 84.85, 20, 99.25, 19.35, 94.75, 114.05, 74.9, 19.8, 80.85, 54.65, 91.7, 118.6, 24.55, 19.45, 116.15, 80.6, 20.3, 99.8, 75, 19.9, 80.3, 84.3, 54.05, 104.9, 97.25, 83.05, 41.1, 45, 74.55, 40.2, 70.5, 19.75, 24.65, 104.25, 78.35, 109.7, 33.45, 94.6, 20.2, 20.3, 39.4, 69.15, 51.35, 100.05, 20.3, 94.45, 46.4, 104.05, 24.9, 59.6, 108.5, 40.55, 58.95, 20.75, 113.15, 48.8, 63.05, 100.85, 80.55, 64.4, 75.2, 84.9, 19.3, 74.65, 59.05, 69.1, 20.55, 76.55, 62.5, 94.9, 111.65, 19.9, 20.45, 106.05, 113.45, 92.55, 65.6, 84.35, 71.1, 85.15, 49.7, 30.2, 56.35, 107.55, 19.85, 95.9, 23.85, 83.85, 84.8, 76.1, 74.55, 39.2, 79.55, 19.6, 19.55, 39.15, 20.1, 99.95, 59.8, 49.75, 108.5, 60.15, 19.05, 84, 44.55, 103.45, 80.65, 57.2, 110.75, 24.7, 97.05, 76.35, 18.9, 74.45, 84.4, 24.4, 20.05, 55.5, 84.3, 100.2, 19.4, 20.4, 94.75, 44.35, 74.55, 73.6, 50.1, 53, 19.85, 24.35, 19.55, 25.05, 93.8, 103.75, 56.75, 20.8, 24.45, 25.6, 59.65, 83.3, 79.55, 24.45, 19.2, 29.8, 45.5, 30.05, 65.65, 74.05, 110.75, 19.7, 49.5, 43.95, 111.15, 20.6, 19.65, 115.8, 88.65, 94.5, 20.1, 34.65, 52.3, 65, 35.45, 19.7, 95.6, 19.85, 81.85, 109.3, 25.4, 69.8, 20, 109.9, 50.3, 101.5, 89.15, 19.4, 29.9, 78.8, 19.3, 96.8, 20.65, 19.8, 104.6, 80.05, 45.15, 73.15, 99.1, 105.35, 45.65, 79.95, 54.45, 25.1, 84.7, 75.85, 48.8, 35.2, 76.25, 24.9, 54.3, 66.3, 20.9, 75.35, 104.45, 49.45, 19.45, 92.15, 19.85, 100.25, 95.7, 93.15, 69.7, 19.8, 71.35, 20.75, 40.6, 20.35, 19.75, 54.4, 20.45, 66.15, 89.85, 45.05, 86.85, 96.75, 106.65, 110.15, 82.85, 20.1, 59.45, 58.6, 49.7, 65.85, 73.5, 113.65, 83.4, 65.65, 61.35, 85.9, 75.65, 70.9, 49.85, 20.1, 103.05, 99.7, 81.9, 66.2, 19.75, 72.6, 116.5, 106.8, 24.95, 89.25, 19.25, 104.55, 87.2, 30.75, 25.7, 86.2, 30.1, 99.35, 19.2, 20.1, 20.35, 25.65, 94.55, 94.4, 56.1, 68.25, 24.75, 76.25, 74.35, 54.15, 19.45, 34.95, 53.65, 104, 70.35, 64.85, 19.65, 45.9, 20, 44.8, 20.35, 45.8, 108.95, 64.35, 90.8, 24.95, 84.7, 70.8, 104.4, 101.5, 54.3, 103.95, 91.1, 19.95, 26.45, 75.1, 108.1, 110.15, 111.5, 106.5, 19.9, 111.1, 70.7, 24.85, 91.2, 65.6, 59.45, 109.95, 38.5, 92.55, 24.5, 19.7, 20.6, 58, 107.45, 65.5, 25.45, 100.15, 104.45, 21.15, 96.2, 44.4, 94.35, 20.3, 105.75, 81.15, 89.55, 54.75, 53.75, 105.75, 105.85, 64.2, 87.7, 89.3, 20.15, 20.05, 67.2, 94.55, 107.5, 73, 114.75, 76.05, 77.9, 90.65, 110.45, 68.7, 44.85, 29.8, 88.9, 58.75, 19.85, 86.9, 59.65, 66.4, 20.15, 108.1, 56.9, 109.6, 25.15, 79.15, 66.75, 48.8, 80.7, 20.55, 115.1, 59.7, 86.45, 33.7, 80.1, 104.05, 108.75, 41.1, 20.35, 105.9, 65.5, 40.45, 70.45, 78.8, 90.1, 82.45, 20.25, 66.25, 89.7, 64.55, 93.65, 73.6, 109.75, 61.45, 106.4, 81.9, 105.2, 54.6, 20.55, 19.7, 66.05, 54.05, 58.9, 96.9, 19.1, 50, 45.4, 85.45, 84.1, 66.25, 76.9, 74.6, 116.95, 40.65, 114.35, 69.7, 98.65, 61.65, 89.35, 95.4, 35.4, 19.95, 19.25, 20.4, 24.75, 25.35, 20, 59.75, 82.5, 20.35, 90.8, 104.95, 105.25, 23.75, 61.3, 75.8, 98, 52, 64.4, 45.8, 30.5, 69.15, 49.25, 39.35, 105.1, 20.1, 19.75, 19.75, 70.4, 20.45, 20.35, 86.2, 95.65, 103.8, 97.2, 63.55, 24.95, 99, 85.55, 94, 50.3, 95, 61.4, 80.55, 78.5, 114.3, 20.05, 62.65, 92.7, 100.45, 75.2, 84.75, 79.5, 19.8, 100.9, 95.3, 90.95, 54.5, 49.6, 25, 45.45, 107.75, 89.1, 44.75, 101.6, 103.15, 95.65, 75.1, 61.35, 19.7, 51, 88.85, 20.05, 65.1, 70.15, 20.75, 56.05, 19.95, 98.6, 79, 89.45, 74.2, 81, 49.6, 84.6, 84.2, 106.3, 69.05, 45.4, 99.35, 50.75, 87.1, 20.15, 98.7, 25.2, 55.7, 65.35, 25.3, 24.25, 60.5, 25.1, 20.05, 30.25, 20.2, 59.9, 25.15, 101.3, 76.95, 55.3, 92.45, 48.45, 19.35, 86.7, 55.7, 84.25, 64.65, 69.2, 54.65, 24.75, 23.95, 105, 59.85, 20.05, 92.15, 44.8, 20.9, 95.4, 80.35, 85.1, 115.05, 19.95, 86.15, 78.85, 86.55, 42.4, 24.25, 20.5, 19.6, 20.25, 20.6, 19.8, 80.2, 116.4, 31.65, 94.15, 20.65, 76.85, 20.15, 55.25, 82.15, 103, 95.1, 95.15, 79.8, 74.8, 20.45, 78.35, 19.1, 20, 19.95, 24, 19.15, 91.3, 19.15, 19.75, 75.5, 83.75, 19.4, 26.5, 19.15, 40.9, 80.25, 70.8, 60.2, 55.2, 54.15, 100.4, 62.55, 70.45, 85.5, 54.5, 20.75, 20.35, 91, 104.8, 51.1, 89.8, 20.55, 64.05, 74.85, 25, 20.3, 26.35, 54.7, 90.25, 20.65, 25.45, 19.5, 66.15, 69.1, 39.1, 20.05, 59.8, 48.6, 105.35, 25.1, 49.75, 94.75, 93, 71.9, 77.55, 19.85, 95.25, 25.05, 53.15, 20.15, 101.25, 100.55, 25.3, 71.8, 19.7, 49.85, 69.6, 19.75, 80.8, 64.2, 35, 19.2, 90.65, 20, 74.65, 61.2, 19.95, 54.8, 73.45, 51.45, 80.45, 85.3, 79.3, 76.5, 25.4, 86.85, 19.65, 45.55, 78.1, 19.3, 110.5, 20.3, 81.35, 55.3, 56.55, 19.7, 104.05, 52.85, 80.65, 24.65, 21.3, 110.2, 51.05, 19.8, 19.9, 87.3, 19.85, 89.4, 20, 20.05, 83.25, 102.9, 39.1, 114.5, 20.2, 55.8, 24.2, 72.8, 99.85, 99.5, 20.25, 26, 19.9, 19.05, 96.5, 19.85, 25.7, 20.3, 91.55, 39.4, 105.7, 70.25, 93.75, 60, 59.8, 90.65, 109, 68.1, 20.4, 81.95, 60.55, 65.6, 82.5, 82.3, 68.15, 20.3, 20.2, 89.2, 74.8, 20.2, 84.4, 25.15, 19.8, 50.85, 102.4, 55.5, 109.75, 106.4, 60, 88.8, 80.05, 75.55, 49.55, 23.9, 66.4, 18.8, 108.4, 85.95, 80.9, 111.8, 20.6, 44.6, 105.1, 115.15, 59.8, 26.3, 70.55, 20.05, 79.85, 90.05, 24.45, 59.95, 25.35, 34.3, 105.05, 19.3, 19.15, 51.4, 71.85, 75.4, 49.7, 78.75, 81.6, 70.4, 76.1, 94, 103.95, 19.95, 110.8, 96.1, 48.8, 50.55, 44.65, 19.45, 89.3, 19.25, 70.5, 19.65, 20.85, 19.65, 19.35, 44, 94.4, 75.4, 71, 21.2, 61.05, 79.95, 19.7, 20.3, 24.35, 19.75, 50.3, 50.25, 85.35, 51.65, 24, 59.85, 25.45, 23.9, 24.15, 75.7, 50.85, 91.6, 98.9, 85, 44.3, 80.2, 60.9, 34.2, 87.15, 54.3, 19.1, 112.75, 19.95, 19.5, 65.55, 78.8, 78.2, 105.25, 89.25, 20.65, 68.7, 78.65, 24.75, 19.75, 89.1, 84.7, 59.9, 19.95, 108.9, 33.6, 85.85, 34.85, 95.3, 84.6, 44.95, 24.7, 100.3, 25.45, 50.7, 55, 68.4, 55.05, 19.8, 84.45, 35.9, 80.75, 78.65, 61.75, 63.7, 87.6, 89.15, 20, 104.4, 20.05, 34.3, 20.65, 84.25, 19.65, 79.85, 20.2, 19.8, 50.35, 74.6, 79.15, 20.35, 21.05, 94.7, 74.95, 111.95, 19.85, 89.75, 20.05, 108.95, 19.65, 24.9, 93.2, 84.8, 71.75, 30.35, 54.85, 19.5, 24.2, 19.35, 100.65, 94.1, 74.55, 56.15, 20.35, 80.55, 61.25, 20.45, 18.9, 19.6, 45.2, 19.45, 25.45, 94.9, 29.3, 20.25, 110.5, 109.4, 19.95, 19.6, 76.6, 19.6, 85.3, 65.85, 20.05, 99.4, 20, 78.45, 25.1, 55, 71.1, 61.55, 45.9, 40.3, 87.1, 49.5, 73.8, 19.2, 25, 35.3, 76.75, 81, 105.55, 18.8, 24.9, 64.9, 61.35, 113.95, 90.15, 54.1, 49.8, 24.4, 95, 69.9, 39.95, 103.25, 94.25, 47.05, 20.55, 19.65, 70.2, 81, 75.9, 24.7, 110.25, 85, 19.75, 23.9, 19.95, 25.15, 54.15, 59.8, 83.85, 104.9, 75.3, 66.65, 109.5, 73.85, 19.3, 118.2, 51.45, 59.45, 19.5, 19.55, 93.55, 59.3, 109.8, 78.1, 39.9, 64.9, 53.4, 24.9, 44.7, 114, 20.25, 83.85, 20.2, 19.95, 20.35, 90, 54.2, 99.1, 66.9, 25.85, 91.05, 20.95, 109.2, 85.8, 19.65, 20.5, 89.65, 74.35, 49.45, 89.1, 75.15, 70.65, 90.05, 19.4, 88.75, 91, 90.8, 18.95, 102.4, 99.9, 88.7, 54.3, 55.7, 103.95, 20.15, 20.05, 91.95, 55.65, 74.7, 104.15, 83.65, 110.05, 25.5, 19.5, 80.7, 105.1, 25.15, 95.65, 80.8, 24.85, 54.75, 50.75, 20.15, 20.05, 71.6, 81.45, 58.4, 53.7, 19.6, 89.4, 84.2, 106.1, 25.75, 64.95, 85.45, 20.05, 20.7, 25.3, 100.6, 74, 99.4, 107.45, 83.6, 99.05, 80.1, 65.3, 89.55, 60.8, 74.5, 99.15, 19.25, 39.45, 44.85, 97.2, 110.55, 19.9, 76.95, 35.4, 20.45, 96.75, 54.2, 100.1, 45.25, 20.85, 33.45, 20.2, 85.9, 61, 86.9, 69.4, 20.35, 104.3, 44.95, 49.45, 20.6, 19.55, 93.5, 54.55, 20.05, 79.45, 79.85, 100, 19.6, 20.2, 50.4, 113.35, 80, 80.95, 24.9, 54.9, 109.25, 116.3, 19.9, 70.35, 25.6, 44.45, 100.15, 73.85, 70.1, 25.25, 21.05, 24.95, 64.5, 105.95, 75.85, 43.6, 91.25, 89.75, 104.4, 90.15, 40.3, 105.25, 104, 69.65, 74.3, 100.9, 20.25, 96.9, 104.1, 20.1, 56.55, 68.6, 69.05, 19.7, 20.05, 94.4, 54.95, 93.7, 110.25, 98.9, 80.45, 79.4, 62.8, 74.9, 74.85, 25.85, 68.3, 48.4, 105.05, 25.15, 19.5, 92.95, 20.7, 19.35, 104.35, 19.55, 74.05, 40.1, 20.1, 83.55, 56.85, 19.55, 106.15, 78.95, 49.75, 92.4, 58.2, 91.95, 65.25, 73.1, 59.75, 59.8, 116.6, 109.3, 101.4, 50.65, 56.15, 19.2, 83, 70.1, 108.3, 25.25, 45.35, 43.9, 79.3, 84.9, 79.25, 71.05, 53.75, 24.25, 44.25, 50.05, 20.15, 69.35, 19.35, 19.15, 61, 20.5, 50.2, 79.6, 24.9, 106.9, 101.35, 55.35, 50.55, 19.5, 90.65, 89.85, 79, 19.55, 19.9, 116.25, 87.75, 81.3, 44.3, 70.35, 44.45, 49.15, 29.45, 85.3, 69.1, 70.35, 20.6, 74.15, 75.05, 44.6, 21.45, 43.45, 20.05, 94.15, 19.55, 75.9, 64.15, 109.55, 110.8, 53.45, 69.95, 97, 90.6, 73.55, 94.35, 19.4, 19.75, 54.6, 29.8, 103.05, 20.3, 35.1, 105.7, 56.25, 60.35, 59.8, 99.65, 50.65, 60.9, 59.65, 64.7, 54.85, 91.35, 25.1, 34, 45.9, 20.5, 20.35, 36.1, 65.8, 20.35, 105.8, 96.75, 24.4, 73.05, 64.35, 20.5, 54.75, 51.15, 41.95, 54.35, 96, 61.45, 19.65, 19, 100, 98.7, 19.8, 73.8, 20.05, 106.2, 116.55, 99.7, 19.7, 19.5, 29.15, 55, 90.8, 51, 90.1, 59.05, 20.3, 72.95, 73.55, 84.3, 78, 72.1, 106.75, 19.25, 20.55, 20, 24.65, 103.5, 23.85, 25.8, 59.45, 20.05, 82.55, 81.25, 74.3, 109.7, 96.35, 66.6, 44.5, 80.1, 69.05, 20.4, 19.7, 50.1, 83.45, 86.65, 20.15, 19.4, 60.05, 20.35, 94.05, 84.1, 78.75, 55.55, 20.1, 70.3, 53.65, 20.75, 103.4, 50.8, 79, 74.6, 96.5, 20.1, 19.4, 77.55, 20.05, 19.85, 20.2, 67.45, 18.55, 29.75, 24.2, 23.55, 20.45, 92.3, 53.65, 39.65, 54.65, 104.8, 29.3, 83.85, 103.65, 99.05, 73.35, 100.05, 20.35, 43.95, 23.5, 70.7, 94.3, 29.15, 20.85, 37.7, 92.45, 44.15, 36.05, 50.25, 109.75, 20.3, 112.35, 94.3, 41.15, 74.65, 48.25, 76.15, 71.1, 96.55, 79.3, 89.6, 20.5, 106.3, 100.35, 85.6, 106.15, 51.1, 19.9, 25.7, 99.4, 69.7, 98.35, 85.45, 95.9, 100.75, 89.2, 25.75, 84.1, 79.3, 107.05, 20.05, 19.5, 45.3, 115.15, 72.95, 19.65, 19.55, 89.55, 50.35, 87.25, 20.8, 109.25, 20.35, 55.9, 79.2, 24, 101.35, 35.45, 79.4, 35.2, 19.65, 49.85, 68.75, 61.9, 79.9, 89.75, 19.4, 93.65, 19.9, 72.9, 25.6, 19.75, 55.7, 117.5, 19.85, 78.9, 20.65, 19.65, 79.75, 79.95, 29.9, 19.75, 45, 44.8, 51.1, 53.15, 24.7, 109.95, 20.8, 25.6, 108.4, 19.55, 85.1, 69.05, 70.15, 111.15, 89.35, 89.1, 91.25, 90.35, 105.55, 19.1, 20.4, 100.45, 85.7, 94, 69.85, 25.85, 71.1, 93.35, 50.55, 81.3, 20.7, 79.05, 19.05, 19.6, 20.2, 20.9, 103.6, 38.8, 88.4, 79.7, 19.3, 55.75, 19.95, 89.65, 45.85, 55.95, 69, 83.55, 65.7, 94.9, 61.9, 20, 67.7, 25.15, 92.85, 111.3, 60.6, 65.5, 19.95, 74.6, 94.6, 81.15, 89.05, 49.2, 19.45, 104.3, 86.05, 25.2, 35.15, 99.65, 105.35, 24.3, 80.7, 89.85, 61.1, 29.05, 99.7, 46, 80.4, 100.05, 94, 68.95, 68.45, 69, 43.85, 44.5, 18.7, 53.55, 114.6, 20.1, 85.5, 108.75, 97.85, 19.55, 84.05, 89.4, 19.7, 79.85, 74.45, 74.1, 18.8, 64.4, 55.8, 20.05, 99.15, 56.75, 104.15, 110.8, 35.75, 69.9, 89.2, 55.65, 50.7, 20, 19.1, 45.55, 101.05, 103.7, 36.25, 49.4, 19.9, 19.8, 45.05, 64.55, 86.25, 19.75, 89.1, 95.55, 102.6, 56.3, 94.2, 43.05, 94, 98.85, 64.35, 72, 49.7, 80.7, 24.2, 65.45, 74.35, 83.2, 25, 40.2, 108.35, 69.5, 76, 93.6, 100.55, 24.45, 89.55, 76.1, 80.5, 20.55, 105.4, 35.75, 95.1, 19.3, 63.1, 84.95, 93.4, 89.2, 85.2, 49.95, 20.65, 20.15, 19.2, 104.95, 103.5, 84.8, 95.05, 73.35, 64.1, 44.4, 20.05, 60, 69.5, 78.7, 60.65, 21.15, 84.8, 103.2, 29.6, 105.65]}, {"line": {"width": 0.65}, "marker": {"color": "#3381ff"}, "name": "Chu<PERSON>", "type": "box", "y": [53.85, 70.7, 99.65, 104.8, 103.7, 55.2, 39.65, 20.15, 99.35, 30.2, 64.7, 69.7, 106.35, 97.85, 80.65, 99.1, 80.65, 95.45, 94.4, 79.35, 75.15, 78.95, 21.05, 98.5, 110, 96.75, 76.5, 100.25, 74.4, 78.05, 58.6, 35.45, 44.35, 70.45, 71.15, 45.65, 95, 82.4, 70.9, 45.3, 104.4, 94.85, 74.45, 76.45, 29.95, 84.5, 79.25, 24.8, 91, 79.9, 106.6, 46, 70.15, 50.05, 55.2, 84.6, 54.4, 95, 74.4, 48.55, 70.4, 40.2, 44.6, 41.15, 106.9, 19.35, 94.45, 24.8, 70.6, 85.4, 105.05, 95.15, 70, 74.5, 44.85, 76.1, 73.6, 95.45, 74.9, 80.6, 80.3, 93.15, 82.45, 70.35, 73.85, 80.6, 75.8, 104.6, 103.4, 90.4, 84.8, 41.9, 80.25, 30.75, 96.5, 85.65, 104.95, 50.65, 90.85, 19.95, 85.45, 73.95, 99.45, 19.9, 19.6, 81.35, 83.3, 75.3, 19.4, 45.4, 105.9, 69.55, 81.05, 101.15, 99.8, 55.95, 55, 74.7, 80.25, 96.1, 69, 45.3, 83.55, 74.35, 74.4, 43.75, 28.45, 99.7, 94.1, 94.2, 80.5, 74.35, 104.8, 75.2, 75.6, 100.05, 85, 86.05, 45.55, 86.3, 80.35, 100.25, 100.3, 19, 75.3, 89.2, 85.7, 61.65, 105.25, 29.95, 65, 20, 90.05, 110.75, 105.5, 104.55, 85.25, 56.15, 89.55, 94.55, 45.7, 89.5, 69.55, 74.6, 19.65, 19.85, 24.25, 69.65, 45.65, 75.05, 49.15, 34.7, 80, 49.25, 75.1, 50.15, 79.85, 19.55, 85.95, 45.35, 94.5, 91.7, 87.25, 98.55, 98.55, 85.9, 89.25, 70.3, 93.35, 19.9, 88.9, 95.8, 82, 45.35, 52.2, 110, 96.75, 98.5, 85.35, 101.3, 69.55, 103.25, 104, 86.2, 111.2, 89.45, 95.6, 90.95, 108.55, 78.85, 44.4, 100, 19.8, 89.9, 100.15, 50.8, 73.65, 95.1, 94.65, 80.6, 85.55, 50.8, 105.1, 110.1, 83.9, 100.55, 103.85, 24.6, 98.9, 98.3, 93.85, 100.5, 72.85, 73.55, 79.4, 81.15, 84.6, 79.05, 74.4, 99.05, 44.95, 44.7, 45.7, 100.3, 19.25, 96, 90.55, 30.35, 108.05, 69.9, 103.75, 86.6, 80.6, 85.3, 70, 94.3, 95.35, 75.5, 90.1, 68.95, 99.55, 57.45, 53.65, 100.6, 83.75, 88.3, 92.1, 79.45, 90.45, 69.75, 19.65, 43.65, 39.5, 97.1, 80, 84.7, 89.55, 90.6, 90.05, 99.05, 69.75, 49.05, 98.05, 114.5, 80.95, 74.3, 89.7, 100.45, 90.4, 56.15, 94.4, 78.95, 44.85, 105.65, 74.65, 64.7, 104.05, 35.55, 95.15, 96.65, 80.4, 31.35, 89.75, 94.4, 19.85, 109.9, 101.35, 60.05, 60.15, 90.45, 94.25, 74.9, 80.25, 69, 66.35, 86, 80.3, 90.55, 75.9, 45.85, 49.95, 78.3, 76.95, 96.15, 45.3, 19.4, 90.15, 45.05, 75.8, 79.3, 30.9, 95.25, 89.6, 110.15, 89.5, 75, 44.75, 44.05, 85.6, 115.55, 86.6, 85.2, 97.65, 109.55, 89.55, 19.45, 20.35, 69.25, 99.5, 25.2, 45, 20.15, 105, 54.7, 87.25, 79.95, 88.35, 94.75, 95.05, 78.45, 70.2, 41.05, 85.6, 79.2, 70, 49.95, 69.25, 94.25, 73, 100.05, 99.8, 35, 76, 93.85, 84.3, 84.4, 101.1, 50.45, 99.95, 91.4, 75.55, 80.8, 100, 20.55, 85.3, 70.4, 98.8, 74.4, 98.75, 106, 104.7, 96.7, 55.05, 88.2, 19.75, 75.65, 74.7, 76.65, 25.8, 42.6, 68.85, 90, 75.35, 100.85, 69.95, 107.5, 45.85, 106.1, 91.7, 95.6, 74.95, 95.35, 45, 99.5, 98.6, 59.5, 80.45, 77.95, 74.7, 88.8, 90.25, 64.65, 89.05, 87.4, 94.75, 86.45, 98.25, 75.75, 85.35, 106.1, 45.05, 109.8, 84.65, 79.5, 85.8, 79.1, 44.3, 105.95, 69.75, 94.65, 96.05, 50.15, 113.6, 78.9, 60.05, 34.7, 85.3, 102.45, 104.4, 99.75, 74.4, 74.25, 59.85, 69.6, 45.4, 19.5, 69.9, 87.15, 84.75, 89.95, 113.2, 90.5, 79, 20.15, 71.65, 20.35, 84.25, 78.1, 89.65, 98.7, 76.35, 79.15, 85, 85.3, 86.55, 73.85, 44.85, 45.1, 96, 20.05, 108.65, 45.55, 35.1, 46.2, 45.15, 43.3, 57.15, 73.2, 85.35, 45.95, 83.75, 70.05, 86, 100.5, 72.65, 21, 45.1, 50.4, 78.95, 94.45, 84.8, 50.9, 99.8, 107.35, 19.55, 79.1, 25.5, 80.75, 91.85, 75.35, 75.45, 49.55, 78.6, 81.1, 74.95, 93.55, 102.1, 90.9, 29.2, 89.15, 108.85, 84.75, 45.7, 69.95, 44.6, 74.95, 95.25, 89.85, 100.45, 47.15, 80.2, 87.1, 75.9, 98.75, 86.45, 45.3, 104.1, 81, 90.6, 88.15, 20.2, 90.8, 79.35, 96.8, 83.35, 49.4, 79.2, 48.7, 95.6, 59.75, 108.15, 71.35, 20.15, 69.75, 93.2, 80.85, 33.65, 79.95, 19.3, 99.2, 85.2, 75.25, 54.35, 24.95, 42.35, 75.3, 94.8, 70.55, 86.15, 85, 80.55, 75.8, 98.5, 95.25, 74.4, 85.15, 88.55, 54.75, 91.3, 79.5, 70.1, 111.3, 95.25, 86.25, 100.8, 86.3, 89.95, 76.45, 70, 64.4, 102.45, 80.1, 80.2, 98.15, 112.95, 70.9, 86.85, 99.85, 74.5, 109.15, 65.2, 99.5, 71.55, 93.9, 108.4, 48.75, 85.65, 106.7, 25.15, 110.35, 103.25, 47.8, 98.6, 83.05, 70.4, 104.75, 94.25, 45.95, 70.15, 85.55, 70.3, 106.35, 103.7, 88.35, 94.2, 96.25, 70.7, 33.55, 94.55, 112.95, 35.9, 99.25, 24.45, 103.35, 84.8, 99.95, 90, 104.85, 106.1, 74.2, 89.35, 77.15, 35.25, 48.55, 98.4, 93.85, 100.7, 80.45, 55.25, 54.45, 75.35, 75.7, 74.25, 89.7, 96.4, 69.5, 85.25, 19.4, 83.3, 80.1, 62.7, 25.3, 98.15, 93.7, 105.95, 82, 95.75, 94.05, 54.75, 95.65, 94.6, 98.9, 76.05, 69.75, 34.5, 105.55, 30.1, 70.3, 80.2, 94.35, 44.6, 19.6, 110.45, 70.7, 99.25, 84.8, 105, 54.45, 85.3, 83.35, 33.5, 90.35, 90.75, 89.6, 49.25, 19.75, 95.15, 101.35, 70.05, 74.65, 50.4, 79.65, 79.85, 78.75, 80.45, 50.1, 76.4, 98.7, 100.8, 90.1, 51.55, 79.25, 94.85, 85.7, 44.4, 89.5, 86.05, 44.55, 35.4, 95.65, 20.95, 34.7, 19.75, 92.55, 60.05, 74.3, 85.35, 74.3, 98.1, 109.65, 74.55, 100.95, 94.9, 104.9, 24.1, 104.05, 88.35, 94.65, 74.4, 79.9, 69.7, 98.1, 20.5, 99.6, 81.95, 69.35, 91.1, 86.1, 94.7, 85.1, 99.15, 102.95, 45.05, 71, 70.55, 79.7, 59, 60.35, 69.25, 70.95, 89, 99.4, 73.5, 95.5, 92.35, 74.55, 83.8, 54.45, 99.8, 105.85, 75.45, 93.85, 100.75, 31, 80, 70.25, 100.15, 94.5, 33.9, 89.95, 84.45, 79.5, 89.15, 74.95, 74.4, 55.3, 55.15, 84.95, 43.25, 48.35, 79.55, 71.05, 84.5, 108.6, 30.3, 30.4, 45.4, 84.15, 19.75, 85.4, 75.2, 55.85, 106.4, 84.4, 56.25, 85.55, 68.65, 77.85, 105.35, 87.05, 53.5, 25.1, 79.65, 54.2, 19.45, 103.4, 84.45, 85.95, 59.4, 30.6, 99.55, 70, 45.3, 29.85, 95.1, 19.8, 76.65, 99.15, 44.95, 49.55, 95.9, 81.5, 84.1, 36.45, 25.05, 49.85, 55.55, 69.5, 104.3, 93.85, 50.45, 74.65, 94.2, 106.55, 20.1, 83.8, 55.15, 86.65, 106.75, 78.45, 87.45, 70.75, 101.35, 81.7, 68.25, 79.15, 95.45, 100.95, 69.1, 69.6, 103.45, 77.95, 78.45, 29.5, 70.75, 84.45, 85.65, 70.15, 97.8, 46.3, 106.3, 89.4, 83.15, 74.15, 99.5, 44.15, 53.9, 85.45, 50.85, 59.2, 83.2, 54.9, 103.9, 19.25, 75.45, 54.85, 95.1, 75.5, 73.75, 96.05, 88.95, 50.7, 53.4, 101.9, 83, 50.6, 89.5, 69.8, 93.8, 101.05, 94.7, 104.15, 61.45, 90.55, 88.8, 85.2, 91.4, 106.15, 45.3, 73.25, 51.05, 85.35, 75.35, 75.2, 20.9, 43.3, 109.5, 84.8, 95.05, 50.35, 74.6, 19.7, 59.45, 105.2, 45.15, 93.8, 96.2, 79.6, 85.25, 80.5, 89.8, 50.75, 89.65, 65.4, 72.9, 40, 99.45, 86.25, 45.65, 70.1, 70.2, 55.35, 84.2, 45.85, 75.25, 100.15, 105.75, 48.35, 24.4, 20.95, 49.55, 20.25, 50.15, 95.9, 25.25, 45.15, 84.4, 75.1, 70.15, 101.75, 45.8, 20.1, 40.65, 101, 69.1, 75, 100, 71.15, 54.15, 108.75, 49.15, 19.4, 79.15, 80.4, 102, 20.65, 70.8, 89.05, 96.6, 104.5, 50.6, 49.35, 94.25, 68.95, 93.85, 76.05, 24.45, 89.35, 80.35, 55.8, 79.05, 95, 25.2, 98.4, 79.4, 101.4, 89.8, 109.75, 19.3, 91.25, 49.1, 65.25, 85.45, 94.1, 71.65, 104.65, 74.25, 74.2, 101, 100.2, 89.05, 70.1, 105.65, 96.5, 70.85, 85.95, 73.9, 109.45, 100.2, 74.05, 101.55, 111.95, 55.95, 45.75, 74.95, 80.15, 109.25, 35.2, 78.8, 106.4, 49.4, 69.1, 90.2, 93.55, 80.85, 82.05, 20.55, 78.55, 78.65, 42.9, 34.25, 58.5, 96.2, 74.4, 105.3, 73.85, 84.55, 24.75, 85.8, 24.45, 105.7, 24.25, 76.5, 93.5, 101.5, 74.9, 69.6, 24.05, 50.55, 100.55, 20.5, 74.8, 24.45, 71, 40.3, 50.6, 84.85, 65.7, 70.5, 94.85, 19.75, 65.45, 98.7, 67.5, 69.6, 84.35, 100.2, 78.05, 73.6, 74.75, 20.2, 93.55, 25.05, 95.55, 39.3, 20.25, 23.9, 85.45, 99.9, 84.8, 75.2, 49.35, 74.9, 101.25, 18.95, 28.6, 24.35, 98.65, 79.9, 103.3, 96.2, 104.15, 102.95, 90.6, 50.8, 90.05, 75.85, 68.5, 73.5, 69.95, 70.05, 94, 61.15, 84.45, 75.3, 109.55, 19.6, 66.4, 100.05, 102, 90.2, 25.15, 79.85, 49.6, 105.95, 91.85, 40.1, 73.9, 89.8, 72.25, 96.55, 40.1, 101.25, 79.55, 84.05, 89.1, 94.4, 78.25, 44.15, 75.75, 19.1, 80.3, 74.9, 80.85, 24.75, 90.85, 79.7, 57.55, 86.5, 103.95, 25.1, 48.95, 83.55, 89.15, 70.45, 60.3, 24.75, 103.3, 78.5, 19.85, 79.9, 99.7, 58.6, 98.5, 50.45, 94.65, 100.25, 88.45, 69.85, 81.7, 79.9, 25.4, 83.75, 20.9, 69.4, 74.9, 74.15, 70.1, 78.3, 96.8, 94.4, 84.3, 92.45, 99.55, 81.25, 19.95, 99.25, 80.45, 79.35, 79.75, 100.05, 69.35, 85.8, 79.7, 50.55, 80.55, 84.5, 84.3, 67.75, 85.25, 80.05, 20, 48.8, 85.05, 102.8, 99.95, 117.8, 79.5, 79.65, 94.7, 92.5, 19.45, 75.55, 70, 74.8, 104.4, 86.2, 51.2, 19.5, 74.9, 74.9, 100.25, 102.8, 100, 99.85, 94.2, 83.85, 88.3, 104.05, 94.7, 100.65, 80.55, 78.85, 44.45, 84.9, 20.25, 74.35, 62.05, 80, 104.1, 39.1, 76.5, 49.2, 23.15, 80.85, 60.1, 78.95, 45.15, 79.4, 53.95, 99.3, 80.65, 71.1, 106.25, 20.8, 94, 89.85, 46, 66.25, 90, 70.45, 19.75, 53.95, 105.5, 81, 69.8, 73.75, 54.55, 76.25, 93.9, 70.4, 91.15, 70.95, 99.5, 83.9, 117.45, 104.4, 29.4, 44.65, 25.25, 84.05, 85.7, 74.7, 90.8, 106.15, 85.35, 90.85, 35.75, 46, 89.4, 19.8, 50.9, 81, 98.35, 51, 91.65, 90.85, 69.4, 94.45, 20.15, 95.7, 74.95, 47.95, 63.6, 36.85, 44.1, 50.75, 104.4, 39.3, 106.45, 96.05, 75.1, 44.7, 55, 74.35, 104.7, 55.7, 19.85, 70.3, 85.55, 94.5, 85.35, 79.65, 79.6, 90.6, 20.2, 106.05, 99.15, 55.9, 82.35, 40.4, 75.35, 85.15, 93.8, 20.4, 94.7, 30.5, 77, 20.1, 75.3, 99.2, 85.5, 20.05, 70.4, 49.75, 75.3, 94, 118.35, 30.45, 96.1, 104.25, 80.2, 104.2, 69.65, 80.8, 80.3, 84.1, 69.35, 79.6, 36.45, 89.4, 80.35, 40.65, 60.45, 84.9, 73.55, 20.15, 34.7, 107.55, 98.75, 101.15, 88.7, 79.75, 94.55, 69.05, 96.25, 101.1, 104.7, 55.25, 90.1, 53.75, 89.3, 95.2, 45.7, 74.5, 79.65, 101.3, 80.05, 89.2, 83.65, 19.5, 51.25, 45.6, 49.65, 20, 34, 92.5, 88.35, 107.95, 74.45, 64.75, 89.8, 95.5, 29.65, 84.5, 90.7, 70.3, 103.95, 74.75, 50.8, 80.25, 78.9, 84.75, 85.45, 19.9, 99.45, 70.6, 81, 84.85, 89.15, 24.8, 105.65, 80.85, 89.45, 72.15, 76.4, 61.6, 79.9, 96.15, 65.3, 19.65, 84.65, 69.55, 31.05, 51, 44.35, 79.7, 55, 84.85, 73.65, 73.9, 77.75, 84.35, 84.95, 73.85, 51.8, 46, 79.4, 71.8, 88.4, 46, 51.75, 94.4, 70.15, 34.7, 81.1, 20.55, 106.6, 106.75, 89.45, 97.9, 55.7, 79.8, 39.05, 83.9, 69.85, 53.55, 93.9, 113.15, 84.95, 80.5, 19.3, 49.65, 54.35, 88.45, 90.5, 94.85, 69.95, 48.6, 55.8, 80.15, 75.5, 20.2, 74.75, 104.05, 96.65, 20.05, 103.45, 19.9, 46.35, 19.95, 79.6, 75.9, 76.2, 19.25, 84.3, 79, 70.25, 84.6, 24.1, 60, 86.55, 20.85, 50.75, 105.5, 85.15, 54.2, 109.5, 104.4, 105.1, 75.7, 90.8, 97.95, 108.15, 80.5, 104.3, 71.35, 89.4, 20.6, 99.95, 81, 70.15, 70.15, 96.55, 95.55, 69.65, 89.3, 87.55, 96.3, 85.2, 35.1, 81.3, 19.6, 85.45, 71, 85.05, 44.4, 70.3, 79.35, 90.8, 70.45, 45.25, 75.8, 71.3, 69.1, 88.25, 70, 97.35, 25.9, 55.65, 69.65, 100.6, 86, 106.95, 29.6, 59.9, 95.6, 41.6, 100.85, 40.2, 84.5, 78.95, 20.2, 85.2, 98, 94.45, 105, 93.85, 84, 48.75, 84.85, 56.65, 73.9, 24.5, 89.9, 78.55, 99.45, 25.2, 74.05, 55, 89.75, 85.15, 94.6, 94.25, 72.45, 105.2, 82.85, 103.85, 83.6, 108.45, 91.5, 80.85, 49.05, 105.3, 88.95, 110.85, 114.2, 36.5, 70.75, 40.15, 94.45, 97.35, 45.3, 94.95, 44.55, 23.45, 29.7, 101.1, 50.65, 55.4, 90.6, 86.85, 99.05, 111.25, 55.1, 102.25, 95.9, 95.05, 53.85, 104.2, 50.25, 99.5, 71, 93.2, 19.35, 19.85, 104.2, 79.25, 44.9, 70.1, 29.65, 77.85, 54.3, 95.15, 110.85, 80.5, 72.2, 51.5, 89.55, 77.5, 95.25, 85, 85.75, 98.25, 25.7, 69, 46.05, 76.4, 100.5, 40.05, 69.95, 93.3, 49.15, 35.05, 73, 83.85, 70.1, 70.65, 20.35, 99, 83.95, 116.2, 93.7, 19.7, 75.55, 77.65, 95, 105.4, 95.85, 79.15, 19.65, 79, 91.85, 106, 49.9, 100.35, 74.95, 49.25, 103.7, 89.75, 20.3, 101.95, 94, 89.3, 74.3, 44.65, 84.05, 80.7, 101.7, 20.4, 102.6, 106, 55.1, 106.5, 91.05, 77.5, 54.2, 69.25, 50.5, 74.4, 79.45, 104.65, 100.05, 100.55, 95.65, 94.4, 55, 101.45, 67.95, 69.5, 18.85, 69.2, 69.65, 101.85, 82.3, 79.25, 84.6, 93.4, 94.2, 25.05, 25.1, 48.95, 45.3, 85.85, 95.2, 100.6, 55.3, 74.85, 102.35, 115.65, 79.85, 76, 104.75, 74.65, 56.25, 106.1, 79.75, 68.65, 20.25, 100.2, 74.9, 70.85, 69.8, 54.55, 70.75, 94.1, 29.7, 110.9, 105, 25.3, 55.15, 20.1, 69.9, 101.4, 80.8, 62.05, 76.45, 91.3, 95.75, 62.65, 74.5, 102.1, 50.15, 86.5, 81.45, 69.15, 79.55, 95.5, 91.05, 79.2, 45.25, 74.3, 74.1, 100.6, 75, 70.2, 70.75, 50.25, 79.2, 96, 100.1, 56.5, 85, 59.3, 49.4, 55, 69.2, 62.3, 92.5, 69.65, 111.6, 48.55, 20.2, 39.65, 24.9, 56.7, 105.95, 74.95, 29.7, 50.35, 47.85, 70.3, 98.8, 99.85, 80.3, 80.45, 86.8, 84.2, 99, 100.75, 91.75, 79.55, 111.1, 89.1, 101.9, 91.65, 88.85, 25.3, 95.45, 91.1, 54.15, 69.7, 89.5, 35.15, 73.75, 101.35, 55.9, 105.9, 43.95, 45.1, 70.25, 55.35, 103, 103.75, 69.35, 73.85, 75.15, 69.6, 80.15, 30.5, 98.3, 107.4, 82, 75.4, 101.25, 89.5, 74.4, 20.5, 74.35, 99.75, 111.95, 39, 94.1, 95.65, 88.05, 66.5, 35.45, 49.9, 104.5, 75.05, 81, 74.45, 60.4, 70.65, 59.8, 44.2, 75.75, 102.95, 74.4]}], "layout": {"font": {"size": 8.8}, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "white", "showlakes": true, "showland": true, "subunitcolor": "#C8D4E3"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "white", "polar": {"angularaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}, "bgcolor": "white", "radialaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "yaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "zaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "baxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "bgcolor": "white", "caxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}}}, "title": {"font": {"size": 13.2}, "text": "Monthly Charges Distribution by Churn Status"}, "xaxis": {"linecolor": "rgba(0,0,0,0.65)", "tickfont": {"size": 8.8}, "title": {"text": "Churn Status"}}, "yaxis": {"linecolor": "rgba(0,0,0,0.65)", "tickfont": {"size": 8.8}, "title": {"text": "Monthly Charges"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["pandas_data_analyst.get_plotly_graph()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Monthly Charge](../img/multi_agents/boxplot_monthly_charge_churn_1.jpg)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Workflow Summary\n", "\n", "This is useful to see the steps taken and the code generated by the agent."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/markdown": ["\n", "\n", "# Pandas Data Analyst Workflow Summary\n", "\n", "This workflow contains 2 agents:\n", "\n", "- **Agent 1:** data_wrangling_agent\n", "\n", "- **Agent 2:** data_visualization_agent\n", "# Data Wrangling Agent Outputs\n", "\n", "## ---RECOMMENDED STEPS----\n", "<recommended_steps_not_found_in_state>\n", "\n", "## ---DATA WRANGLER FUNCTION----\n", "```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: data_wrangling_agent\n", "# Time Created: 2025-02-24 15:14:41\n", "\n", "def data_wrangler(data_list):\n", "    import pandas as pd\n", "    import numpy as np\n", "    '''\n", "    Wrangle the data provided in data.\n", "    \n", "    data_list: A list of one or more pandas data frames containing the raw data to be wrangled.\n", "    '''\n", "\n", "\n", "    # Ensure the data_list is a list, if not, convert it to a list\n", "    if not isinstance(data_list, list):\n", "        data_list = [data_list]\n", "    \n", "    # Concatenate all dataframes in the list into a single dataframe\n", "    df = pd.concat(data_list, ignore_index=True)\n", "    \n", "    # Convert 'TotalCharges' to numeric, errors='coerce' will turn non-convertible values to NaN\n", "    df['TotalCharges'] = pd.to_numeric(df['TotalCharges'], errors='coerce')\n", "    \n", "    # Calculate the average monthly payment for each customer and add it as a new column\n", "    df['AverageMonthlyPayment'] = df['TotalCharges'] / df['tenure'].replace(0, np.nan)  # Avoid division by zero\n", "    \n", "    # Handle churn by converting 'Churn' column to a binary format (Yes = 1, No = 0)\n", "    df['Churn'] = df['Churn'].map({'Yes': 1, 'No': 0})\n", "    \n", "    # Select relevant columns: MonthlyCharges and Churn\n", "    df_wrangled = df[['MonthlyCharges', 'Churn', 'AverageMonthlyPayment']]\n", "    \n", "    # Return the wrangled DataFrame\n", "    return df_wrangled\n", "```\n", "\n", "## ---DATA WRANGLER FUNCTION PATH----\n", "```python\n", "None\n", "```\n", "\n", "## ---DATA WRANGLER FUNCTION NAME----\n", "```python\n", "data_wrangler\n", "```\n", "\n", "## ---DATA WRANGLER ERROR----\n", "None\n", "\n", "# Data Visualization Agent Outputs\n", "\n", "## ---RECOMMENDED STEPS----\n", "# Recommended Data Cleaning Steps:\n", "CHART GENERATOR INSTRUCTIONS: \n", "Create a boxplot with a violin plot overlay to visualize the relationship between Monthly Charges and Churn status. \n", "\n", "- Set the title of the chart to \"Monthly Charges Distribution by Churn Status\".\n", "- Label the X-axis as \"Churn Status\" and the Y-axis as \"Monthly Charges\".\n", "- Use the \"plotly_white\" template for the chart.\n", "- Ensure the background is white.\n", "- Use the color '#3381ff' for the box and violin plots.\n", "- Set the base font size to 8.8 for x and y axes tickfont, any annotations, and hover tips.\n", "- Set the title font size to 13.2.\n", "- Specify the line size as 0.65 within the xaxis and yaxis dictionaries.\n", "- Add smoothers or trendlines to the plots if applicable.\n", "- Ensure that hover tip size is set to 8.8.\n", "\n", "## ---DATA VISUALIZATION FUNCTION----\n", "```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: data_visualization_agent\n", "# Time Created: 2025-02-24 15:14:55\n", "\n", "def data_visualization(data_raw):\n", "    import pandas as pd\n", "    import numpy as np\n", "    import json\n", "    import plotly.graph_objects as go\n", "    import plotly.io as pio\n", "\n", "\n", "\n", "\n", "\n", "    # Create the boxplot and violin plot\n", "    fig = go.Figure()\n", "\n", "    # Add violin plot\n", "    fig.add_trace(go.Violin(\n", "        y=data_raw[data_raw['Churn'] == 0]['MonthlyCharges'],\n", "        box_visible=True,\n", "        line_color='#3381ff',\n", "        name='Not Churned',\n", "        hoverinfo='y',\n", "        points='all'\n", "    ))\n", "\n", "    fig.add_trace(go.Violin(\n", "        y=data_raw[data_raw['Churn'] == 1]['MonthlyCharges'],\n", "        box_visible=True,\n", "        line_color='#3381ff',\n", "        name='<PERSON><PERSON>',\n", "        hoverinfo='y',\n", "        points='all'\n", "    ))\n", "\n", "    # Add boxplot\n", "    fig.add_trace(go.Box(\n", "        y=data_raw[data_raw['Churn'] == 0]['MonthlyCharges'],\n", "        name='Not Churned',\n", "        marker_color='#3381ff',\n", "        line=dict(width=0.65)\n", "    ))\n", "\n", "    fig.add_trace(go.Box(\n", "        y=data_raw[data_raw['Churn'] == 1]['MonthlyCharges'],\n", "        name='<PERSON><PERSON>',\n", "        marker_color='#3381ff',\n", "        line=dict(width=0.65)\n", "    ))\n", "\n", "    # Update layout\n", "    fig.update_layout(\n", "        title='Monthly Charges Distribution by Churn Status',\n", "        xaxis_title='Churn Status',\n", "        yaxis_title='Monthly Charges',\n", "        template='plotly_white',\n", "        font=dict(size=8.8),\n", "        titlefont=dict(size=13.2),\n", "        xaxis=dict(tickfont=dict(size=8.8), linecolor='rgba(0,0,0,0.65)'),\n", "        yaxis=dict(tickfont=dict(size=8.8), linecolor='rgba(0,0,0,0.65)'),\n", "        showlegend=True\n", "    )\n", "\n", "    # Convert figure to JSON\n", "    fig_json = pio.to_json(fig)\n", "    fig_dict = json.loads(fig_json)\n", "\n", "    return fig_dict\n", "```\n", "\n", "## ---DATA VISUALIZATION FUNCTION PATH----\n", "```python\n", "None\n", "```\n", "\n", "## ---DATA VISUALIZATION FUNCTION NAME----\n", "```python\n", "data_visualization\n", "```\n", "\n", "## ---DATA VISUALIZATION ERROR----\n", "None"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pandas_data_analyst.get_workflow_summary(markdown=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)"]}], "metadata": {"kernelspec": {"display_name": "ds4b_301p_dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}