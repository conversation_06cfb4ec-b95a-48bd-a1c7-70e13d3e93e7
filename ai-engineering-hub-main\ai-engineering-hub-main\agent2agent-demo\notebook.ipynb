{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!curl -LsSf https://astral.sh/uv/install.sh | sh\n", "!uv pip install python-a2a"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# open a terminal and run: python agent1.py\n", "# open another terminal and run: python agent2.py\n", "# open another terminal and run: python agent3.py\n", "\n", "# after that, run the following cells"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from python_a2a import AgentNetwork, A2AClient, AIAgentRouter\n", "\n", "# Create an agent network\n", "network = AgentNetwork(name=\"Math Assistant Network\")\n", "\n", "# Add agents to the network\n", "network.add(\"Sine\", \"http://localhost:4737\")\n", "network.add(\"Cosin<PERSON>\", \"http://localhost:4738\")\n", "network.add(\"Tangent\", \"http://localhost:4739\")\n", "\n", "# Create a router to intelligently direct queries to the best agent\n", "router = AIAgentRouter(\n", "    llm_client=A2AClient(\"http://localhost:5000/openai\"),  # LLM for making routing decisions\n", "    agent_network=network\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Route a query to the appropriate agent\n", "query = \"Tan of 0.78545\"\n", "agent_name, confidence = router.route_query(query)\n", "print(f\"Routing to {agent_name} with {confidence:.2f} confidence\")\n", "\n", "# Get the selected agent and ask the question\n", "agent = network.get_agent(agent_name)\n", "response = agent.ask(query)\n", "print(f\"Response: {response}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Route a query to the appropriate agent\n", "query = \"Sine of 3.14159\"\n", "agent_name, confidence = router.route_query(query)\n", "print(f\"Routing to {agent_name} with {confidence:.2f} confidence\")\n", "\n", "# Get the selected agent and ask the question\n", "agent = network.get_agent(agent_name)\n", "response = agent.ask(query)\n", "print(f\"Response: {response}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}