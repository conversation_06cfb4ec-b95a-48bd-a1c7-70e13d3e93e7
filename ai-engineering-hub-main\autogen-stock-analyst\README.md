
# Coding and Stock Analyst

This project leverages Microsoft's AutoGen to build an advanced Coding and Stock Analyst. The solution is powered by Qualcomm's **Cloud AI 100 Ultra**, enabling high-performance LLM serving. Explore the [Qualcomm Cloud AI 100 Ultra Playground](http://bit.ly/Qualcomm-CloudAI100Ultra-Playground) to learn more.

## Demo
Check out the demo video below to see the project in action:
[![Demo Video]](https://youtu.be/ijHtziG0knY)  

## Installation

**Install Dependencies**:
   Ensure you have Python 3.11 or later installed.
   ```bash
   pip install imagine_sdk-0.4.1-py3-none-any.whl[langchain]
   pip install autogen-agentchat~=0.2
   ```
## Features

- Advanced LLM-powered stock analysis
- Auto-code generation for financial and analytical tasks
- Optimized deployment using Qualcomm's Cloud AI 100 Ultra

## Contribution

Contributions are welcome! Please fork the repository and submit a pull request with your improvements.
