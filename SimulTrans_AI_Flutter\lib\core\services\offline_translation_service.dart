import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:flutter_gemma/core/chat.dart';
import 'package:flutter_gemma/core/model.dart';
import '../models/translation_result.dart';
import 'gemma_downloader_service.dart';

/// Offline translation service using Gemma 3N
class OfflineTranslationService {
  InferenceModel? _model;
  InferenceChat? _chat;
  bool _isInitialized = false;
  Gemma3NModel _currentModel = Gemma3NModels.defaultModel;

  /// Initialize the offline translation service
  Future<void> initialize({Gemma3NModel? model}) async {
    try {
      if (_isInitialized) return;

      _currentModel = model ?? Gemma3NModels.defaultModel;
      
      if (kDebugMode) {
        print('🚀 Initializing Offline Translation Service...');
        print('📱 Model: ${_currentModel.modelName}');
      }

      // Check if model is downloaded
      final downloader = GemmaDownloaderService(model: _currentModel);
      final isDownloaded = await downloader.isModelDownloaded();
      
      if (!isDownloaded) {
        throw Exception('Model ${_currentModel.modelName} is not downloaded. Please download it first.');
      }

      final modelPath = await downloader.getModelFilePath();
      
      if (kDebugMode) {
        print('📁 Model path: $modelPath');
      }

      // Initialize Flutter Gemma plugin
      final gemma = FlutterGemmaPlugin.instance;
      
      // Create model with optimized parameters for Gemma 3N
      _model = await gemma.createModel(
        modelType: ModelType.gemmaIt,
        supportImage: true,
        maxTokens: 4096, // Increased for better handling of long translations
      );

      // Create chat instance with multimodal support
      _chat = await _model!.createChat(supportImage: true);
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Offline Translation Service initialized successfully!');
        print('🧠 Using ${_currentModel.modelName} with MatFormer architecture');
        print('🌍 Multimodal support: Text + Image processing');
        print('⚡ Optimized for on-device efficiency');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Offline Translation Service: $e');
      }
      rethrow;
    }
  }

  /// Translate text using offline Gemma 3N
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized || _model == null || _chat == null) {
      throw Exception('Offline translation service not initialized');
    }

    try {
      final startTime = DateTime.now();
      
      if (kDebugMode) {
        print('🔄 Translating text offline...');
        print('📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: $sourceLanguage → To: $targetLanguage');
      }

      // Build optimized prompt for Gemma 3N
      final prompt = _buildTranslationPrompt(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

      // Create message for translation
      final message = Message(
        text: prompt,
        isUser: true,
      );

      // Add message to chat
      await _chat!.addQueryChunk(message);
      
      // Generate translation
      final responseStream = _chat!.generateChatResponseAsync();
      final translatedText = await _collectStreamResponse(responseStream);
      
      // Clean up the response
      final cleanedTranslation = _cleanTranslationResponse(translatedText);
      
      final processingTime = DateTime.now().difference(startTime);
      
      if (kDebugMode) {
        print('✅ Translation completed offline!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
        print('🎯 Result: ${cleanedTranslation.length > 50 ? '${cleanedTranslation.substring(0, 50)}...' : cleanedTranslation}');
      }

      return TranslationResult(
        translatedText: cleanedTranslation,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.95, // Gemma 3N generally has high confidence
        processingTimeMs: processingTime.inMilliseconds,
        isFromCache: false,
        metadata: {
          'model': _currentModel.modelName,
          'offline': true,
          'architecture': 'MatFormer',
          'effective_parameters': _currentModel.modelName.contains('E4B') ? '4B' : '2B',
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Offline translation failed: $e');
      }
      rethrow;
    }
  }

  /// Translate image with text using offline Gemma 3N
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
  }) async {
    if (!_isInitialized || _model == null || _chat == null) {
      throw Exception('Offline translation service not initialized');
    }

    try {
      final startTime = DateTime.now();
      
      if (kDebugMode) {
        print('🖼️ Translating image offline...');
        print('📊 Image size: ${(imageBytes.length / 1024).toStringAsFixed(1)} KB');
        print('🌍 Target language: $targetLanguage');
      }

      // Build image translation prompt
      final prompt = _buildImageTranslationPrompt(
        targetLanguage: targetLanguage,
        context: context,
      );

      // Create message with image
      final message = Message.withImage(
        text: prompt,
        imageBytes: imageBytes,
        isUser: true,
      );

      // Add message to chat
      await _chat!.addQueryChunk(message);
      
      // Generate translation
      final responseStream = _chat!.generateChatResponseAsync();
      final translatedText = await _collectStreamResponse(responseStream);
      
      // Clean up the response
      final cleanedTranslation = _cleanTranslationResponse(translatedText);
      
      final processingTime = DateTime.now().difference(startTime);
      
      if (kDebugMode) {
        print('✅ Image translation completed offline!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
      }

      return TranslationResult(
        translatedText: cleanedTranslation,
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.90, // Slightly lower confidence for image translation
        processingTimeMs: processingTime.inMilliseconds,
        isFromCache: false,
        metadata: {
          'model': _currentModel.modelName,
          'offline': true,
          'type': 'image_translation',
          'architecture': 'MatFormer',
          'multimodal': true,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Offline image translation failed: $e');
      }
      rethrow;
    }
  }

  /// Build optimized translation prompt for Gemma 3N
  String _buildTranslationPrompt({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) {
    final sourceLang = _getLanguageName(sourceLanguage);
    final targetLang = _getLanguageName(targetLanguage);

    final buffer = StringBuffer();

    // Optimized prompt for Gemma 3N MatFormer architecture
    buffer.writeln('# Professional Translation Task (Gemma 3N Optimized)');
    buffer.writeln();
    buffer.writeln('You are Gemma 3N, an advanced multimodal AI with MatFormer architecture optimized for translation.');
    buffer.writeln('Task: Translate the following text from $sourceLang to $targetLang with maximum accuracy.');
    buffer.writeln();

    // Advanced guidelines for Gemma 3N
    buffer.writeln('## Translation Guidelines:');
    buffer.writeln('- Provide ONLY the direct translation (no explanations)');
    buffer.writeln('- Maintain original tone, style, and register');
    buffer.writeln('- Preserve formatting and punctuation');
    buffer.writeln('- Use natural, idiomatic expressions');
    buffer.writeln('- Apply cultural adaptation when appropriate');
    buffer.writeln('- Handle technical terminology with precision');
    buffer.writeln('- Ensure grammatical correctness');
    buffer.writeln();

    if (context != null) {
      buffer.writeln('## Context: $context');
      buffer.writeln();
    }

    if (domain != null) {
      buffer.writeln('## Domain: $domain');
      buffer.writeln();
    }

    buffer.writeln('## Text to translate:');
    buffer.writeln(text);
    buffer.writeln();
    buffer.writeln('## Translation:');

    return buffer.toString();
  }

  /// Build image translation prompt
  String _buildImageTranslationPrompt({
    required String targetLanguage,
    String? context,
  }) {
    final targetLang = _getLanguageName(targetLanguage);

    return '''
# Image Text Translation Task (Gemma 3N Multimodal)

You are Gemma 3N with advanced multimodal capabilities. Analyze this image and:

1. Extract all visible text from the image
2. Identify the source language
3. Translate all text to $targetLang
4. Maintain original formatting and layout context

## Guidelines:
- Extract text accurately using OCR capabilities
- Provide clean, natural translations
- Preserve any special formatting or structure
- Handle multiple text elements separately if needed
- Consider visual context for better translation

${context != null ? '## Context: $context\n' : ''}
## Provide only the translated text:
''';
  }

  /// Collect response from stream
  Future<String> _collectStreamResponse(Stream<String> stream) async {
    final buffer = StringBuffer();
    await for (final chunk in stream) {
      buffer.write(chunk);
    }
    return buffer.toString();
  }

  /// Clean translation response
  String _cleanTranslationResponse(String response) {
    // Remove common prefixes and suffixes
    String cleaned = response.trim();
    
    // Remove markdown formatting
    cleaned = cleaned.replaceAll(RegExp(r'^\*\*.*?\*\*:?\s*'), '');
    cleaned = cleaned.replaceAll(RegExp(r'^#+\s*.*?\n'), '');
    
    // Remove "Translation:" prefix
    cleaned = cleaned.replaceAll(RegExp(r'^Translation:\s*', caseSensitive: false), '');
    
    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    return cleaned.trim();
  }

  /// Get language name from code
  String _getLanguageName(String languageCode) {
    final languageNames = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'auto': 'Auto-detect',
    };
    
    return languageNames[languageCode] ?? languageCode.toUpperCase();
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_chat != null) {
      // Note: flutter_gemma doesn't have explicit dispose methods
      // Resources are managed by the plugin
      _chat = null;
    }
    
    if (_model != null) {
      _model = null;
    }
    
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🧹 Offline Translation Service disposed');
    }
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get current model information
  Gemma3NModel get currentModel => _currentModel;
}
