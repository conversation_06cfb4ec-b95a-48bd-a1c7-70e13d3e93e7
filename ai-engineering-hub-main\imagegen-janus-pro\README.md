
# Image-gen and multimodal QA app ft. DeepSeek Janus-Pro 

This project leverages DeepSeek Janus-pro 7B and Streamlit to create a 100% locally running image gen and multimodal QA app.

## Installation and setup

**Install Dependencies**:
   Ensure you have Python 3.11 or later installed.
```bash
!git clone https://github.com/deepseek-ai/Janus.git

%cd Janus

!pip install -e .
!pip install flash-attn
!pip install streamlit
```
---

## 📬 Stay Updated with Our Newsletter!
**Get a FREE Data Science eBook** 📖 with 150+ essential lessons in Data Science when you subscribe to our newsletter! Stay in the loop with the latest tutorials, insights, and exclusive resources. [Subscribe now!](https://join.dailydoseofds.com)

[![Daily Dose of Data Science Newsletter](https://github.com/patchy631/ai-engineering/blob/main/resources/join_ddods.png)](https://join.dailydoseofds.com)

---

## Contribution

Contributions are welcome! Please fork the repository and submit a pull request with your improvements.
