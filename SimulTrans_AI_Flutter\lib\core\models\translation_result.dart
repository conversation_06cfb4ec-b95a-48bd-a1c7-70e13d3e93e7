// Translation result model

/// Represents the result of a translation operation
class TranslationResult {
  final String originalText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  final double confidence;
  final DateTime timestamp;
  final bool isImageTranslation;
  final bool isAudioTranslation;
  final bool isVideoTranslation;
  final Map<String, dynamic>? metadata;
  Duration? processingTime;
  
  TranslationResult({
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.confidence,
    required this.timestamp,
    this.isImageTranslation = false,
    this.isAudioTranslation = false,
    this.isVideoTranslation = false,
    this.metadata,
    this.processingTime,
  });

  /// Create from JSON
  factory TranslationResult.fromJson(Map<String, dynamic> json) {
    return TranslationResult(
      originalText: json['originalText'] as String,
      translatedText: json['translatedText'] as String,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'originalText': originalText,
      'translatedText': translatedText,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'confidence': confidence,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create a copy with modified fields
  TranslationResult copyWith({
    String? originalText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    double? confidence,
    DateTime? timestamp,
    bool? isImageTranslation,
    bool? isAudioTranslation,
    bool? isVideoTranslation,
    Map<String, dynamic>? metadata,
    Duration? processingTime,
  }) {
    return TranslationResult(
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      confidence: confidence ?? this.confidence,
      timestamp: timestamp ?? this.timestamp,
      isImageTranslation: isImageTranslation ?? this.isImageTranslation,
      isAudioTranslation: isAudioTranslation ?? this.isAudioTranslation,
      isVideoTranslation: isVideoTranslation ?? this.isVideoTranslation,
      metadata: metadata ?? this.metadata,
      processingTime: processingTime ?? this.processingTime,
    );
  }

  /// Get translation type as string
  String get translationType {
    if (isImageTranslation) return 'image';
    if (isAudioTranslation) return 'audio';
    if (isVideoTranslation) return 'video';
    return 'text';
  }

  /// Check if translation is multimodal
  bool get isMultimodal => isImageTranslation || isAudioTranslation || isVideoTranslation;

  /// Get confidence level as string
  String get confidenceLevel {
    if (confidence >= 0.9) return 'high';
    if (confidence >= 0.7) return 'medium';
    return 'low';
  }

  /// Get processing time in milliseconds
  int? get processingTimeMs => processingTime?.inMilliseconds;

  @override
  String toString() {
    return 'TranslationResult(originalText: $originalText, translatedText: $translatedText, '
           'sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, '
           'confidence: $confidence, type: $translationType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TranslationResult &&
        other.originalText == originalText &&
        other.translatedText == translatedText &&
        other.sourceLanguage == sourceLanguage &&
        other.targetLanguage == targetLanguage &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return originalText.hashCode ^
        translatedText.hashCode ^
        sourceLanguage.hashCode ^
        targetLanguage.hashCode ^
        timestamp.hashCode;
  }
}
