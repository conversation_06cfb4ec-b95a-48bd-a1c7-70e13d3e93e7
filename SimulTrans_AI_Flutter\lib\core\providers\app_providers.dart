import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../services/gemma_service.dart';
import '../services/cache_service.dart';
import '../services/performance_service.dart';
import '../services/analytics_service.dart';
import '../models/language_info.dart';
import '../models/translation_result.dart';

/// Theme mode provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  return ThemeModeNotifier();
});

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  Future<void> _loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeIndex = prefs.getInt('theme_mode') ?? 0;
    state = ThemeMode.values[themeModeIndex];
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    state = mode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', mode.index);
  }
}

/// Locale provider
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

class LocaleNotifier extends StateNotifier<Locale> {
  LocaleNotifier() : super(const Locale('pt', 'BR')) {
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('language_code') ?? 'pt';
    final countryCode = prefs.getString('country_code') ?? 'BR';
    state = Locale(languageCode, countryCode);
  }

  Future<void> setLocale(Locale locale) async {
    state = locale;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', locale.languageCode);
    if (locale.countryCode != null) {
      await prefs.setString('country_code', locale.countryCode!);
    }
  }
}

/// Gemma service provider
final gemmaServiceProvider = Provider<GemmaService>((ref) {
  return GemmaService.instance;
});

/// Gemma initialization state provider
final gemmaInitializationProvider = StateNotifierProvider<GemmaInitializationNotifier, AsyncValue<bool>>((ref) {
  return GemmaInitializationNotifier(ref.read(gemmaServiceProvider));
});

class GemmaInitializationNotifier extends StateNotifier<AsyncValue<bool>> {
  final GemmaService _gemmaService;

  GemmaInitializationNotifier(this._gemmaService) : super(const AsyncValue.loading()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      final success = await _gemmaService.initialize();
      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> reinitialize({String? modelName, String? huggingFaceToken}) async {
    state = const AsyncValue.loading();
    try {
      final success = await _gemmaService.initialize(
        modelName: modelName,
        huggingFaceToken: huggingFaceToken,
        forceReinitialization: true,
      );
      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Translation history provider
final translationHistoryProvider = StateNotifierProvider<TranslationHistoryNotifier, List<TranslationResult>>((ref) {
  return TranslationHistoryNotifier();
});

class TranslationHistoryNotifier extends StateNotifier<List<TranslationResult>> {
  TranslationHistoryNotifier() : super([]) {
    _loadHistory();
  }

  Future<void> _loadHistory() async {
    // Load from cache or local storage
    // Implementation would load saved translation history
    state = [];
  }

  void addTranslation(TranslationResult result) {
    state = [result, ...state];
    _saveHistory();
  }

  void removeTranslation(TranslationResult result) {
    state = state.where((t) => t != result).toList();
    _saveHistory();
  }

  void clearHistory() {
    state = [];
    _saveHistory();
  }

  Future<void> _saveHistory() async {
    // Save to cache or local storage
    // Implementation would persist translation history
  }
}

/// Language preferences provider
final languagePreferencesProvider = StateNotifierProvider<LanguagePreferencesNotifier, LanguagePreferences>((ref) {
  return LanguagePreferencesNotifier();
});

class LanguagePreferencesNotifier extends StateNotifier<LanguagePreferences> {
  LanguagePreferencesNotifier() : super(LanguagePreferences.defaultPreferences()) {
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    final sourceLanguage = prefs.getString('source_language') ?? 'auto';
    final targetLanguage = prefs.getString('target_language') ?? 'en';
    final recentLanguages = prefs.getStringList('recent_languages') ?? [];
    final favoriteLanguages = prefs.getStringList('favorite_languages') ?? [];

    state = LanguagePreferences(
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      recentLanguages: recentLanguages,
      favoriteLanguages: favoriteLanguages,
    );
  }

  Future<void> setSourceLanguage(String languageCode) async {
    state = state.copyWith(sourceLanguage: languageCode);
    await _savePreferences();
  }

  Future<void> setTargetLanguage(String languageCode) async {
    state = state.copyWith(targetLanguage: languageCode);
    await _savePreferences();
    _addToRecentLanguages(languageCode);
  }

  Future<void> addToFavorites(String languageCode) async {
    if (!state.favoriteLanguages.contains(languageCode)) {
      final updatedFavorites = [...state.favoriteLanguages, languageCode];
      state = state.copyWith(favoriteLanguages: updatedFavorites);
      await _savePreferences();
    }
  }

  Future<void> removeFromFavorites(String languageCode) async {
    final updatedFavorites = state.favoriteLanguages.where((lang) => lang != languageCode).toList();
    state = state.copyWith(favoriteLanguages: updatedFavorites);
    await _savePreferences();
  }

  void _addToRecentLanguages(String languageCode) {
    if (languageCode == 'auto') return;
    
    final updatedRecent = [languageCode, ...state.recentLanguages.where((lang) => lang != languageCode)]
        .take(10)
        .toList();
    
    state = state.copyWith(recentLanguages: updatedRecent);
    _savePreferences();
  }

  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('source_language', state.sourceLanguage);
    await prefs.setString('target_language', state.targetLanguage);
    await prefs.setStringList('recent_languages', state.recentLanguages);
    await prefs.setStringList('favorite_languages', state.favoriteLanguages);
  }
}

/// Performance metrics provider
final performanceMetricsProvider = Provider<Map<String, dynamic>>((ref) {
  return PerformanceService.instance.getMetrics();
});

/// Cache statistics provider
final cacheStatsProvider = Provider<Map<String, dynamic>>((ref) {
  return CacheService.instance.getCacheStats();
});

/// Supported languages provider
final supportedLanguagesProvider = Provider<List<LanguageInfo>>((ref) {
  return LanguageInfo.getAllSupportedLanguages();
});

/// Popular languages provider
final popularLanguagesProvider = Provider<List<LanguageInfo>>((ref) {
  return LanguageInfo.getPopularLanguages();
});

/// App settings provider
final appSettingsProvider = StateNotifierProvider<AppSettingsNotifier, AppSettings>((ref) {
  return AppSettingsNotifier();
});

class AppSettingsNotifier extends StateNotifier<AppSettings> {
  AppSettingsNotifier() : super(AppSettings.defaultSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    state = AppSettings(
      enableAnalytics: prefs.getBool('enable_analytics') ?? true,
      enableCrashReporting: prefs.getBool('enable_crash_reporting') ?? true,
      enablePerformanceMonitoring: prefs.getBool('enable_performance_monitoring') ?? true,
      enableOfflineMode: prefs.getBool('enable_offline_mode') ?? true,
      autoDetectLanguage: prefs.getBool('auto_detect_language') ?? true,
      saveTranslationHistory: prefs.getBool('save_translation_history') ?? true,
      enableVibration: prefs.getBool('enable_vibration') ?? true,
      enableSounds: prefs.getBool('enable_sounds') ?? true,
      maxCacheSize: prefs.getInt('max_cache_size') ?? 100,
      cacheExpirationDays: prefs.getInt('cache_expiration_days') ?? 7,
    );
  }

  Future<void> updateSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    
    switch (key) {
      case 'enable_analytics':
        state = state.copyWith(enableAnalytics: value);
        await prefs.setBool(key, value);
        await AnalyticsService.instance.setAnalyticsEnabled(value);
        break;
      case 'enable_crash_reporting':
        state = state.copyWith(enableCrashReporting: value);
        await prefs.setBool(key, value);
        await AnalyticsService.instance.setCrashReportingEnabled(value);
        break;
      case 'enable_performance_monitoring':
        state = state.copyWith(enablePerformanceMonitoring: value);
        await prefs.setBool(key, value);
        if (value) {
          PerformanceService.instance.startMonitoring();
        } else {
          PerformanceService.instance.stopMonitoring();
        }
        break;
      case 'enable_offline_mode':
        state = state.copyWith(enableOfflineMode: value);
        await prefs.setBool(key, value);
        break;
      case 'auto_detect_language':
        state = state.copyWith(autoDetectLanguage: value);
        await prefs.setBool(key, value);
        break;
      case 'save_translation_history':
        state = state.copyWith(saveTranslationHistory: value);
        await prefs.setBool(key, value);
        break;
      case 'enable_vibration':
        state = state.copyWith(enableVibration: value);
        await prefs.setBool(key, value);
        break;
      case 'enable_sounds':
        state = state.copyWith(enableSounds: value);
        await prefs.setBool(key, value);
        break;
      case 'max_cache_size':
        state = state.copyWith(maxCacheSize: value);
        await prefs.setInt(key, value);
        break;
      case 'cache_expiration_days':
        state = state.copyWith(cacheExpirationDays: value);
        await prefs.setInt(key, value);
        break;
    }
  }
}

/// Language preferences model
class LanguagePreferences {
  final String sourceLanguage;
  final String targetLanguage;
  final List<String> recentLanguages;
  final List<String> favoriteLanguages;

  const LanguagePreferences({
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.recentLanguages,
    required this.favoriteLanguages,
  });

  factory LanguagePreferences.defaultPreferences() {
    return const LanguagePreferences(
      sourceLanguage: 'auto',
      targetLanguage: 'en',
      recentLanguages: [],
      favoriteLanguages: ['en', 'es', 'fr', 'de', 'pt'],
    );
  }

  LanguagePreferences copyWith({
    String? sourceLanguage,
    String? targetLanguage,
    List<String>? recentLanguages,
    List<String>? favoriteLanguages,
  }) {
    return LanguagePreferences(
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      recentLanguages: recentLanguages ?? this.recentLanguages,
      favoriteLanguages: favoriteLanguages ?? this.favoriteLanguages,
    );
  }
}

/// App settings model
class AppSettings {
  final bool enableAnalytics;
  final bool enableCrashReporting;
  final bool enablePerformanceMonitoring;
  final bool enableOfflineMode;
  final bool autoDetectLanguage;
  final bool saveTranslationHistory;
  final bool enableVibration;
  final bool enableSounds;
  final int maxCacheSize;
  final int cacheExpirationDays;

  const AppSettings({
    required this.enableAnalytics,
    required this.enableCrashReporting,
    required this.enablePerformanceMonitoring,
    required this.enableOfflineMode,
    required this.autoDetectLanguage,
    required this.saveTranslationHistory,
    required this.enableVibration,
    required this.enableSounds,
    required this.maxCacheSize,
    required this.cacheExpirationDays,
  });

  factory AppSettings.defaultSettings() {
    return const AppSettings(
      enableAnalytics: true,
      enableCrashReporting: true,
      enablePerformanceMonitoring: true,
      enableOfflineMode: true,
      autoDetectLanguage: true,
      saveTranslationHistory: true,
      enableVibration: true,
      enableSounds: true,
      maxCacheSize: 100,
      cacheExpirationDays: 7,
    );
  }

  AppSettings copyWith({
    bool? enableAnalytics,
    bool? enableCrashReporting,
    bool? enablePerformanceMonitoring,
    bool? enableOfflineMode,
    bool? autoDetectLanguage,
    bool? saveTranslationHistory,
    bool? enableVibration,
    bool? enableSounds,
    int? maxCacheSize,
    int? cacheExpirationDays,
  }) {
    return AppSettings(
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableOfflineMode: enableOfflineMode ?? this.enableOfflineMode,
      autoDetectLanguage: autoDetectLanguage ?? this.autoDetectLanguage,
      saveTranslationHistory: saveTranslationHistory ?? this.saveTranslationHistory,
      enableVibration: enableVibration ?? this.enableVibration,
      enableSounds: enableSounds ?? this.enableSounds,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      cacheExpirationDays: cacheExpirationDays ?? this.cacheExpirationDays,
    );
  }
}
