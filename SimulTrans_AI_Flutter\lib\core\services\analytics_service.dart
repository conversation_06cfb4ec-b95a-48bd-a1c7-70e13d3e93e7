import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../app_config.dart';
import '../utils/logger.dart';
import 'cache_service.dart';

/// Analytics and crash reporting service
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  static AnalyticsService get instance => _instance;
  AnalyticsService._internal();

  FirebaseAnalytics? _analytics;
  FirebaseCrashlytics? _crashlytics;
  
  bool _isInitialized = false;
  bool _analyticsEnabled = AppConfig.enableAnalytics;
  bool _crashReportingEnabled = AppConfig.enableCrashReporting;
  
  String? _userId;
  Map<String, dynamic> _userProperties = {};
  final List<Map<String, dynamic>> _pendingEvents = [];

  bool get isInitialized => _isInitialized;
  bool get analyticsEnabled => _analyticsEnabled;
  bool get crashReportingEnabled => _crashReportingEnabled;

  /// Initialize analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (_analyticsEnabled) {
        _analytics = FirebaseAnalytics.instance;
        await _setupAnalytics();
      }

      if (_crashReportingEnabled) {
        _crashlytics = FirebaseCrashlytics.instance;
        await _setupCrashlytics();
      }

      // Set up device and app info
      await _setDeviceInfo();
      
      // Process any pending events
      await _processPendingEvents();

      _isInitialized = true;
      Logger.info('Analytics service initialized');
      
    } catch (e) {
      Logger.error('Failed to initialize analytics service: $e');
      // Continue without analytics rather than failing
      _isInitialized = true;
    }
  }

  /// Track translation event
  Future<void> trackTranslation({
    required String sourceLanguage,
    required String targetLanguage,
    required String translationType, // text, image, audio, video
    required int textLength,
    required Duration processingTime,
    required double confidence,
    bool? fromCache,
  }) async {
    if (!_analyticsEnabled) return;

    final event = {
      'event_name': 'translation_completed',
      'parameters': {
        'source_language': sourceLanguage,
        'target_language': targetLanguage,
        'translation_type': translationType,
        'text_length': textLength,
        'processing_time_ms': processingTime.inMilliseconds,
        'confidence_score': confidence,
        'from_cache': fromCache ?? false,
        'language_pair': '${sourceLanguage}_to_$targetLanguage',
      },
    };

    await _trackEvent(event);
  }

  /// Track model download event
  Future<void> trackModelDownload({
    required String modelName,
    required Duration downloadTime,
    required int modelSizeMB,
    required bool success,
    String? errorMessage,
  }) async {
    if (!_analyticsEnabled) return;

    final event = {
      'event_name': 'model_download',
      'parameters': {
        'model_name': modelName,
        'download_time_ms': downloadTime.inMilliseconds,
        'model_size_mb': modelSizeMB,
        'success': success,
        'error_message': errorMessage,
      },
    };

    await _trackEvent(event);
  }

  /// Track user engagement
  Future<void> trackUserEngagement({
    required String action, // app_open, feature_used, settings_changed
    String? feature,
    Map<String, dynamic>? additionalData,
  }) async {
    if (!_analyticsEnabled) return;

    final event = {
      'event_name': 'user_engagement',
      'parameters': {
        'action': action,
        'feature': feature,
        ...?additionalData,
      },
    };

    await _trackEvent(event);
  }

  /// Track performance metrics
  Future<void> trackPerformance({
    required String operation,
    required Duration duration,
    required bool success,
    Map<String, dynamic>? metadata,
  }) async {
    if (!_analyticsEnabled) return;

    final event = {
      'event_name': 'performance_metric',
      'parameters': {
        'operation': operation,
        'duration_ms': duration.inMilliseconds,
        'success': success,
        ...?metadata,
      },
    };

    await _trackEvent(event);
  }

  /// Track error events
  Future<void> trackError({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    final event = {
      'event_name': 'error_occurred',
      'parameters': {
        'error_type': errorType,
        'error_message': errorMessage,
        'has_stack_trace': stackTrace != null,
        ...?context,
      },
    };

    await _trackEvent(event);

    // Also report to Crashlytics if enabled
    if (_crashReportingEnabled && _crashlytics != null) {
      await _crashlytics!.recordError(
        errorMessage,
        stackTrace != null ? StackTrace.fromString(stackTrace) : null,
        context: context,
      );
    }
  }

  /// Track feature usage
  Future<void> trackFeatureUsage({
    required String featureName,
    Map<String, dynamic>? parameters,
  }) async {
    if (!_analyticsEnabled) return;

    final event = {
      'event_name': 'feature_used',
      'parameters': {
        'feature_name': featureName,
        ...?parameters,
      },
    };

    await _trackEvent(event);
  }

  /// Set user properties
  Future<void> setUserProperties({
    String? userId,
    String? preferredLanguage,
    String? deviceType,
    Map<String, dynamic>? customProperties,
  }) async {
    if (!_analyticsEnabled) return;

    if (userId != null) {
      _userId = userId;
      await _analytics?.setUserId(id: userId);
    }

    final properties = {
      if (preferredLanguage != null) 'preferred_language': preferredLanguage,
      if (deviceType != null) 'device_type': deviceType,
      ...?customProperties,
    };

    _userProperties.addAll(properties);

    for (final entry in properties.entries) {
      await _analytics?.setUserProperty(
        name: entry.key,
        value: entry.value.toString(),
      );
    }
  }

  /// Enable/disable analytics
  Future<void> setAnalyticsEnabled(bool enabled) async {
    _analyticsEnabled = enabled;
    
    if (_analytics != null) {
      await _analytics!.setAnalyticsCollectionEnabled(enabled);
    }

    Logger.info('Analytics ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Enable/disable crash reporting
  Future<void> setCrashReportingEnabled(bool enabled) async {
    _crashReportingEnabled = enabled;
    
    if (_crashlytics != null) {
      await _crashlytics!.setCrashlyticsCollectionEnabled(enabled);
    }

    Logger.info('Crash reporting ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Get analytics summary
  Map<String, dynamic> getAnalyticsSummary() {
    return {
      'analytics_enabled': _analyticsEnabled,
      'crash_reporting_enabled': _crashReportingEnabled,
      'user_id': _userId,
      'user_properties': _userProperties,
      'pending_events': _pendingEvents.length,
      'is_initialized': _isInitialized,
    };
  }

  /// Clear all analytics data
  Future<void> clearAnalyticsData() async {
    _userId = null;
    _userProperties.clear();
    _pendingEvents.clear();
    
    if (_analytics != null) {
      await _analytics!.resetAnalyticsData();
    }

    Logger.info('Analytics data cleared');
  }

  // Private helper methods
  Future<void> _setupAnalytics() async {
    if (_analytics == null) return;

    await _analytics!.setAnalyticsCollectionEnabled(_analyticsEnabled);
    
    // Set default parameters
    await _analytics!.setDefaultEventParameters({
      'app_version': (await PackageInfo.fromPlatform()).version,
      'platform': defaultTargetPlatform.name,
    });
  }

  Future<void> _setupCrashlytics() async {
    if (_crashlytics == null) return;

    await _crashlytics!.setCrashlyticsCollectionEnabled(_crashReportingEnabled);
    
    // Set up automatic crash reporting for Flutter errors
    FlutterError.onError = (FlutterErrorDetails details) {
      if (_crashReportingEnabled) {
        _crashlytics!.recordFlutterFatalError(details);
      }
    };

    // Set up automatic crash reporting for async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      if (_crashReportingEnabled) {
        _crashlytics!.recordError(error, stack);
      }
      return true;
    };
  }

  Future<void> _setDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      
      Map<String, dynamic> deviceProperties = {
        'app_version': packageInfo.version,
        'app_build': packageInfo.buildNumber,
        'platform': defaultTargetPlatform.name,
      };

      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceProperties.addAll({
          'device_model': androidInfo.model,
          'device_brand': androidInfo.brand,
          'os_version': androidInfo.version.release,
          'sdk_version': androidInfo.version.sdkInt.toString(),
        });
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceProperties.addAll({
          'device_model': iosInfo.model,
          'device_name': iosInfo.name,
          'os_version': iosInfo.systemVersion,
        });
      }

      await setUserProperties(customProperties: deviceProperties);
      
    } catch (e) {
      Logger.error('Failed to set device info: $e');
    }
  }

  Future<void> _trackEvent(Map<String, dynamic> event) async {
    if (!_isInitialized || !_analyticsEnabled) {
      // Queue event for later if not initialized
      _pendingEvents.add(event);
      return;
    }

    try {
      if (_analytics != null) {
        await _analytics!.logEvent(
          name: event['event_name'],
          parameters: event['parameters'],
        );
      }
      
      Logger.debug('Analytics event tracked: ${event['event_name']}');
      
    } catch (e) {
      Logger.error('Failed to track analytics event: $e');
      // Add back to pending events for retry
      _pendingEvents.add(event);
    }
  }

  Future<void> _processPendingEvents() async {
    if (_pendingEvents.isEmpty) return;

    final eventsToProcess = List<Map<String, dynamic>>.from(_pendingEvents);
    _pendingEvents.clear();

    for (final event in eventsToProcess) {
      await _trackEvent(event);
    }

    Logger.info('Processed ${eventsToProcess.length} pending analytics events');
  }
}
