analysis_task:
  description: >
    Analyse the usage of {brand_name} in a set of Youtube videos containing these details:
    - URL
    - Title
    - Description
    - Transcript (may be empty)
    - Hashtags
    - Likes
    - Comments
    - Original Poster ID
    - Views
    
    This is the list of scraped data:
    {youtube_data}

  expected_output: >
    A clear and concise analysis of the Youtube videos and how the {brand_name} is being used in the videos. 
    Your analysis should include distinct analysis for each video. Each piece of analysis should include the url of the video, the title of the video,
    your thoughts on the tone of the video, the sentiment of towards the brand, the engagement of the video, etc.
  agent: analysis_agent

write_report_task:
  description: >
    Write a crisp bullet point report using the analysis obtained from the Youtube analysis agent and how the {brand_name} is being used in the videos.
  expected_output: >
    A clear and concise bullet point report about the analysis of the Youtube videos and how the {brand_name} is being used in the videos.
    For each video in the input data, the output should be in the structured format provided to you:
    - A short and crisp title summarizing how the {brand_name} is being used in a video.
    - The URL of the video.
    - A detailed analysis of the usage of {brand_name} in the video with bullet points. You can cover things like the tone of the video,
    the sentiment of towards the brand, whether it received a ton of engagement, whether it was a paid partnership, etc.
    Maintain a verbal communicative tone in each of the bullet points but don't be too verbose.
  agent: writer_agent
