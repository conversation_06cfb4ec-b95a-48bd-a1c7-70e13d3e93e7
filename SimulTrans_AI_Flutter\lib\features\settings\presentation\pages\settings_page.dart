import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/services/cache_service.dart';
import '../../../../core/services/performance_service.dart';

/// Settings page with comprehensive configuration options
class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final appSettings = ref.watch(appSettingsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(localeProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configurações'),
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // App Settings Section
          _buildSection(
            context,
            title: 'Aplicativo',
            icon: Icons.settings,
            children: [
              _buildThemeSelector(context, ref, themeMode),
              _buildLanguageSelector(context, ref, locale),
              _buildSwitchTile(
                title: 'Modo Offline',
                subtitle: 'Usar apenas recursos locais',
                value: appSettings.enableOfflineMode,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('enable_offline_mode', value),
              ),
              _buildSwitchTile(
                title: 'Detecção Automática de Idioma',
                subtitle: 'Detectar idioma automaticamente',
                value: appSettings.autoDetectLanguage,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('auto_detect_language', value),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Privacy & Analytics Section
          _buildSection(
            context,
            title: 'Privacidade e Analytics',
            icon: Icons.privacy_tip,
            children: [
              _buildSwitchTile(
                title: 'Analytics',
                subtitle: 'Ajudar a melhorar o app',
                value: appSettings.enableAnalytics,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('enable_analytics', value),
              ),
              _buildSwitchTile(
                title: 'Relatórios de Erro',
                subtitle: 'Enviar relatórios de falhas',
                value: appSettings.enableCrashReporting,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('enable_crash_reporting', value),
              ),
              _buildSwitchTile(
                title: 'Monitoramento de Performance',
                subtitle: 'Coletar métricas de performance',
                value: appSettings.enablePerformanceMonitoring,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('enable_performance_monitoring', value),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Storage Section
          _buildSection(
            context,
            title: 'Armazenamento',
            icon: Icons.storage,
            children: [
              _buildSwitchTile(
                title: 'Salvar Histórico',
                subtitle: 'Manter histórico de traduções',
                value: appSettings.saveTranslationHistory,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('save_translation_history', value),
              ),
              _buildSliderTile(
                title: 'Tamanho Máximo do Cache',
                subtitle: '${appSettings.maxCacheSize} MB',
                value: appSettings.maxCacheSize.toDouble(),
                min: 50,
                max: 500,
                divisions: 9,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('max_cache_size', value.round()),
              ),
              _buildSliderTile(
                title: 'Expiração do Cache',
                subtitle: '${appSettings.cacheExpirationDays} dias',
                value: appSettings.cacheExpirationDays.toDouble(),
                min: 1,
                max: 30,
                divisions: 29,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('cache_expiration_days', value.round()),
              ),
              _buildActionTile(
                title: 'Limpar Cache',
                subtitle: 'Remover dados em cache',
                icon: Icons.clear_all,
                onTap: () => _showClearCacheDialog(context),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // User Experience Section
          _buildSection(
            context,
            title: 'Experiência do Usuário',
            icon: Icons.tune,
            children: [
              _buildSwitchTile(
                title: 'Vibração',
                subtitle: 'Feedback tátil',
                value: appSettings.enableVibration,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('enable_vibration', value),
              ),
              _buildSwitchTile(
                title: 'Sons',
                subtitle: 'Feedback sonoro',
                value: appSettings.enableSounds,
                onChanged: (value) => ref.read(appSettingsProvider.notifier)
                    .updateSetting('enable_sounds', value),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // About Section
          _buildSection(
            context,
            title: 'Sobre',
            icon: Icons.info,
            children: [
              _buildActionTile(
                title: 'Versão do App',
                subtitle: '1.0.0',
                icon: Icons.app_settings_alt,
                onTap: () {},
              ),
              _buildActionTile(
                title: 'Modelo Gemma 3N',
                subtitle: 'Google Gemma 3N E4B-it',
                icon: Icons.psychology,
                onTap: () {},
              ),
              _buildActionTile(
                title: 'Licenças',
                subtitle: 'Licenças de código aberto',
                icon: Icons.description,
                onTap: () => _showLicensesDialog(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: AppTheme.primaryColor,
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(subtitle),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildThemeSelector(BuildContext context, WidgetRef ref, ThemeMode currentMode) {
    return ListTile(
      leading: Icon(Icons.palette, color: AppTheme.primaryColor),
      title: const Text('Tema'),
      subtitle: Text(_getThemeModeLabel(currentMode)),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showThemeDialog(context, ref, currentMode),
    );
  }

  Widget _buildLanguageSelector(BuildContext context, WidgetRef ref, Locale currentLocale) {
    return ListTile(
      leading: Icon(Icons.language, color: AppTheme.primaryColor),
      title: const Text('Idioma do App'),
      subtitle: Text(_getLocaleLabel(currentLocale)),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showLanguageDialog(context, ref, currentLocale),
    );
  }

  String _getThemeModeLabel(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Claro';
      case ThemeMode.dark:
        return 'Escuro';
      case ThemeMode.system:
        return 'Sistema';
    }
  }

  String _getLocaleLabel(Locale locale) {
    switch (locale.languageCode) {
      case 'pt':
        return 'Português';
      case 'en':
        return 'English';
      case 'es':
        return 'Español';
      default:
        return locale.languageCode.toUpperCase();
    }
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref, ThemeMode currentMode) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Selecionar Tema'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((mode) {
            return RadioListTile<ThemeMode>(
              title: Text(_getThemeModeLabel(mode)),
              value: mode,
              groupValue: currentMode,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeModeProvider.notifier).setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context, WidgetRef ref, Locale currentLocale) {
    final languages = [
      const Locale('pt', 'BR'),
      const Locale('en', 'US'),
      const Locale('es', 'ES'),
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Idioma do App'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages.map((locale) {
            return RadioListTile<Locale>(
              title: Text(_getLocaleLabel(locale)),
              value: locale,
              groupValue: currentLocale,
              onChanged: (value) {
                if (value != null) {
                  ref.read(localeProvider.notifier).setLocale(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Limpar Cache'),
        content: const Text('Isso removerá todas as traduções em cache. Deseja continuar?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              await CacheService.instance.clearAllCaches();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache limpo com sucesso!'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Limpar'),
          ),
        ],
      ),
    );
  }

  void _showLicensesDialog(BuildContext context) {
    showLicensePage(context: context);
  }
}
