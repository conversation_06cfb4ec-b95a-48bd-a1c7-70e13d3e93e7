# Financial Analyst with CrewAI and DeepSeek using SambaNova

This project implements a Financial Analyst with CrewAI and DeepSeek using SambaNova.
- [<PERSON><PERSON><PERSON><PERSON>](https://fnf.dev/4jH8edk) is used to as the inference engine to run the DeepSeek model.
- CrewAI is used to analyze the user query and generate a summary.
- Streamlit is used to create a web interface for the project.


---
## Setup and installations

**Get SambaNova API Key**:
- Go to [SambaNova](https://fnf.dev/4jH8edk) and sign up for an account.
- Once you have an account, go to the API Key page and copy your API key.
- Paste your API key by creating a `.env` file as shown below:

```
SAMBANOVA_API_KEY=your_api_key
```


**Install Dependencies**:
   Ensure you have Python 3.11 or later installed.
   ```bash
   pip install streamlit openai crewai crewai-tools
   ```

---


---

## 📬 Stay Updated with Our Newsletter!
**Get a FREE Data Science eBook** 📖 with 150+ essential lessons in Data Science when you subscribe to our newsletter! Stay in the loop with the latest tutorials, insights, and exclusive resources. [Subscribe now!](https://join.dailydoseofds.com)

[![Daily Dose of Data Science Newsletter](https://github.com/patchy631/ai-engineering/blob/main/resources/join_ddods.png)](https://join.dailydoseofds.com)

---

## Contribution

Contributions are welcome! Please fork the repository and submit a pull request with your improvements.
