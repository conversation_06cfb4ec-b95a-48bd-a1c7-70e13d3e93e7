import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../../core/theme/app_theme.dart';
import '../pages/home_page.dart';

/// Interactive translation mode selector with animations
class TranslationModeSelector extends StatefulWidget {
  final TranslationMode currentMode;
  final Function(TranslationMode) onModeChanged;

  const TranslationModeSelector({
    super.key,
    required this.currentMode,
    required this.onModeChanged,
  });

  @override
  State<TranslationModeSelector> createState() => _TranslationModeSelectorState();
}

class _TranslationModeSelectorState extends State<TranslationModeSelector>
    with TickerProviderStateMixin {
  late AnimationController _selectionController;
  late Animation<double> _selectionAnimation;

  @override
  void initState() {
    super.initState();
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _selectionAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _selectionController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(TranslationModeSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentMode != widget.currentMode) {
      _selectionController.forward().then((_) {
        _selectionController.reset();
      });
    }
  }

  @override
  void dispose() {
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Modo de Tradução',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Mode Grid
            AnimationLimiter(
              child: GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                mainAxisSpacing: 12,
                crossAxisSpacing: 12,
                childAspectRatio: 2.5,
                children: TranslationMode.values.asMap().entries.map((entry) {
                  final index = entry.key;
                  final mode = entry.value;
                  
                  return AnimationConfiguration.staggeredGrid(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    columnCount: 2,
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: _ModeButton(
                          mode: mode,
                          isSelected: mode == widget.currentMode,
                          onTap: () => widget.onModeChanged(mode),
                          animation: _selectionAnimation,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),

            const SizedBox(height: 16),

            // Mode Description
            _buildModeDescription(widget.currentMode),
          ],
        ),
      ),
    );
  }

  Widget _buildModeDescription(TranslationMode mode) {
    final theme = Theme.of(context);
    
    final descriptions = {
      TranslationMode.text: 'Digite ou cole texto para tradução instantânea com detecção automática de idioma.',
      TranslationMode.image: 'Capture ou selecione imagens para extrair e traduzir texto automaticamente.',
      TranslationMode.audio: 'Grave áudio ou carregue arquivos para transcrição e tradução em tempo real.',
      TranslationMode.video: 'Processe vídeos para extrair áudio, legendas e conteúdo visual para tradução.',
    };

    final features = {
      TranslationMode.text: ['Detecção automática', 'Histórico', 'Cópia rápida'],
      TranslationMode.image: ['OCR avançado', 'Múltiplos formatos', 'Contexto visual'],
      TranslationMode.audio: ['Tempo real', 'Múltiplos falantes', 'Ruído reduzido'],
      TranslationMode.video: ['Legendas automáticas', 'Análise de frames', 'Sincronização'],
    };

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                mode.icon,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Modo ${mode.label}',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            descriptions[mode] ?? '',
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: (features[mode] ?? []).map((feature) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  feature,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

class _ModeButton extends StatefulWidget {
  final TranslationMode mode;
  final bool isSelected;
  final VoidCallback onTap;
  final Animation<double> animation;

  const _ModeButton({
    required this.mode,
    required this.isSelected,
    required this.onTap,
    required this.animation,
  });

  @override
  State<_ModeButton> createState() => _ModeButtonState();
}

class _ModeButtonState extends State<_ModeButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _hoverAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _hoverAnimation = Tween<double>(begin: 1, end: 1.05).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return MouseRegion(
      onEnter: (_) => _setHovered(true),
      onExit: (_) => _setHovered(false),
      child: AnimatedBuilder(
        animation: Listenable.merge([_hoverAnimation, widget.animation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _hoverAnimation.value,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                gradient: widget.isSelected
                    ? AppTheme.primaryGradient
                    : null,
                color: widget.isSelected
                    ? null
                    : (_isHovered 
                        ? AppTheme.primaryColor.withOpacity(0.1)
                        : Colors.grey[50]),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: widget.isSelected
                      ? Colors.transparent
                      : (_isHovered 
                          ? AppTheme.primaryColor.withOpacity(0.3)
                          : Colors.grey[300]!),
                  width: widget.isSelected ? 0 : 1,
                ),
                boxShadow: widget.isSelected
                    ? [
                        BoxShadow(
                          color: AppTheme.primaryColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: widget.isSelected
                                ? Colors.white.withOpacity(0.2)
                                : AppTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            widget.mode.icon,
                            color: widget.isSelected
                                ? Colors.white
                                : AppTheme.primaryColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            widget.mode.label,
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: widget.isSelected
                                  ? Colors.white
                                  : theme.textTheme.titleSmall?.color,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _setHovered(bool hovered) {
    setState(() {
      _isHovered = hovered;
    });
    
    if (hovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }
}
