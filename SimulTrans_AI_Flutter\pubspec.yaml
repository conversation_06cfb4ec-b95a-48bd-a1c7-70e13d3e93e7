name: simul_trans_ai
description: "Tradutor Simultâneo Multimodal Avançado com Google Gemma 3N - Su<PERSON>e a 140+ idiomas com processamento offline"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  
  # Core UI
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296
  
  # Gemma 3N Integration
  flutter_gemma: ^0.9.0
  
  # Multimodal Processing
  image_picker: ^1.0.4
  camera: ^0.10.5+9
  file_picker: ^8.0.0+1
  
  # Audio Processing
  record: ^5.0.4
  audioplayers: ^6.0.0
  speech_to_text: ^6.6.2
  
  # Video Processing
  video_player: ^2.8.2
  
  # UI & UX
  flutter_markdown: ^0.7.1
  lottie: ^3.1.0
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.1.1
  
  # Storage & Cache
  shared_preferences: ^2.5.2
  path_provider: ^2.1.5
  sqflite: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Network & API
  http: ^1.3.0
  dio: ^5.4.0
  connectivity_plus: ^5.0.2
  
  # Utils
  url_launcher: ^6.3.1
  package_info_plus: ^5.0.1
  device_info_plus: ^10.1.0
  permission_handler: ^11.3.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Internationalization
  intl: ^0.19.0
  flutter_localizations:
    sdk: flutter
  
  # Performance & Analytics
  firebase_core: ^2.24.2
  firebase_analytics: ^10.8.0
  firebase_crashlytics: ^3.4.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
    - assets/models/
    - assets/sounds/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
