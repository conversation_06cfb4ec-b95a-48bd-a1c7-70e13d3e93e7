{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# !pip install \"chonkie[semantic]\"\n", "# !pip install markitdown\n", "# !pip install qdrant-client\n", "# !pip install fastembed"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from crewai import Agent, Crew, Process, Task\n", "from crewai.project import <PERSON><PERSON><PERSON>, agent, crew, task\n", "from crewai_tools import SerperDevTool\n", "from crewai_tools import PDFSearchTool\n", "# from tools.custom_tool import DocumentSearchTool\n", "from tools.custom_tool import DocumentSearchTool\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/env_crewai/lib/python3.10/site-packages/model2vec/hf_utils.py:152: ResourceWarning: unclosed file <_io.TextIOWrapper name='/Users/<USER>/.cache/huggingface/hub/models--minishlab--potion-base-8M/snapshots/dcbec7aa2d52fc76754ac6291803feedd8c619ce/config.json' mode='r' encoding='UTF-8'>\n", "  config = json.load(open(config_path))\n", "ResourceWarning: Enable tracemalloc to get the object allocation traceback\n"]}], "source": ["pdf_tool = DocumentSearchTool(file_path='/Users/<USER>/Eigen/ai-engineering/agentic_rag/knowledge/dspy.pdf')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["web_search_tool = SerperDevTool()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import yaml\n", "with open('config/agents.yaml', 'r') as f:\n", "    agents_config = yaml.safe_load(f)\n", "\n", "with open('config/tasks.yaml', 'r') as f:\n", "    tasks_config = yaml.safe_load(f)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["retriever_agent = Agent(\n", "    config=agents_config['retriever_agent'],\n", "    verbose=True,\n", "    tools=[\n", "        pdf_tool,\n", "        web_search_tool\n", "    ]\n", ")\n", "\n", "response_synthesizer_agent = Agent(\n", "    config=agents_config['response_synthesizer_agent'],\n", "    verbose=True\n", ")\n", "\n", "retrieval_task = Task(\n", "    description=tasks_config['retrieval_task']['description'],\n", "    expected_output=tasks_config['retrieval_task']['expected_output'],  \n", "    agent=retriever_agent\n", ")\n", "\n", "response_task = Task(\n", "    description=tasks_config['response_task']['description'],\n", "    expected_output=tasks_config['response_task']['expected_output'], \n", "    agent=response_synthesizer_agent\n", ")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-06 00:22:52,271 - ********** - __init__.py-__init__:537 - WARNING: Overriding of current TracerProvider is not allowed\n"]}], "source": ["crew = Crew(\n", "\t\t\tagents=[retriever_agent, response_synthesizer_agent], \n", "\t\t\ttasks=[retrieval_task, response_task],\n", "\t\t\tprocess=Process.sequential,\n", "\t\t\tverbose=True,\n", "\t\t\t# process=Process.hierarchical, # In case you wanna use that instead https://docs.crewai.com/how-to/Hierarchical/\n", "\t\t)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRetrieve relevant information to answer the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mRetrieve the most relevant information from the available sources for the user query: Who is elon musk?\n", "\u001b[00m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/env_crewai/lib/python3.10/site-packages/crewai/tools/tool_usage.py:162: PydanticDeprecatedSince20: The `schema` method is deprecated; use `model_json_schema` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.10/migration/\n", "  acceptable_args = tool.args_schema.schema()[\"properties\"].keys()  # type: ignore # Item \"None\" of \"type[BaseModel] | None\" has no attribute \"schema\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRetrieve relevant information to answer the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Thought:\u001b[00m \u001b[92mI will first search for relevant information in the available documents about Elon Musk.\u001b[00m\n", "\u001b[95m## Using tool:\u001b[00m \u001b[92mDocumentSearchTool\u001b[00m\n", "\u001b[95m## Tool Input:\u001b[00m \u001b[92m\n", "\"{\\\"query\\\": \\\"Who is elon musk?\\\"}\"\u001b[00m\n", "\u001b[95m## Tool Output:\u001b[00m \u001b[92m\n", "Content: (b) if Google believes, in good faith, that the Distributor has violated or caused Google to violate any\n", "Anti-Bribery Laws (as defined in Clause 8.5) or that such a violation is reasonably likely to occur,\n", "Source: 4-pl\n", "=========\n", "FINAL ANSWER: This Agreement is governed by English law.\n", "SOURCES: 28-pl\n", "\n", "QUESTION: What did the president say about <PERSON>?\n", "=========\n", "Content: <PERSON>am Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet.\n", "Justices of the Supreme Court. My fellow Americans.\n", "Last year COVID-19 kept us apart. This year we are finally together again.\n", "Tonight, we meet as Democrats Republicans and Independents. But most importantly as Americans.\n", "With a duty to one another to the American people to the Constitution.\n", "And with an unwavering resolve that freedom will always triumph over tyranny.\n", "Six days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to\n", "his menacing ways. But he badly miscalculated.\n", "He thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined.\n", "He met the Ukrainian people.\n", "From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world.\n", "Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending\n", "their homeland.\n", "Source: 0-pl\n", "\n", "___\n", "result = total toys\n", "return result\n", "\n", "Q: <PERSON> had 20 lollipops. He gave <PERSON> some lollipops. Now <PERSON> has 12 lollipops. How many lollipops did <PERSON> give to\n", "<PERSON>?\n", "\n", "\n", "___\n", "My fellow Americans|tonight , we have gathered in a sacred space|the citadel of our democracy.\n", "In this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done\n", "great things.\n", "We have fought for freedom, expanded liberty, defeated totalitarianism and terror.\n", "And built the strongest, freest, and most prosperous nation the world has ever known.\n", "Now is the hour.\n", "Our moment of responsibility.\n", "\n", "___\n", "To get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health.\n", "It’s based on DARPA|the Defense Department project that led to the Internet, GPS, and so much more.\n", "\n", "___\n", "well as the NSF under CAREER grant CNS-1651570. Any opinions, findings, and conclusions or\n", "recommendations expressed in this material are those of the authors and do not necessarily reflect\n", "the views of the National Science Foundation. <PERSON> is supported by the Apple Scholars in\n", "AI/ML fellowship.\n", "\n", "\\usepackage[pdftex]{graphicx} ...\n", "\\includegraphics[width=0.8\\linewidth]{myfile.pdf}\n", "\n", "REFERENCES\n", "\n", "\n", "___\n", "I checked: {query}\n", "\n", "___\n", " In International Conference on Machine\n", "Learning, pp.\n", "___\n", " arXiv preprint arXiv:2305.03495, 2023.\n", "\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Answering complex\n", "In Proceedings of the 2019 Con-\n", "\n", "___\n", "(1) Search[entity], which searches the exact entity on Wikipedia and returns the first paragraph if it exists. If not, it\n", "will return some similar entities to search.\n", "(2) Lookup[keyword], which returns the next sentence containing keyword in the current passage.\n", "(3) Finish[answer], which returns the answer and finishes the task.\n", "Here are some examples.\n", "Question: What is the elevation range for the area that the eastern sector of the Colorado orogeny extends into?\n", "Action 1: Search[Colorado orogeny]\n", "Observation 1: The Colorado orogeny was an episode of mountain building (an orogeny) in Colorado and surrounding areas.\n", "Action 2: Lookup[eastern sector]\n", "Observation 2: (Result 1 / 1) The eastern sector extends into the High Plains and is called the Central Plains orogeny.\n", "Action 3: Search[High Plains]\n", "Observation 3: High Plains refers to one of two distinct land regions:\n", "Action 4: Search[High Plains (United States)]\n", "Observation 4: The High Plains are a subregion of the Great Plains. From east to west, the High Plains rise in elevation\n", "from around 1,800 to 7,000 ft (550 to 2,130 m).[3]\n", "Action 5: Finish[1,800 to 7,000 ft]\n", "Question: Musician and satirist <PERSON> wrote a song about the \"The Simpsons\" character <PERSON><PERSON><PERSON>, who <PERSON>\n", "named after who?\n", "Action 1: Search[Milhouse]\n", "Observation 1: <PERSON><PERSON><PERSON> is a recurring character in the Fox animated television series The Simpsons\n", "voiced by <PERSON> and created by <PERSON>.\n", "Action 2: Lookup[named after]\n", "Observation 2: (Result 1 / 1) Milhouse was named after U.S. president <PERSON>, whose middle name was <PERSON><PERSON><PERSON><PERSON>.\n", "Action 3: Finish[<PERSON>]\n", "Question: Which documentary is about Finnish rock groups, <PERSON> or The Saimaa Gesture?\n", "Action 1: Search[<PERSON>]\n", "Observation 1: Could not find [<PERSON>]. Similar: [<PERSON><PERSON>’, ’Seventh Avenue (Manhattan)’,\n", "’<PERSON> Jr. State Office Building’, <PERSON><PERSON>, <PERSON><PERSON>, ’<PERSON> (film)’,\n", "\n", "___\n", " He gave <PERSON> some lollipops. Now <PERSON> has 12 lollipops. How many lollipops did <PERSON> give\n", "\n", "to <PERSON>?\"\"\"\n", "\n", "jason lollipops initial = 20\n", "jason lollipops after = 12\n", "denny lollipops = jason lollipops initial - jason lollipops after\n", "result = denny lollipops\n", "return result\n", "\n", "Q: <PERSON> had 32 chocolates and her sister had 42. If they ate 35, how many pieces do they have left in total?\n", "\n", "# solution in Python:\n", "\n", "def solution():\n", "\n", "\"\"\"<PERSON> had 32 chocolates and her sister had 42. If they ate 35, how many pieces do they have left in total?\"\"\"\n", "leah chocolates = 32\n", "sister chocolates = 42\n", "total chocolates = leah chocolates + sister chocolates\n", "chocolates eaten = 35\n", "chocolates left = total chocolates - chocolates eaten\n", "result = chocolates left\n", "return result\n", "\n", "\u001b[00m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/env_crewai/lib/python3.10/site-packages/crewai/tools/tool_usage.py:162: PydanticDeprecatedSince20: The `schema` method is deprecated; use `model_json_schema` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.10/migration/\n", "  acceptable_args = tool.args_schema.schema()[\"properties\"].keys()  # type: ignore # Item \"None\" of \"type[BaseModel] | None\" has no attribute \"schema\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRetrieve relevant information to answer the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Thought:\u001b[00m \u001b[92mThought: The search in the document did not yield relevant information regarding Elon Musk. I will now conduct a web search to find the information.\u001b[00m\n", "\u001b[95m## Using tool:\u001b[00m \u001b[92mSearch the internet\u001b[00m\n", "\u001b[95m## Tool Input:\u001b[00m \u001b[92m\n", "\"{\\\"search_query\\\": \\\"Who is <PERSON><PERSON>?\\\"}\"\u001b[00m\n", "\u001b[95m## Tool Output:\u001b[00m \u001b[92m\n", "\n", "Search results: Title: Who is <PERSON><PERSON> and what is his net worth? - BBC\n", "Link: https://www.bbc.com/news/business-61234231\n", "Snippet: The boss of X (formerly Twitter), <PERSON>sla and SpaceX is a billionaire and a business celebrity.\n", "---\n", "Title: Mr <PERSON><PERSON> - Fellow <PERSON><PERSON> | Royal Society\n", "Link: https://royalsociety.org/people/elon-musk-13829/\n", "Snippet: In 2017, SpaceX made further history by re-flying both a Falcon 9 rocket and Dragon spacecraft for the first time. By pioneering the development of reusable ...\n", "---\n", "Title: <PERSON><PERSON> - Tesla Investor Relations\n", "Link: https://ir.tesla.com/corporate/elon-musk\n", "Snippet: <PERSON><PERSON> is Technoking of Tesla and has served as our Chief Executive Officer since October 2008 and as a member of the Board since April 2004.\n", "---\n", "Title: <PERSON><PERSON> - <PERSON>\n", "Link: https://www.forbes.com/profile/elon-musk/\n", "Snippet: Real Time Net Worth · <PERSON><PERSON> Musk cofounded seven companies, including electric car maker Tesla, rocket producer SpaceX and artificial intelligence startup xAI.\n", "---\n", "Title: <PERSON><PERSON> changes his name to <PERSON><PERSON><PERSON> on X - BBC\n", "Link: https://www.bbc.com/news/articles/cy53vz1qpx1o\n", "Snippet: The world's richest man sparks speculation after changing his name and using a picture of <PERSON><PERSON><PERSON> the <PERSON>.\n", "---\n", "Title: <PERSON><PERSON> | Tesla\n", "Link: https://www.tesla.com/elon-musk\n", "Snippet: As the co-founder and CEO of Tesla, <PERSON><PERSON> leads all product design, engineering and global manufacturing of the company's electric vehicles, battery products ...\n", "---\n", "Title: <PERSON><PERSON> - WSJ Spotlight Coverage, Recent News\n", "Link: https://www.wsj.com/topics/person/elon-musk\n", "Snippet: <PERSON><PERSON> is the CEO of electric-vehicle maker Tesla, whose board he joined in 2004. During his two decades there, the company has grown to be the global ...\n", "---\n", "Title: <PERSON><PERSON> | Biography, SpaceX, Tesla, Twitter, X, Trump, & Facts\n", "Link: https://www.britannica.com/money/Elon-Musk\n", "Snippet: <PERSON><PERSON>, the South African-born entrepreneur widely known as the founder of SpaceX and Tesla, is renowned for his advances in electric vehicles and space ...\n", "---\n", "Title: How did <PERSON><PERSON> become the most powerful man in Washington?\n", "Link: https://www.youtube.com/watch?v=8-iFXyLQQsg\n", "Snippet: <PERSON><PERSON>, the world's richest man, is also the most powerful man in Washington right now. The panel discusses how this came to be and if ...\n", "---\n", "\n", "\n", "\n", "You ONLY have access to the following tools, and should NEVER make up tools that are not listed here:\n", "\n", "Tool Name: DocumentSearchTool\n", "Tool Arguments: {'query': {'description': 'Query to search the document.', 'type': 'str'}}\n", "Tool Description: Search the document for the given query.\n", "Tool Name: Search the internet\n", "Tool Arguments: {'search_query': {'description': 'Mandatory search query you want to use to search the internet', 'type': 'str'}}\n", "Tool Description: A tool that can be used to search the internet with a search_query.\n", "\n", "Use the following format:\n", "\n", "Thought: you should always think about what to do\n", "Action: the action to take, only one name of [DocumentSearchTool, Search the internet], just the name, exactly as it's written.\n", "Action Input: the input to the action, just a simple python dictionary, enclosed in curly braces, using \" to wrap keys and values.\n", "Observation: the result of the action\n", "\n", "Once all necessary information is gathered:\n", "\n", "Thought: I now know the final answer\n", "Final Answer: the final answer to the original input question\n", "\u001b[00m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/env_crewai/lib/python3.10/site-packages/crewai/tools/tool_usage.py:162: PydanticDeprecatedSince20: The `schema` method is deprecated; use `model_json_schema` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.10/migration/\n", "  acceptable_args = tool.args_schema.schema()[\"properties\"].keys()  # type: ignore # Item \"None\" of \"type[BaseModel] | None\" has no attribute \"schema\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRetrieve relevant information to answer the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Thought:\u001b[00m \u001b[92mThought: I have found multiple relevant sources from the web about Elon Musk. I will now extract the complete content from the best source.\u001b[00m\n", "\u001b[95m## Using tool:\u001b[00m \u001b[92mSearch the internet\u001b[00m\n", "\u001b[95m## Tool Input:\u001b[00m \u001b[92m\n", "\"{\\\"search_query\\\": \\\"Elon Musk biography\\\"}\"\u001b[00m\n", "\u001b[95m## Tool Output:\u001b[00m \u001b[92m\n", "\n", "Search results: Title: <PERSON><PERSON> - <PERSON>\n", "Link: https://en.wikipedia.org/wiki/Elon_Musk\n", "Snippet: <PERSON><PERSON> is a businessman known for his key roles in the space company SpaceX and the automotive company Tesla, Inc. He is also known for his ...\n", "---\n", "Title: <PERSON><PERSON>: Biography, Entrepreneur, SpaceX and Tesla Founder\n", "Link: https://www.biography.com/business-leaders/elon-musk\n", "Snippet: <PERSON><PERSON> was born on June 28, 1971, in Pretoria, South Africa. His mother, <PERSON><PERSON>, is a Canadian model and the oldest woman to star in ...\n", "---\n", "Title: <PERSON><PERSON>: <PERSON><PERSON>, Walter: 9781982181284 - Amazon.com\n", "Link: https://www.amazon.com/Elon-Musk-<PERSON>-<PERSON>/dp/1982181281\n", "Snippet: The #1 New York Times bestseller from the author of <PERSON>--this is the astonishingly intimate story of the most fascinating and controversial innovator of our era--a rule-breaking visionary who helped to lead the world into the era of electric vehicles, private space exploration, and artificial intelligence.\n", "---\n", "Title: <PERSON><PERSON> | Book by <PERSON> | Official Publisher Page\n", "Link: https://www.simonandschuster.com/books/Elon-Musk/<PERSON>-<PERSON>/9781982181284\n", "Snippet: The #1 New York Times bestseller from the author of <PERSON>--this is the astonishingly intimate story of the most fascinating and controversial innovator of our era--a rule-breaking visionary who helped to lead the world into the era of electric vehicles, private space exploration, and artificial intelligence.\n", "---\n", "Title: <PERSON><PERSON> | Biography, SpaceX, Tesla, Twitter, X, Trump, & Facts\n", "Link: https://www.britannica.com/money/Elon-Musk\n", "Snippet: South African-born American entrepreneur who cofounded the electronic-payment firm PayPal and formed SpaceX, maker of launch vehicles and spacecraft.\n", "---\n", "Title: Book Review: '<PERSON><PERSON>,' by <PERSON> - The New York Times\n", "Link: https://www.nytimes.com/2023/09/09/books/review/elon-musk-walter-isaacson.html\n", "Snippet: <PERSON>'s biography of the billionaire entrepreneur depicts a mercurial “man-child” with grandiose ambitions and an ego to match.\n", "---\n", "Title: Who is <PERSON><PERSON> and what is his net worth? - BBC\n", "Link: https://www.bbc.com/news/business-61234231\n", "Snippet: Born in Pretoria, South Africa, Mr <PERSON><PERSON> showed his talents for entrepreneurship early, going door-to-door with his brother selling homemade ...\n", "---\n", "Title: <PERSON><PERSON>: <PERSON>sla, SpaceX, and the Quest for a Fantastic Future\n", "Link: https://www.amazon.com/Elon-Musk-SpaceX-Fantastic-Future/dp/0062301233\n", "Snippet: In the spirit of <PERSON> and <PERSON><PERSON>, <PERSON><PERSON> is both an illuminating and authorized look at the extraordinary life of one of Silicon Valley's most exciting, unpredictable, and ambitious entrepreneurs--a real-life <PERSON>--and a fascinating exploration of the renewal of American invention and its new ``makers.''\n", "---\n", "Title: <PERSON><PERSON>'s Biography Summary - Dante\n", "Link: https://dantekim.com/notes/elon-musk-tesla-spacex-and-the-quest-for-a-fantastic-future/\n", "Snippet: In his biography you get to dig much deeper and see his flaws, extreme perseverance and genius in such close detail.\n", "---\n", "Title: <PERSON><PERSON> By <PERSON>: A Biography Book On Hardcore Hope\n", "Link: https://www.youtube.com/watch?v=srlhWHvRVuQ\n", "Snippet: <PERSON><PERSON>: the name synonymous with <PERSON><PERSON>, SpaceX, and audacious dreams of colonizing Mars. But what's the man behind the ambition?\n", "---\n", "\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mRetrieve relevant information to answer the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "**<PERSON><PERSON>** (born June 28, 1971) is a South African-born American entrepreneur and business magnate. He is the founder, CEO, and chief engineer of SpaceX; early investor, CEO, and product architect of Tesla, Inc.; founder of The Boring Company; co-founder of Neuralink; and co-founder and initial co-chairman of OpenAI. <PERSON><PERSON> is known for his ambitious vision for the future, including the colonization of Mars and the advent of sustainable energy. \n", "\n", "Born in Pretoria, South Africa, <PERSON><PERSON> showed an early interest in computing and technology. He taught himself computer programming at a young age and created a video game, which he sold at the age of 12. After moving to the U.S. to study at the University of Pennsylvania, <PERSON><PERSON> dropped out to pursue his entrepreneurial ambitions, co-founding Zip2, X.com, which later became PayPal, and then aiming to revolutionize transportation with his work on electric vehicles through Tesla.\n", "\n", "<PERSON><PERSON> established SpaceX in 2002 to reduce space transportation costs and enable the colonization of Mars. He made significant advancements with the Falcon rocket series and the Dragon spacecraft. With Tesla, <PERSON><PERSON> aims to accelerate the world's transition to sustainable energy, to help combat climate change.\n", "\n", "<PERSON><PERSON> has been recognized for his contributions and endeavors in the technology industry and has been listed among the most powerful and influential people in the world.\n", "\n", "For a more detailed exploration of his life, contributions, and impact, you can read further on his [Wikipedia page](https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>).\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mResponse synthesizer agent for the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Task:\u001b[00m \u001b[92mSynthesize the final response for the user query: Who is elon musk?\n", "\u001b[00m\n", "\n", "\n", "\u001b[1m\u001b[95m# Agent:\u001b[00m \u001b[1m\u001b[92mResponse synthesizer agent for the user query: Who is elon musk?\u001b[00m\n", "\u001b[95m## Final Answer:\u001b[00m \u001b[92m\n", "**<PERSON><PERSON>** (born June 28, 1971) is a South African-born American entrepreneur and business magnate, renowned for his groundbreaking contributions in various technology sectors. He is the founder, CEO, and chief engineer of SpaceX; an early investor, CEO, and product architect of Tesla, Inc.; the founder of The Boring Company; and a co-founder of Neuralink and OpenAI. <PERSON><PERSON> is celebrated for his ambitious vision for the future, which includes plans for the colonization of Mars and the promotion of sustainable energy.\n", "\n", "Born in Pretoria, South Africa, <PERSON><PERSON> displayed a keen interest in computing from a young age, teaching himself programming and creating a video game that he sold by the age of 12. He moved to the United States to study at the University of Pennsylvania but left before completing his degree to pursue entrepreneurial ventures, co-founding Zip2 and later X.com, which evolved into PayPal. His ambitions continued with Tesla, where he aims to revolutionize transportation through electric vehicles and combat climate change.\n", "\n", "<PERSON><PERSON> started SpaceX in 2002 with the goal of reducing space transportation costs and enabling the colonization of Mars, achieving significant milestones with the Falcon rocket series and the Dragon spacecraft. Recognized globally, <PERSON><PERSON> has been cited as one of the most powerful and influential people in the world due to his innovative works and contributions to technology. For further details about his life and impact, refer to his [Wikipedia page](https://en.wikipedia.org/wiki/<PERSON><PERSON>_Mu<PERSON>).\u001b[00m\n", "\n", "\n", "**<PERSON><PERSON>** (born June 28, 1971) is a South African-born American entrepreneur and business magnate, renowned for his groundbreaking contributions in various technology sectors. He is the founder, CEO, and chief engineer of SpaceX; an early investor, CEO, and product architect of Tesla, Inc.; the founder of The Boring Company; and a co-founder of Neuralink and OpenAI. <PERSON><PERSON> is celebrated for his ambitious vision for the future, which includes plans for the colonization of Mars and the promotion of sustainable energy.\n", "\n", "Born in Pretoria, South Africa, <PERSON><PERSON> displayed a keen interest in computing from a young age, teaching himself programming and creating a video game that he sold by the age of 12. He moved to the United States to study at the University of Pennsylvania but left before completing his degree to pursue entrepreneurial ventures, co-founding Zip2 and later X.com, which evolved into PayPal. His ambitions continued with Tesla, where he aims to revolutionize transportation through electric vehicles and combat climate change.\n", "\n", "<PERSON><PERSON> started SpaceX in 2002 with the goal of reducing space transportation costs and enabling the colonization of Mars, achieving significant milestones with the Falcon rocket series and the Dragon spacecraft. Recognized globally, <PERSON><PERSON> has been cited as one of the most powerful and influential people in the world due to his innovative works and contributions to technology. For further details about his life and impact, refer to his [Wikipedia page](https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>).\n"]}], "source": ["result = crew.kickoff(inputs={\"query\": \"Who is elon musk?\"})\n", "print(result)"]}], "metadata": {"kernelspec": {"display_name": "env_crewai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}