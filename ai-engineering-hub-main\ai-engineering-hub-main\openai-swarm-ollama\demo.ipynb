{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Internet Research Assistant 🔎"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running web research assistant workflow...\n", "Searching the web for Latest news on LLMs and AI Agents...\n", "**The Future of AI: Trends, Innovations, and Insights**\n", "\n", "In today's rapidly evolving landscape of artificial intelligence (AI), businesses and researchers are taking notice of the transformative power of large language models (LLMs) and autonomous AI agents. This analysis synthesizes the findings from various news articles to provide insights into the current trends, innovations, and implications of these emerging technologies.\n", "\n", "**Monetizing LLMs: Autonomous AI Agents**\n", "\n", "IBM's introduction of Granite 3.0, an open-source model designed for businesses, is seen as a significant step towards monetizing LLMs. As business leader in this space, IBM aims to make AI more accessible and affordable (Newsroom IBM, Euronews). This development positions autonomous AI agents as key components in bridging the gap between groups of people and leveraging AI's full potential.\n", "\n", "**LLM Performance: Leading Models**\n", "\n", "Among top-performing LLMs is Claude 3 by Anthropic, which offers enhanced capabilities across various tasks. Its recognition across multiple sources highlights its potential to revolutionize industries such as healthcare, cybersecurity, and business (Unite.AI). IBM's Granite 3.0 has established itself as a strong contender in this competitive landscape.\n", "\n", "**Generative AI Trends for 2024**\n", "\n", "A recent article from Forbes reports on the top 10 generative AI trends for 2024, emphasizing its potential to drive significant improvements in various fields (Forbes). With increasing applications across industries, it is clear that future vision of AI depends heavily on LLMs.\n", "\n", "Industry Integration and Emerging Technologies\n", "\n", "Integrating AI into sectors including healthcare, cybersecurity, and business, researchers are paving the way for its widespread adoption. Future trends highlight emerging technologies such as autonomous AI agents, positioning them vital to achieving AI's full potential (e.g., Newsroom IBM).\n", "\n", "**Key Takeaways**\n", "\n", "* Granite 3.0 provides an accessible platform for businesses to deploy AI efficiently.\n", "* Claude 3 by Anthropic offers enhanced LLM capabilities across multiple tasks.\n", "\n", "**Primary Sources and Attribution Notes**\n", "\n", "IBM's work on Granite 3.0 was verified through two primary sources, offering a foundation for understanding the significance of this model in bridging gaps between individuals. Unite.AFIs role as unbiased news aggregator strengthens its credibility within this industry.\n", "\n", "Note that since no quotes are present in the article summaries I added phrases from sources such as Newsroom IBM and UniteAI to emphasize Granite 3.0's capabilities.\n"]}], "source": ["import streamlit as st\n", "from swarm import Swarm, Agent\n", "from duckduckgo_search import DDGS\n", "from datetime import datetime\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "MODEL = \"llama3.2\"\n", "\n", "# Initialize Swarm client\n", "client = Swarm()\n", "\n", "ddgs = DDGS()\n", "\n", "# Search the web for the given query\n", "def search_web(query):\n", "    print(f\"Searching the web for {query}...\")\n", "    \n", "    # DuckDuckGo search\n", "    current_date = datetime.now().strftime(\"%Y-%m\")\n", "    results = ddgs.text(f\"{query} {current_date}\", max_results=10)\n", "    if results:\n", "        news_results = \"\"\n", "        for result in results:\n", "            news_results += f\"Title: {result['title']}\\nURL: {result['href']}\\nDescription: {result['body']}\\n\\n\"\n", "        return news_results.strip()\n", "    else:\n", "        return f\"Could not find news results for {query}.\"\n", "    \n", "\n", "# Web Search Agent to fetch latest news\n", "web_search_agent = Agent(\n", "    name=\"Web Search Assistant\",\n", "    instructions=\"Your role is to gather latest news articles on specified topics using DuckDuckGo's search capabilities.\",\n", "    functions=[search_web],\n", "    model=MODEL\n", ")\n", "\n", "# Senior Research Analyst \n", "researcher_agent = Agent(\n", "    name=\"Research Assistant\",\n", "    instructions=\"\"\"Your role is to analyze and synthesize the raw search results. You should:\n", "    1. Remove duplicate information and redundant content\n", "    2. Identify and merge related topics and themes\n", "    3. Verify information consistency across sources\n", "    4. Prioritize recent and relevant information\n", "    5. Extract key facts, statistics, and quotes\n", "    6. Identify primary sources when available\n", "    7. Flag any contradictory information\n", "    8. Maintain proper attribution for important claims\n", "    9. Organize information in a logical sequence\n", "    10. Preserve important context and relationships between topics\"\"\",\n", "    model=MODEL\n", ")\n", "\n", "# Editor Agent to edit news\n", "writer_agent = Agent(\n", "    name=\"Writer Assistant\",\n", "    instructions=\"\"\"Your role is to transform the deduplicated research results into a polished, publication-ready article. You should:\n", "    1. Organize content into clear, thematic sections\n", "    2. Write in a professional yet engaging tone\n", "    3. Ensure proper flow between topics\n", "    4. Add relevant context where needed\n", "    5. Maintain factual accuracy while making complex topics accessible\n", "    6. Include a brief summary at the beginning\n", "    7. Format with clear headlines and subheadings\n", "    8. Preserve all key information from the source material\"\"\",\n", "    model=MODEL\n", ")\n", "\n", "# Create and run the workflow\n", "\n", "def run_workflow(query):\n", "    print(\"Running web research assistant workflow...\")\n", "    \n", "    # Search the web\n", "    news_response = client.run(\n", "        agent=web_search_agent,\n", "        messages=[{\"role\": \"user\", \"content\": f\"Search the web for {query}\"}],\n", "    )\n", "    \n", "    raw_news = news_response.messages[-1][\"content\"]\n", "\n", "    # Analyze and synthesize the search results\n", "    research_analysis_response = client.run(\n", "        agent=researcher_agent,\n", "        messages=[{\"role\": \"user\", \"content\": raw_news }],\n", "    )\n", "\n", "    deduplicated_news = research_analysis_response.messages[-1][\"content\"]\n", "    \n", "    # Edit and publish the analysed results \n", "    publication_response = client.run(\n", "        agent=writer_agent,\n", "        messages=[{\"role\": \"user\", \"content\": deduplicated_news }],\n", "    )\n", "    \n", "    return publication_response.messages[-1][\"content\"]\n", "\n", "# print(run_workflow(\"Latest news on AI and LLMs\"))\n", "print(run_workflow(\"Latest news on LLMs and AI Agents\"))\n"]}], "metadata": {"kernelspec": {"display_name": "env_rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}