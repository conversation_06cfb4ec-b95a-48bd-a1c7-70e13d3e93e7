[project]
name = "brand_monitoring_flow"
version = "0.1.0"
description = "brand-monitoring-flow using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.102.0,<1.0.0",
]

[project.scripts]
kickoff = "brand_monitoring_flow.main:kickoff"
plot = "brand_monitoring_flow.main:plot"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "flow"
