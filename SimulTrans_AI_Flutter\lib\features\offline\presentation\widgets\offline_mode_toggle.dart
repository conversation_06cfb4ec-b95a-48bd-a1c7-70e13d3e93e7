import 'package:flutter/material.dart';
import '../../../../core/services/real_translation_service.dart';
import '../../../../core/services/gemma_downloader_service.dart';

class OfflineModeToggle extends StatefulWidget {
  const OfflineModeToggle({super.key});

  @override
  State<OfflineModeToggle> createState() => _OfflineModeToggleState();
}

class _OfflineModeToggleState extends State<OfflineModeToggle> {
  bool _isOfflineMode = false;
  bool _isOfflineAvailable = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkOfflineAvailability();
    _isOfflineMode = RealTranslationService.instance.isOfflineMode;
  }

  Future<void> _checkOfflineAvailability() async {
    setState(() => _isLoading = true);
    
    try {
      final isAvailable = await RealTranslationService.instance.isOfflineAvailable();
      setState(() {
        _isOfflineAvailable = isAvailable;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isOfflineAvailable = false;
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleOfflineMode(bool value) async {
    if (!_isOfflineAvailable && value) {
      _showOfflineNotAvailableDialog();
      return;
    }

    setState(() => _isLoading = true);

    try {
      if (value) {
        await RealTranslationService.instance.switchToOfflineMode();
      } else {
        await RealTranslationService.instance.switchToOnlineMode();
      }
      
      setState(() {
        _isOfflineMode = value;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              value 
                ? '📱 Switched to offline mode' 
                : '🌐 Switched to online mode'
            ),
            backgroundColor: value ? Colors.orange : Colors.blue,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to switch mode: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showOfflineNotAvailableDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Offline Mode Not Available'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('No offline models are downloaded.'),
            SizedBox(height: 16),
            Text('To use offline mode:'),
            Text('1. Tap the offline models button (⚡)'),
            Text('2. Download a Gemma 3N model'),
            Text('3. Return here to enable offline mode'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to model management
              Navigator.of(context).pushNamed('/model-management');
            },
            child: const Text('Download Models'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isOfflineMode ? Icons.offline_bolt : Icons.cloud,
                  color: _isOfflineMode ? Colors.orange : Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Translation Mode',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _isOfflineMode 
                          ? 'Using offline Gemma 3N models'
                          : 'Using online Gemini 2.5 Flash',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  Switch(
                    value: _isOfflineMode,
                    onChanged: _toggleOfflineMode,
                    activeColor: Colors.orange,
                    inactiveThumbColor: Colors.blue,
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (_isOfflineMode ? Colors.orange : Colors.blue).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: (_isOfflineMode ? Colors.orange : Colors.blue).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _isOfflineMode ? Icons.info : Icons.info_outline,
                    size: 16,
                    color: _isOfflineMode ? Colors.orange : Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _isOfflineMode
                        ? 'Offline mode: No internet required, uses device processing'
                        : 'Online mode: Requires internet, uses cloud processing',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: (_isOfflineMode ? Colors.orange : Colors.blue).withOpacity(0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (!_isOfflineAvailable) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.amber.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.warning,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Offline models not available. Download Gemma 3N models to enable offline mode.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.amber.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
