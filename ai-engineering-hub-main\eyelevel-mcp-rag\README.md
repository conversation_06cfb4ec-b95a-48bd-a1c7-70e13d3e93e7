# Build an MCP server for RAG over comple real world docs

This server leverages [GroundX](https://eyelevel.ai/)'s state-of-the-art document search and retrieval capabilities.

You can quickly test it on your own complex docs [here](https://eyelevel.ai/).

### Setup

To sync dependencies, run:

```sh
uv sync
```

### Environment Variables

You need to set up the following environment variables:

```sh
GROUNDX_API_KEY=...
```
[Get your GroundX API keys here](https://eyelevel.ai/)

Ensure these variables are configured correctly before running the application use `.env.example` as reference and create your own `.env` file.

---

## 📬 Stay Updated with Our Newsletter!
**Get a FREE Data Science eBook** 📖 with 150+ essential lessons in Data Science when you subscribe to our newsletter! Stay in the loop with the latest tutorials, insights, and exclusive resources. [Subscribe now!](https://join.dailydoseofds.com)

[![Daily Dose of Data Science Newsletter](https://github.com/patchy631/ai-engineering/blob/main/resources/join_ddods.png)](https://join.dailydoseofds.com)

---

## Contribution

Contributions are welcome! Please fork the repository and submit a pull request with your improvements.

