import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Gemma 3N Model Configuration
class Gemma3NModel {
  final String modelUrl;
  final String modelFilename;
  final String modelName;
  final String description;
  final int sizeInMB;

  const Gemma3NModel({
    required this.modelUrl,
    required this.modelFilename,
    required this.modelName,
    required this.description,
    required this.sizeInMB,
  });
}

/// Available Gemma 3N Models
class Gemma3NModels {
  // Gemma 3N E2B-IT (2B effective parameters)
  static const e2bIt = Gemma3NModel(
    modelUrl: 'https://huggingface.co/google/gemma-3n-E2B-it/resolve/main/model.gguf',
    modelFilename: 'gemma-3n-e2b-it.gguf',
    modelName: 'Gemma 3N E2B-IT',
    description: '2B effective parameters - Optimized for mobile devices',
    sizeInMB: 1800, // Approximate size
  );

  // Gemma 3N E4B-IT (4B effective parameters)
  static const e4bIt = Gemma3NModel(
    modelUrl: 'https://huggingface.co/google/gemma-3n-E4B-it/resolve/main/model.gguf',
    modelFilename: 'gemma-3n-e4b-it.gguf',
    modelName: 'Gemma 3N E4B-IT',
    description: '4B effective parameters - Best balance of performance and efficiency',
    sizeInMB: 3600, // Approximate size
  );

  static const List<Gemma3NModel> availableModels = [e2bIt, e4bIt];
  static const Gemma3NModel defaultModel = e4bIt; // Default to E4B-IT
}

/// Service for downloading and managing Gemma 3N models
class GemmaDownloaderService {
  final Gemma3NModel model;
  static const String _huggingFaceToken = 'YOUR_HUGGING_FACE_TOKEN_HERE';

  GemmaDownloaderService({required this.model});

  String get _preferenceKey => 'model_downloaded_${model.modelFilename}';

  /// Get the local file path for the model
  Future<String> getModelFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/models/${model.modelFilename}';
  }

  /// Check if the model exists locally
  Future<bool> isModelDownloaded() async {
    final prefs = await SharedPreferences.getInstance();
    if (prefs.getBool(_preferenceKey) ?? false) {
      final filePath = await getModelFilePath();
      final file = File(filePath);
      if (file.existsSync()) {
        return true;
      }
    }

    try {
      final filePath = await getModelFilePath();
      final file = File(filePath);

      final Map<String, String> headers = _huggingFaceToken.isNotEmpty
          ? {'Authorization': 'Bearer $_huggingFaceToken'}
          : {};
      
      final headResponse = await http.head(
        Uri.parse(model.modelUrl),
        headers: headers,
      );

      if (headResponse.statusCode == 200) {
        final contentLengthHeader = headResponse.headers['content-length'];
        if (contentLengthHeader != null) {
          final remoteFileSize = int.parse(contentLengthHeader);
          if (file.existsSync() && await file.length() == remoteFileSize) {
            await prefs.setBool(_preferenceKey, true);
            return true;
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking model existence: $e');
      }
    }
    
    await prefs.setBool(_preferenceKey, false);
    return false;
  }

  /// Download the Gemma 3N model with progress tracking
  Future<void> downloadModel({
    String? token,
    required Function(double progress, int downloadedMB, int totalMB) onProgress,
  }) async {
    http.StreamedResponse? response;
    IOSink? fileSink;
    final prefs = await SharedPreferences.getInstance();

    try {
      final filePath = await getModelFilePath();
      final file = File(filePath);
      
      // Create models directory if it doesn't exist
      await file.parent.create(recursive: true);

      int downloadedBytes = 0;
      if (file.existsSync()) {
        downloadedBytes = await file.length();
      }

      final request = http.Request('GET', Uri.parse(model.modelUrl));
      final authToken = token ?? _huggingFaceToken;
      
      if (authToken.isNotEmpty && authToken != 'YOUR_HUGGING_FACE_TOKEN_HERE') {
        request.headers['Authorization'] = 'Bearer $authToken';
      }

      if (downloadedBytes > 0) {
        request.headers['Range'] = 'bytes=$downloadedBytes-';
      }

      if (kDebugMode) {
        print('🚀 Starting download of ${model.modelName}...');
        print('📁 File path: $filePath');
        print('📊 Resume from: ${(downloadedBytes / 1024 / 1024).toStringAsFixed(1)} MB');
      }

      response = await request.send();
      
      if (response.statusCode == 200 || response.statusCode == 206) {
        final contentLength = response.contentLength ?? 0;
        final totalBytes = downloadedBytes + contentLength;
        fileSink = file.openWrite(mode: FileMode.append);

        int received = downloadedBytes;

        await for (final chunk in response.stream) {
          fileSink.add(chunk);
          received += chunk.length;
          
          final progress = totalBytes > 0 ? received / totalBytes : 0.0;
          final downloadedMB = (received / 1024 / 1024).round();
          final totalMB = (totalBytes / 1024 / 1024).round();
          
          onProgress(progress, downloadedMB, totalMB);
        }
        
        await prefs.setBool(_preferenceKey, true);
        
        if (kDebugMode) {
          print('✅ ${model.modelName} downloaded successfully!');
          print('📊 Total size: ${(received / 1024 / 1024).toStringAsFixed(1)} MB');
        }
      } else {
        await prefs.setBool(_preferenceKey, false);
        
        if (kDebugMode) {
          print('❌ Failed to download model. Status code: ${response.statusCode}');
          print('📋 Headers: ${response.headers}');
          
          try {
            final errorBody = await response.stream.bytesToString();
            print('💬 Error body: $errorBody');
          } catch (e) {
            print('❌ Could not read error body: $e');
          }
        }
        
        throw Exception('Failed to download ${model.modelName}. Status: ${response.statusCode}');
      }
    } catch (e) {
      await prefs.setBool(_preferenceKey, false);
      
      if (kDebugMode) {
        print('❌ Error downloading ${model.modelName}: $e');
      }
      
      rethrow;
    } finally {
      if (fileSink != null) await fileSink.close();
    }
  }

  /// Delete the downloaded model
  Future<void> deleteModel() async {
    try {
      final filePath = await getModelFilePath();
      final file = File(filePath);
      
      if (file.existsSync()) {
        await file.delete();
        
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_preferenceKey, false);
        
        if (kDebugMode) {
          print('🗑️ ${model.modelName} deleted successfully');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting ${model.modelName}: $e');
      }
      rethrow;
    }
  }

  /// Get model download status and size information
  Future<Map<String, dynamic>> getModelInfo() async {
    final isDownloaded = await isModelDownloaded();
    final filePath = await getModelFilePath();
    final file = File(filePath);
    
    int localSizeMB = 0;
    if (file.existsSync()) {
      localSizeMB = ((await file.length()) / 1024 / 1024).round();
    }

    return {
      'isDownloaded': isDownloaded,
      'localSizeMB': localSizeMB,
      'expectedSizeMB': model.sizeInMB,
      'modelName': model.modelName,
      'description': model.description,
      'filePath': filePath,
    };
  }
}
