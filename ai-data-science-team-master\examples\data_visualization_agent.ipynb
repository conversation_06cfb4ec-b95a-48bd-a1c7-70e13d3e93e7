{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to Automate Data Visualization with AI <a id=\"make-a-data-visualization-agent\"></a>\n", "\n", "In this tutorial, you will learn how to automate data visualization with AI. We will create a Data Visualization Agent that uses the `plotly` library to create interactive visualizations from Natural Language.\n", "\n", "### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "bat"}}, "source": ["# Table of Contents\n", "\n", "1. [How to Automate Data Visualization with AI](#make-a-data-visualization-agent)\n", "2. [Load Libraries](#load-libraries)\n", "3. [Setup AI and Logging](#setup-ai-and-logging)\n", "4. [Load a Dataset](#load-a-dataset)\n", "5. [Create The Agent](#create-the-agent)\n", "6. [Response](#response)\n", "7. [The data visualization](#the-data-visualization)\n", "8. [Data Visualization Function](#data-visualization-function)\n", "9.  [Free Generative AI Data Science Workshop](#free-generative-ai-data-science-workshop)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Libraries <a id=\"load-libraries\"></a>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# * Libraries\n", "\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "import yaml\n", "import pandas as pd\n", "from pprint import pprint\n", "\n", "from ai_data_science_team.agents import DataVisualizationAgent\n", "from ai_data_science_team.utils.plotly import plotly_from_dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup AI and Logging <a id=\"setup-ai-and-logging\"></a>\n", "\n", "This section of code sets up the LLM inputs and the logging information. Logging is used to store AI-generated code and files during the AI Data Science Teams processing of files. \n", "\n", "*Important Note:* This example uses OpenAI's API. But any LLM can be used such as Anthropic or local LLMs with Ollama."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x7fc228631a20>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x7fc1f8b20040>, root_client=<openai.OpenAI object at 0x7fc2180fffd0>, root_async_client=<openai.AsyncOpenAI object at 0x7fc2286319f0>, model_name='gpt-4o-mini', model_kwargs={}, openai_api_key=SecretStr('**********'))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# * Setup\n", "\n", "MODEL    = \"gpt-4o-mini\"\n", "LOG      = True\n", "LOG_PATH = os.path.join(os.getcwd(), \"logs/\")\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = yaml.safe_load(open('../credentials.yml'))['openai']\n", "\n", "llm = ChatOpenAI(model = MODEL)\n", "\n", "llm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load a Dataset <a id=\"load-a-dataset\"></a>\n", "\n", "Next, let's load a customer churn data set that we will clean up. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customerID</th>\n", "      <th>gender</th>\n", "      <th>SeniorCitizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>tenure</th>\n", "      <th>PhoneService</th>\n", "      <th>MultipleLines</th>\n", "      <th>InternetService</th>\n", "      <th>OnlineSecurity</th>\n", "      <th>...</th>\n", "      <th>DeviceProtection</th>\n", "      <th>TechSupport</th>\n", "      <th>StreamingTV</th>\n", "      <th>StreamingMovies</th>\n", "      <th>Contract</th>\n", "      <th>PaperlessBilling</th>\n", "      <th>PaymentMethod</th>\n", "      <th>MonthlyCharges</th>\n", "      <th>TotalCharges</th>\n", "      <th><PERSON>rn</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7590-VHVEG</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.85</td>\n", "      <td>29.85</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5575-GNVDE</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>34</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>56.95</td>\n", "      <td>1889.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3668-QPYBK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7795-CFOCW</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>45</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>42.30</td>\n", "      <td>1840.75</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9237-HQITU</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7038</th>\n", "      <td>6840-RESVB</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>24</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>84.80</td>\n", "      <td>1990.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7039</th>\n", "      <td>2234-XADUH</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>72</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>103.20</td>\n", "      <td>7362.9</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7040</th>\n", "      <td>4801-JZAZL</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>11</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.60</td>\n", "      <td>346.45</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7041</th>\n", "      <td>8361-LTMKD</td>\n", "      <td>Male</td>\n", "      <td>1</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>4</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>74.40</td>\n", "      <td>306.6</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7042</th>\n", "      <td>3186-AJIEK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>66</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Two year</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>105.65</td>\n", "      <td>6844.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7043 rows × 21 columns</p>\n", "</div>"], "text/plain": ["      customerID  gender  SeniorCitizen Partner Dependents  tenure  \\\n", "0     7590-VHVEG  Female              0     Yes         No       1   \n", "1     5575-GNVDE    Male              0      No         No      34   \n", "2     3668-QPYBK    Male              0      No         No       2   \n", "3     7795-CFOCW    Male              0      No         No      45   \n", "4     9237-HQITU  Female              0      No         No       2   \n", "...          ...     ...            ...     ...        ...     ...   \n", "7038  6840-RESVB    Male              0     Yes        Yes      24   \n", "7039  2234-XADUH  Female              0     Yes        Yes      72   \n", "7040  4801-JZAZL  Female              0     Yes        Yes      11   \n", "7041  8361-LTMKD    Male              1     Yes         No       4   \n", "7042  3186-AJIEK    Male              0      No         No      66   \n", "\n", "     PhoneService     MultipleLines InternetService OnlineSecurity  ...  \\\n", "0              No  No phone service             DSL             No  ...   \n", "1             Yes                No             DSL            Yes  ...   \n", "2             Yes                No             DSL            Yes  ...   \n", "3              No  No phone service             DSL            Yes  ...   \n", "4             Yes                No     Fiber optic             No  ...   \n", "...           ...               ...             ...            ...  ...   \n", "7038          Yes               Yes             DSL            Yes  ...   \n", "7039          Yes               Yes     Fiber optic             No  ...   \n", "7040           No  No phone service             DSL            Yes  ...   \n", "7041          Yes               Yes     Fiber optic             No  ...   \n", "7042          Yes                No     Fiber optic            Yes  ...   \n", "\n", "     DeviceProtection TechSupport StreamingTV StreamingMovies        Contract  \\\n", "0                  No          No          No              No  Month-to-month   \n", "1                 Yes          No          No              No        One year   \n", "2                  No          No          No              No  Month-to-month   \n", "3                 Yes         Yes          No              No        One year   \n", "4                  No          No          No              No  Month-to-month   \n", "...               ...         ...         ...             ...             ...   \n", "7038              Yes         Yes         Yes             Yes        One year   \n", "7039              Yes          No         Yes             Yes        One year   \n", "7040               No          No          No              No  Month-to-month   \n", "7041               No          No          No              No  Month-to-month   \n", "7042              Yes         Yes         Yes             Yes        Two year   \n", "\n", "     PaperlessBilling              PaymentMethod MonthlyCharges  TotalCharges  \\\n", "0                 Yes           Electronic check          29.85         29.85   \n", "1                  No               Mailed check          56.95        1889.5   \n", "2                 Yes               Mailed check          53.85        108.15   \n", "3                  No  Bank transfer (automatic)          42.30       1840.75   \n", "4                 Yes           Electronic check          70.70        151.65   \n", "...               ...                        ...            ...           ...   \n", "7038              Yes               Mailed check          84.80        1990.5   \n", "7039              Yes    Credit card (automatic)         103.20        7362.9   \n", "7040              Yes           Electronic check          29.60        346.45   \n", "7041              Yes               Mailed check          74.40         306.6   \n", "7042              Yes  Bank transfer (automatic)         105.65        6844.5   \n", "\n", "     Churn  \n", "0       No  \n", "1       No  \n", "2      Yes  \n", "3       No  \n", "4      Yes  \n", "...    ...  \n", "7038    No  \n", "7039    No  \n", "7040    No  \n", "7041   Yes  \n", "7042    No  \n", "\n", "[7043 rows x 21 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"https://raw.githubusercontent.com/business-science/ai-data-science-team/refs/heads/master/data/churn_data.csv\")\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create The Agent <a id=\"create-the-agent\"></a>\n", "\n", "Run this code to create an agent with `DataVisualizationAgent()`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAi4AAAIrCAIAAABH5YU7AAAAAXNSR0IArs4c6QAAIABJREFUeJzs3XdYE1nbBvCTBin03qsoggVYVETXrth7b2uHtbd119W19+7aO65d7A2RYgEVO5ZVsWFBpIYOCWnfH/O+efkQkOo44f5de+1lJpOZJwnJnTNz5hyWSqUiAAAA9GHTXQAAANR0iCIAAKAZoggAAGiGKAIAAJohigAAgGaIIgAAoBmX7gJAkykVqqSPkrxsRV6WQqFQFUiUdFdUJlp8Nl/IFupyRfocY0ttussB0HwsXFcEVU4uV768m/3uae6nV3lWTgJtAVuoxzEw1SrIZ0YUqVSqLLE8L1uuLeQkf5Q41dNxqi+ychbQXReAxkIUQRW7d0X88l62jYvAqb7Ivq6I7nIqKzNV9u5ZjvhLQVaa3Lebsbk9n+6KADQQogiqzLtnOaEHkhq2NPDpbEx3LVXv85v8m+dTzW35Lfua0l0LgKZBFEHVuHdFnJ5U0Kq/mZa2JveF+fAiN+JY8qBZdnwhh+5aADQHogiqwIPwdJlUqZGNoa/lZMiPrPo4Yr4DT6NDF+B7QhRBZYUfTRKIOL7dTOgu5LvaPffdoFl2Ij30QQWoAvhZB5Xy+EYGT4td03KIEDJktv2RlR/prgJAQyCKoOI+v81P+yJt0bsmnsYXiDhdx1qGHUmiuxAATYAogoqLPJVSv7kB3VXQxsJBIMlVxD3LpbsQAMZDFEEFvXqYbWiuZWpdowcj8O1mcut8Kt1VADAeoggq6PWjbN/uNaLLXCmMzLUc64lex2TTXQgAsyGKoCJS4qXZ6XJdA9732d2XL18SEhLoenjpzO35rx/mVNPGAWoIRBFURNy/uY7u32lQn/j4+O7duz9//pyWh3+TUz3RO5wuAqgcRBFUREq81LnBd4oiuVxesavfqEdV+OFlxGKz3JroxT1Hwwig4nCJK1TEjj/ejlzgqMWv4p8yEolkxYoVN27cIIR4enrOnDlTpVJ1795dvULXrl0XLFhQUFCwa9eukJCQpKQkExOTLl26+Pv7czgcQkj//v2dnZ2dnZ2PHj0qkUj27ds3aNCgIg+v2poJIVFnUkUGHM9WhlW+ZYAaAteKQ7nJpEqVilR5DhFC9u3bd+HChYCAABMTkwsXLggEAqFQuGTJkrlz5wYEBHh7exsZGRFCOBzOnTt3WrRoYWNjExsbu3fvXj09vaFDh1IbuX37tkQiWb9+fV5enr29/dcPr3IifU5upqI6tgxQQyCKoNxys+TVNOBNQkKCQCAYMWIEl8vt2bMntdDV1ZUQ4uDg4OHhQS3hcDj79+9nsVjUzfj4+IiICHUUcbncZcuWCQSCkh5e5UR63OR4aTVtHKAmwLkiKDelQiUQVctfTqdOnSQSyaRJk968eVP6mmKxeMWKFT179mzTps3bt2/T0tLUd9WrV0+dQ98Hh8dis1nfc48AGgZRBOUm0ueKk2TVsWVfX9+NGzempaUNHDhwyZIlcrm82NXS0tKGDBly9+7dX3/9ddOmTXXr1lUo/nd87DvnECEkJ12uLcRHCaDicIAOyk1bwFHIVXKZksur+u9fX19fHx+fI0eOrF+/3tLScvTo0V+vc/LkSbFYHBgYaGFhQQixsLD48OFDlVdSdtV3xBKghsBPOagIBzdhblbVn6gvKCgghLDZ7CFDhpiamr58+ZIQwufzCSEpKSnq1TIyMgwNDakcom6W0hH064dXORUh+ibf6WpfAI2En3JQEbpGvHdPcjxbV3H35aNHj16/fr1z584pKSkpKSlubm6EEHNzc2tr64MHDwoEgszMzIEDB3p7ex8/fnzbtm0NGzaMiIi4efOmUqnMyMgwMChmbNavH66tXcXj5j2Nymy8qFr65gHUEGgVQUU4uovi/q36IQZsbGwKCgrWr19/5syZgQMHDhs2jBDCYrGWLVsmEonWrFlz/vx5sVjcpk2bMWPGBAUFzZkzRyaTBQYGOjg4HDt2rNhtfv3wqq3506s8C3s+ZnQFqAxc4goVdHrr566jLXjaHLoLodndK2KRHsfdR5/uQgAYDAfooIIc3UXRl8Q/9ypx3rzu3btnZWV9vbxBgwZPnjz5erm+vv7Zs2erusyioqKi5s6d+/VylUqlUqnY7GIaNyEhISUd05PkKh5fzxi71KkaKgWoQdAqgorbOy9uwEzbkjqPJSYmKpXKsm+NzWareyJUH4lEUuwxOqVSqVQqudxinoulpaX6ctoirh5LNrXTrtcUTSKASkEUQcW9fpidkiD17WpCdyH0yEqXRZ5M6TLGiu5CABgP51qh4ly8dGVS1ZPIDLoLocex1Z/aDjanuwoATYAogkpp2cf0zeOcNzE1boqE4+s/dRltyRfW9F4bAFUCB+igClze/8Wpvqi2lx7dhXwnQes/tR9qbmCqRXchABoCrSKoAh1/sXz3NO/elSq+ZOcHlCWW7Zz9rll3E+QQQBVCqwiqzMOI9KdRmb7djF08demuperl5yhunU+V5CnbDTbTFuC4HEBVQhRBVcoSy26dTyvIVzi4ixzriXQNNWFkto8v8xI/5D+JzPTtZuLWpKYchAT4nhBFUPWS4yUv7mTHPcvlC9kWTnyhDleox9E14CoYMtOpUqbMzpDnZikIUT2NyrJy5rt46iKEAKoPogiqUcpnadJHSW6GPC9LweGysjOKn3+owmJjY62srHR1q/h4oEDE1RKwRHocPROevauwOubCAIDCEEXAYP7+/mPHjvX29qa7EACoFPzcAwAAmiGKAACAZogiYDArKysOB/2qARgPUQQMlpCQoGBKtzwAKBmiCBhMKBSWNH0DADAIoggYLC8vD11AATQAoggYzMDAoNh5VwGAWfAxBgbLyMgo10SxAPBjQhQBg9nY2KAHHYAGQBQBg8XHx6MHHYAGQBQBAADNEEXAYLq6uujMDaABEEXAYNnZ2ejMDaABEEXAYGgVAWgGRBEwGFpFAJoBUQQAADRDFAGDmZmZYbQFAA2AjzEwWHJyMkZbANAAiCIAAKAZoggYzNraGgP/AGgARBEw2OfPnzHwD4AGQBQBAADNEEXAYBiZG0AzIIqAwTAyN4BmQBQBAADNEEXAYFZWVjhAB6ABEEXAYAkJCThAB6ABEEUAAEAzRBEwmFAoxCQRABoAUQQMlpeXh0kiADQAoggYzNzcHCNzA2gAfIyBwZKSkjAyN4AGQBQBAADNEEXAYPr6+ui2AKABEEXAYJmZmei2AKABEEXAYBgOFUAzIIqAwTAcKoBmQBQBg6FVBKAZEEXAYGgVAWgGRBEwmLGxMS5xBdAALHRAAsbx8/Pj8XhsNjs9PV0oFFL/1tLSOnHiBN2lAUBFcOkuAKDcBAJBfHw89e/8/HxCCIvFGjduHN11AUAF4eAGME/Xrl2LXNlqY2MzYMAA+ioCgEpBFAHzDBgwwMrKSn2TxWJ17NhRT0+P1qIAoOIQRcA8urq6nTp1Ut+0sbEZPHgwrRUBQKUgioCRBg4caGdnR/27Y8eOurq6dFcEABWHKAJGMjAw6NChA4vFsrOzw1kiAKZDD7qaJTdLLv5SIJNpQg/+5l59oiPifHx8xPE8cXwu3eVUFosQHUOukbkWh4uxxqHGwXVFNUVOhvzaieSkD1L7uqK8bIxQ8MPRErDFX6SEENdGul5tDOkuB+C7QhTVCLmZ8tNbPrfoZ2Fopk13LfAN0ReT9Y25jf2M6C4E4PvBuaIaYf/i91397ZBDjODTxSwzTf4oIp3uQgC+H0SR5rsbIm7cyQRnIBjEp4vZq0c5MimOo0JNgSjSfF/iJDoGPLqrgPJRqYg4SUZ3FQDfCaJI8ynlKh0jLbqrgPIxseJnieV0VwHwnSCKNF9etpzgSA/TSCUKoqS7CIDvBVEEAAA0QxQBAADNEEUAAEAzRBEAANAMUQQAADRDFAEAAM0QRQAAQDNEEQAA0AxRBAAANEMUAQAAzRBFAABAM0QRlNWJk4dbt/XOy8ur/Kaev3gmlUrLsual4LM9e7dLSkqswF5ycnJevX5ZgQeWTqFQPH0aU+WbBajJEEXwvV0OOT9h4giJJL8sK2tpaYtEOmx2Rf5Qx4wbGBx8tgIPLN3qtYvXbVhW5ZsFqMm4dBcANYhKpWKxWGVsD1Hate3Yrm3Hiu2uoKDgm8VUZLPlqb9K9gig8RBFUAyJRHLg4O6rV6+kpCabm1t2aN9lyOCR1F2RkRGHjwampCTVr+cxc8ZfpqZmhJCnT2MOHNz99FkMIcS1jntAwNQ6tesSQjIzM3r2bhfgP+X1m9ibN6+5uLh27tRjw8YVhJCevdsRQn6fNb+jX7eSylixakFIyAVCSGhINJfLnTtvhq2NPZfLvXDxtFwm8/FpPmXyHzo6OoSQw0cCz5w9np2dVatWnRG/+P/k1Xjg4K7p6eIzZ4POnA0yN7c4evjC18WsW7O9vZ/P2DETBw8aQe1x9pypmZkZWzcHlvQirF67+Oq1UEJI67behJDDh85ZWljJ5fJ9gdtDrlzIzMywt3cc8Yt/82atCCHXroctXPTH4oVrjgUdePny3z9nL27dqv33eg8BmARRBEUpFIo/50x9+iymd6+BtZxrv//w7lP8Bw6HQ937z4Fd/fsPk0ol/xzYtXzFvHVrtxNCEhMTpAXSYUPHsNnss2eD/pg9+cih83w+n3rIwYN7evTot3bNdg6HY2Zq3r/f0ONBB5cv3SAS6djY2JVSSe9eA5VKZWjoJfWS40EH27TusGzpho8f4tasW2JsbBrgP+XBw7u7dm9u27Zjk0a+d+/dys/LI4QsmL9q1u8TPRr+1K/vEJ7W/2YOLFxMBV6EoYNHpSQnffnyefYfiwghxkYmhJA1a5eEhQcPHTLKwcE5LDz4r3kzN67f1aCBJ7WpjZtWjhk1YdTIX52cXCr1xgBoLkQRFHX9RvijmPu/zfyrc6ceX9+7ds12CwtLQohcLt+1e3NmZoa+vkG7dp3at+9MrVCnjtv0GQFPn8U08vahlri51R8zeoJ6C1ZWNoSQunXr6esblF5JbRdXB3unwktsbOz+nL2YxWLVdXW/ERVx7/7tAP8piYkJhJBePfq7uzdQl+Fax43L5Robm9Sv71F4C4WLkctLnCa1pBfBxsZOX99AnJ6m3uzHj+9DrlwYPmzMiF/8CSEtW7QdOrxX4P4dVEgTQnr1HODn17X0ZwpQw6HbAhR1994tbW1tvw7Ff3vq6elT/3ByrEUISU5JIoSwWKzIqKuTpozu3rPNylULCCHp4jT1Q7y8GldVbXxtvvp0i7m5ZWpqCiHEp0lzXV29Zcv/io6O+uYWylhM6S9CYY+fPCSENG/emrrJYrEaefvEvnpe3j0C1GSIIigqXZxmYmxa+vErQgiLzaYOZBFC/jmwe9783+rUdlu6eF2A/1RCiFL1v9mw+XxBddTJ4/KUSgUhxNjYZPPfe21s7WfPmTppyuiUlORSHlXGYsr4IhBCcnNzCCGGBkbqJXp6+nl5ebm5udRNoUBYlj0C1GSIIihKR0dXnJ5WhhX/QyqVHj6yr0vnnhMnzKhf38Otbv2yPEqlUlWixqLs7BxWLv977ZptcXFvqGZZWfZSSn+20l+Ewps1MTEjhGRlZaqXiMVpXC5XfaoMAL4JUQRFeXo2ys/PD48IUS8p5ZwKIUQiyZdKpbVr16VuZmZlEEKUSmVJ6wv4AkIIdWytqlD9tr08G/n4/Ky+rFXAF6SlpZbyKA6Ho6url5r2n0pUKlVy8n+upS3lReDzBWJxmvoJ1q1bj8ViRd+JUlcSfSfK3b1BWVpUAEBBtwUoqn27zmfOHl+xcv7Ll//Wcq79Lu7Ng4d3dm4/VNL6+voGTk61Tp0+amRknJuTs/+fnWw2+927NyWt716vIYfD2bx1TSe/7tICafdufSpZ8IuX/y5c9HvPHv0FAuHdu7dc67hRy+vX9wyPuHz4SKCurp67WwNjY5OvH9u4UdPQKxe9PBsZGRofDzr48eN7FxfXUl4ENpvdsIFX8OVz69Yvq1/PQ1dXz9e3hV+HroH7dygUCisrm4sXT4vFaX/OXlzJJwVQoyCKoChtbe21a7bv2rUpNOzShYunLCysWrfqUHrD6K85y1auWrBo8WwbG7tff5329u2rkyeP+I+bXOzK1lY2M6bP2b1ny+Yta1xcXCsfRVo8LXs7x8OH96lUqoYeP02eOIta7j9usliceuDgbgN9w/HjpxcbRRPGz5BKpStWzheJdLp36yuRSqhDbSW9CFpaWu3bd4599fxK6MXb0ZEd/br5+raYOuUPkUjn9Jlj2dlZjg7Oy5as9/JsVMknBVCjsKr2kD38gA4t/9Cyn5W+KY/uQqAcbpxMrO2h4+KlQ3chAN8DWkVAs8lTx8TFFXM0z9e35ezfF9JREQB8b4gioNm8uctlctnXywXV0wUcAH5AiCKgmYmJKd0lAADN0JkbAABohigCAACaIYoAAIBmiCIAAKAZoggAAGiGKAIAAJohigAAgGaIIgAAoBmiCAAAaIYoAgAAmiGKNJ+hhbaKYPx1hhGIOFytEieZBdAwiCLNx+Wx0hIkdFcB5fPhZa6xlRbdVQB8J4gizefkLhQnSumuAsohK63AxFJLzwhTTEFNgSjSfLU8dVUK1aOraXQXAmV19Vhiiz4YsBxqEMziWlNEHEtmsVhGVnwzaz6Lg5MQPxwWi2SJZdniglvnUn6ZZ69riCYR1CCIohrk9aPst09yZQWqtAQNOV5XUFDA5XLZbE1o3Av1OFwu28qZ79PZmO5aAL43RBEwmL+//9ixY729vekuBAAqRRN+TgIAAKMhigAAgGaIImAwGxsbDodDdxUAUFmIImCw+Ph4hUJBdxUAUFmIImAwc3Nzzeg+B1DD4WMMDJaUlKRUKumuAgAqC1EEDGZlZYVzRQAaAFEEDJaQkIBzRQAaAFEEDGZmZoZzRQAaAB9jYLDk5GScKwLQAIgiAACgGaIIGMzS0hIH6AA0AD7GwGBfvnzBAToADYAoAgAAmiGKgMF4PMwvB6AJEEXAYDKZjO4SAKAKIIqAwQQCAYuFydEBGA9RBAyWn5+PaYgBNACiCAAAaIYoAgYzMjLCdUUAGgAfY2AwsViM64oANACiCAAAaIYoAgbDLK4AmgEfY2AwzOIKoBkQRQAAQDNEETCYjY0NJhQH0ACIImCw+Ph4TCgOoAEQRQAAQDNEETAYetABaAZ8jIHB0IMOQDMgioDBRCIRRuYG0ACIImCw3NxcjMwNoAEQRQAAQDNEETCYlZUVrisC0ACIImCwhIQEXFcEoAEQRcBg1tbWaBUBaABEETDY58+f0SoC0ACIImAwnCsC0AyIImAwnCsC0AyIImAwnCsC0AwsXCEIjNO3b18ej8fj8T5+/GhgYCAQCHg8HpvNDgwMpLs0AKgILt0FAJQbi8V6/fo19e+cnBxCiEql6t69O911AUAF4QAdMI+vr2+RoefMzMxGjhxJX0UAUCmIImCevn37Ojg4qG+qVCpfX187OztaiwKAikMUAfPY2to2bdpUfZrTwsICTSIARkMUASP16dPH1taWahK1bt3axsaG7ooAoOIQRcBI9vb2VMPIxsamf//+dJcDAJWCHnQ1Tn62Qi7XhB78vboNjo563Ny3uaGuVXa6nO5yKkulVOkZ8+iuAoAeuK6oBrl1IfXlvWx9E61ssYzuWqAoPWPel3f5Du4i73aG5vZ8ussB+K4QRTWCUqk6tfmzYz1daxeRSA9N4R+UUqnKSpVFnk5s2cvU2kVAdzkA3w+iqEY4sTG+ro+BnasO3YVAmVzc9ennnibWtZBGUFOg24Lme34308JJiBxikLaDLR+Ep9NdBcD3gyjSfF/eSQUijBnKJHwRNyVempvF+L4YAGWEKNJ8CrnK0Fyb7iqgfOxcRemJBXRXAfCdIIo0X3aaTIU5fZgmO12mIqwyrAigCRBFAABAM0QRAADQDFEEAAA0QxQBAADNEEUAAEAzRBEAANAMUQQAADRDFAEAAM0QRQAAQDNEEQAA0AxRBAAANEMUQVm9fhPbuq337duRld/U8xfPpFJpVRRFg5ycnFevX9JdBYBGQRTB93Y55PyEiSMkkny6C6mgMeMGBgefpbsKAI2CKILvh5oyuMLtIZVK9TkhvqqLKmYvpa9QUFDBuRswYzJASbh0FwA/IolEcuDg7qtXr6SkJpubW3Zo32XI4JHUXXHv3x49/k9s7HMbG7spk36vX9+DEJKcnLRn39Y7d27m5ubY2toPHjSyXduOhJDMzIyevdsF+E95/Sb25s1rLi6unTv12LBxBSGkZ+92hJDfZ83v6NetlEqev3i2Zevad+9eGxuZODg6v3kT+0/gKS0tLYlEsnvPlvCIywUFUlsb+/79h7Vp3YEQcuLk4YirV/r1HbJnz5Y0caqLi+vM6XPt7ByorT2Kub9r9+a3b18ZGhp5ejQaM3qCsbEJIWTk6P6ODs4ODs6nTh+VSiVBxy7Hxb05cHD302cxhBDXOu4BAVPr1K5LCBk4uGt6uvjM2aAzZ4PMzS2OHr5ACJHL5fsCt4dcuZCZmWFv7zjiF//mzVoRQq5dD1u46I/FC9ccCzrw8uW/06f92alj9+/yBgIwDKIIilIoFH/Omfr0WUzvXgNrOdd+/+Hdp/gPHM5/5oE9eGhP/37DOnXsfvhI4Jy/ph8+eE5HR0eukL98+W+P7n319QxuREUsXTbX2tq2rqv7fx5ycE+PHv3WrtnO4XDMTM379xt6POjg8qUbRCIdGxu7UipJSkqc+duvLi6uc2YvuXP35oWLp8eOmailpaVUKufMnZaYmDBk8EgDA6OYmPuLl/wpkeR37tSDEPLixbPjxw/MmDFXLpevW7d0+cr527bsJ4Q8eHj3j9mT27fr3KvngOyszJOnjkyfGbBj20E+n08IuXfvtkQqWbZkfV5+no6OTmJigrRAOmzoGDabffZs0B+zJx85dJ7P5y+Yv2rW7xM9Gv7Ur+8QnpYWVeeatUvCwoOHDhnl4OAcFh7817yZG9fvatDAk7p346aVY0ZNGDXyVyfHWtX81gEwFaIIirp+I/xRzP3fZv5FfbMXMWXS735+XQkh9naO4yeOePDwTssWba0srQP3BrFYLEJIp049evVpd/PmNXUUubnVHzN6gnoLVlY2hJC6devp6xuUXklo2KX8/Pz5f60wMjJu1qzl4ycPo+9EDR404kZkxJOnj44cOm9iYkoIade2Y35+3slTR9QFL12y3sjImBDSu/fArdvWZ2Zl6uvpb9q8ulvX3pMnzaLW8fb2+WVk33v3b//cvDUhhMPl/jVnmUAgoO5t165T+/adqX/XqeM2fUbA02cxjbx9XOu4cblcY2MTqjlICPn48X3IlQvDh40Z8Ys/IaRli7ZDh/cK3L9j3drt1Aq9eg6gXjEAKAmiCIq6e++Wtra2X4fivz319PSpfzg4OBNCUlKSqJtv3r4K3L8jNvY51a4Si9PUD/HyalyxSlJSkkQiERUqLBbLysomKekLISQ6Okoulw8e+r+DXQqFQiTSUd/k8/+TKObmloSQtNSU/Ly8Dx/iPn/+dOHi6cK7SE7+T/1169ZT5xC1u8ioq8eDDn74ECcUCgkh6YWeUWGPnzwkhDRv3lr9wEbePqFhlyr/9AFqDkQRFJUuTjMxNlUfkSsJm82mMoAQ8vDRvd//mOTp4T3rt/kioWjegt+UKqV6TXUwlJe1tW1ubu67d2+cnGrJZLI3b2I9PLwJIenpacbGJuvWbC+8ModbzB8zj8sjhCiUivT0NELIL8PHtfi5TeEVjIxMqH8I/n+R/xzYvS9we5/eg8aNmZQmTl246I/Cz6iw3NwcQoihgZF6iZ6efl5eXm5uLnVTKBBW7OkD1ByIIihKR0dXnF58C6AkBw7strKyWbZ0A5fL/fprvVhl6U7m16Fr0IlDf86d2qF9l5jHD+Ry+Yjh4wghurp6GRnp5uaW2traZaxQR0eXECKVStRdGEohlUoPH9nXpXPPiRNmFG45FVu8iYkZISQrK5M6WkgIEYvTuFwudQoKAMoCnbmhKE/PRvn5+eERIeolcrm89IdkZmXUcq5N5VBBQUFefp5SWXwbQh1Uqakp36xEX99g4oSZ2tr8uLi33j/57NpxmOrm4OXVWKFQnDt/Qr1mfv43rlKysbEzN7cIvnxOvaZcLpfJZMWuLJHkS6XS2rXrqp8dIUT9jAR8QVpaqnrlunXrsVis6DtR1M2CgoLoO1Hu7g2+2awEADW0iqCo9u06nzl7fMXK+S9f/lvLufa7uDcPHt7Zuf1QKQ/x8PAOCTl/Kfisnq5+0MlD2dlZ7+PeltTuca/XkMPhbN66ppNfd2mBtHu3PiVt9sXLf1etXjh54iwuj8dms798+WxkZMzhcNq363z+wqntOzZ+SUyo7eL65s2rqJtXA/eeKKUhwmKxJoyfMW/+bxMmjejera9SoQi5cqF9+859+wz+emV9fQMnp1qnTh81MjLOzcnZ/89ONpv97t0b6t769T3DIy4fPhKoq6vn7tbAyamWX4eugft3KBQKKyubixdPi8Vpf85eXOprDAD/D6IIitLW1l67ZvuuXZtCwy5duHjKwsKqdasOpTeMRo34VZyWumnzal1dva5devfvO3TdhmWPYu47O7l8vbK1lc2M6XN279myecsaFxfXUqLIwtzS0tJ65eqF6lRzqVXn7417+Hz+6pVbdu3eFBERcuHCKRsbu+7d+nKLO1dU2M/NWy9fumFf4PYtW9eKRDoN6ns2aOBV0sp/zVm2ctWCRYtn29jY/frrtLdvX508ecR/3GQej+c/brJYnHrg4G4DfcPx46c7OdWaOuUPkUjn9Jlj2dlZjg7Oy5as9/JsVHoxAFAYC1eAa7yTG+M9WpuY2TPy1IVCoaCOdCkUisioqwsX/bF2zbaa8EUfeuBzow5GtrUr2OMDgFnQKgKaTZ46Ji7uzdfLfX1bDhk0csq0sU19fq7lXFtaIL2zz0QvAAAgAElEQVRxI5zP59tYl3ZVLAAwEaIIaDZv7nKZvJjuAwK+QKFQtG3TMTo6MjTsko6Obv16HlOnzjYzM6ejTACoRogioJm6D3SxJk6YQfWoBgANhs7cAABAM0QRAADQDFEEAAA0QxQBAADNEEUAAEAzRBEAANAMUQQAADRDFAEAAM0QRQAAQDNEEQAA0AxRpPn0TXgszOLGNLqGPBY+nVBj4I9d83G0WOIvUrqrgPJ5/zzH2EKL7ioAvhNEkeazduLnZX9jRnD4oeRmyKwcBQIdNGahpkAUab7aP+mlJ0lfPcikuxAoq9CDCY06GtJdBcD3g1lca4oLuxNMrAVWzkJDc226a4HiSfIUmakFUaeTuo6xNLHC2wQ1CKKoBnkYkf7yXjaHy8pMLWaqOiZSKJVsNotFWHQXUgUMzXmZKTIHd1EjPyN9Yx7d5QB8V4iiGkchV8llGvKmT5s2bfjw4Z6ennQXUgVUSsIX4YA51FCYxbXG4XBZHK4mNCMIIQqVhKul0hbgGxyA2fAZBgAAmiGKgMHMzMzYbPwNAzAePsbAYMnJyUqlku4qAKCyEEXAYDY2NhwOrgMFYDxEETBYfHy8QqGguwoAqCxEETAYWkUAmgFRBAyGVhGAZkAUAYMJBAIWS0OukQKoyRBFwGD5+fkYLgRAAyCKAACAZogiYDBbW1t0WwDQAIgiYLBPnz6h2wKABkAUAQAAzRBFwGCmpqYYgw5AA+BjDAyWkpKCMegANACiCAAAaIYoAgYTCoW4xBVAAyCKgMHy8vJwiSuABkAUAYOhSQSgGRBFwGBoEgFoBkQRAADQDFEEDCYSiXCMDkADIIqAwXJzc3GMDkADIIoAAIBmiCJgMDMzMwz8A6AB8DEGBktOTsbAPwAaAFEEAAA0QxQBg9nY2GDqPAANgCgCBouPj8fUeQAaAFEEAAA0QxQBg/H5fFziCqABEEXAYBKJBJe4AmgARBEwGLotAGgGRBEwGLotAGgGRBEwmJGREUZbANAA+BgDg4nFYoy2AKABEEXAYGgVAWgGfIyBwdAqAtAMiCJgMPSgA9AMiCJgMPSgA9AMLFwhCIzTpUuXpKQkpVLJZrNVKhWLxVIqla1bt167di3dpQFARaBVBMzToEEDQgjVYYEa+MfKymrUqFF01wUAFYQoAuYZOnSohYWF+qZKpfLw8HB3d6e1KACoOEQRMI+7u3vDhg3VNy0sLAYPHkxrRQBQKYgiYKQBAwZQDSOVSuXp6enm5kZ3RQBQcYgiYKQGDRpQDSMLC4uBAwfSXQ4AVAqiCJhq4MCBRkZGDRo0qFevHt21AECloDN3RTy7mfn2Sa5SqUqJl9JdS40mV8jZbA4bs+fRR8eIy1IR61qCJp2MtAW43BgqCFFUbqGHkrRFXDNbvrEln83BlyDUaGw2yRLLssWyW+eSB8yw1Tfh0V0RMBKiqHwu7f1iYM6v39yQ7kIAfjhnNn/oNNLCxEqb7kKAeXCuqBxePcwW6vOQQwDFajfM6tb5NLqrAEZCFJXDx5d5+sZadFcB8IPS0eelfSnITpfRXQgwD6KoHGQFKhNrPt1VAPy47FyFaV8K6K4CmAdRVA7piQU4swZQirwshRJDpUP5IYoAAIBmiCIAAKAZoggAAGiGKAIAAJohigAAgGaIIgAAoBmiCAAAaIYoAgAAmiGKAACAZogiAACgGaIIAABohigCAACaIYo0mUKhePo0pmq3efHSmdZtvdPSUktfLScn59Xrl1W7a7UVKxcE/DqsCjeYmZnRuq332XMnqmoXz188k0r/32TzVV5zVSnjGwpQrRBFmmz12sXrNiyjZddjxg0MDj5bTRsXikRCoaiaNl75XVwOOT9h4giJJL+qNgig8bh0FwDVqOD//zD/rrsuqMZJayZP/K36Nl75XUiLe9m/Q80AzIUoql4SiWT3ni3hEZcLCqS2Nvb9+w9r07qDXC73/3Uol8PdumU/h8ORyWQB44dpa/M3bdzD4XC+JCZs3bruwcM7WlratV1cR40a71rHjdra06cx+//Z+fzFU0JIw4Y/jRwR4ORYq72fz9gxEwcPGkGtM3vO1MzMjK2bA1esWnD1WighpHVbb0LI4UPnLC2sCCGPYu7v2r357dtXhoZGnh6NxoyeYGxsUvqzeP0mdtPm1bGxz42NTGxt7dXLnz6NOXBw99NnMYQQ1zruAQFT69SuSwgZOLhrerr4zNmgM2eDzM0tjh6+QAgJvnzuzJnj7+LeCATCxo2aTpww08CgxKnZpVJpvwGdmjT2nfPnEmpJTMyDaTP8ly/dsOHvFUlJifXqNdy0cQ8hJDo6aufuTQkJ8RYWVt279e3da8CevVuPHT9w5fJt6oEvY5//On74iuV/N2nsW1LBRQwc3FW9i9VrFl/6/807Fou1f98Jc3PLfw7siogISU5JMjY26dC+y4hf/DkczuWQ8xs2riCE9OzdjhDy+6z5Hf26Fd4gIUQul+8L3B5y5UJmZoa9veOIX/ybN2tFCDlx8nDE1Sv9+g7Zs2dLmjjVxcV15vS5dnYO3/wbO3Bw99WrV1JSk83NLTu07zJk8EgOh/P8xbPtOzbExj7n8wW+TVv8+us0PV290t9QQsjZcyeOBx1MTU22sLBq26bjgP7DtLW1Sy8AoPIQRdVIqVTOmTstMTFhyOCRBgZGMTH3Fy/5UyLJ79ypx4zpcydOGnn23InevQYE7t+RkBC/a+cRDoeTlpY6afIoa2vbiRNmslisK1cuTpk6ZvvWA46OzvfuR8/+c4qzk0uA/1SlUnn79g2FXF7K3ocOHpWSnPTly+fZfywihBgbmRBCHjy8+8fsye3bde7Vc0B2VubJU0emzwzYse0gn1/i7LQfP76fNn2cvp7B2DETORzuPwd2qe9KTEyQFkiHDR3DZrPPng36Y/bkI4fO8/n8BfNXzfp9okfDn/r1HcLT+s8U7M+fP7Wzc2jfvnN6uvjU6aO5ebnLl24oaafa2tod2ne5eOl0Xl6eUCgkhISGXTI3t2jc2HfG9Lm7dm2iVsvLy1uw6HcHe6cZ0+fGxb1JS0sp/R0pqeAiqxXeRft2nWv/N66ysjL37tvWu9dAW1t7hULx4MGdpr4trCxt3ryJPXhor66uXv9+Q5s0bta/39DjQQeXL90gEunY2NgV2SAhZM3aJWHhwUOHjHJwcA4LD/5r3syN63c1aOBJCHnx4tnx4wdmzJgrl8vXrVu6fOX8bVv2l/KMFArFn3OmPn0W07vXwFrOtd9/ePcp/gOHw3n//t2MmQEODs6zfpufmZG+L3B7cnLi2jXbSn9DA/fvDDpxsHevgfb2Tp8+vT92/J/4zx///GNR6a8qQOUhiqrRjciIJ08fHTl03sTElBDSrm3H/Py8k6eOdO7Uw61uvV69BuwL3GZman702D9TJv9uY21LCDlwcLehgdHa1du4XC71PTh0eM8Ll05PmjBz85Y1FhZWm/7eq6WlRQjp2aMf9fu6pL3b2Njp6xuI09Pq1/dQL9y0eXW3rr0nT5pF3fT29vllZN9792//3Lx1SdvZvnMjm8XesjmQasSw2WzqVz8hpF27Tu3bd6b+XaeO2/QZAU+fxTTy9nGt48blco2NTQrvevq0P1ksFvVvLpd78NBeqVRayi/ubl17nzx1JDIyws+vq1QqvREZPqD/cDab3cjbJyjoYL4knxCSniGWSqU//9ymfbtOZXlHSiq4yGqFd+Hh8ZOHx0/U8iVL51iYW44eNZ4QwuFwtm7Zr35GCV/ib0RG9O831NDQyMrKhhBSt249fX2Drzf48eP7kCsXhg8bM+IXf0JIyxZthw7vFbh/x7q126mVly5Zb2RkTAjp3Xvg1m3rM7My9fX0S3pG12+EP4q5/9vMvzp36lF4+cFDe9hs9qqVm3V1dAkhurp6y1bMe/z4YcOGXiW9oampKYcO7507Z2nLFm2pjRgbm67fsHzq5D+oXwMA1QdRVI2io6Pkcvngod3VSxQKhUikQ/179MjxN29e+2v+zCZNmnXv1odaeOfOzeSUpM5df1Y/RCaTpSQnfUlM+Pjx/ZjRE7T+28iogMTELx8+xH3+/OnCxdOFlycnJ5X0EIlEcu/e7e7d+6oPplEZSWGxWJFRV48HHfzwIY76tkoXp5W0KZlMdur00dCwS8nJidrafKVSmZGRbm5uUdL69vaO9et7hIUH+/l1vXnrukQiKfJtSwixsrR2d29w8NAePl/QrWvvb7445Sq4iKioa+ERIatWbhYIBNSS9HTxPwd23bsfnZ2dRQihvvS/6fGTh4SQ5v/NfhaL1cjbJzTsknoFPv8/2zc3tySEpKWmlBJFd+/d0tbW9uvQtcjymMcPPD0bqUtq1KgpIST21fM6ddxKekMfPLgjl8uXLpu7dNlcaolKpaLagogiqG6IomqUnp5mbGyybs32wgs5//3kC4XCNq39jhzd37vXQPW94vS0pk1/HjdmUuGHiEQ6ycmJhBAzU/NK1kMI+WX4uBY/tym83MioxHNFaeJUuVxOnWT62j8Hdu8L3N6n96BxYyaliVMXLvpDqVIWu6ZKpfpzztTYV89/GT7Oza1BZGTE0WP/lLSyWrcuvVesWpCWlhoadql5s1ZUW6EwFou1Ytnfu/ds3r5jQ9CJg7N/X9SwoVcpGyx7wUVkZmWu37i8Q4cu6iaUWJw2LmCIQCAcNfJXKyubvXu3for/UJZN5ebmEEIMDYzUS/T09PPy8nJzc4usyePyCCEKpaKUraWL00yMTTkcztd7MdD/36k4XV09qt1TyhuaJk4lhCxbuqHIn5mpqVlZnhdAZSCKqpGurl5GRrq5uWWxh6E+J8SfPnNMKBRu2rx65/ZD1G9tXV29zMyMr89UU99f4vSiP+HVB4hKQv2wpejo6BJCpFLJN8+Eq1FfZ+np4q/vkkqlh4/s69K558QJM4ptWhXe9ePHDx88vDvnzyXt2nYkhHyO/1iWvbdo0XbTljWnTh+9d+/26lVbil1HR0dn6pQ/+vcf9te8GXP/mn7s6KWSXpNvFlyKzVvWKJXK8QHT1EvOnT+Zni7esimQatiZmVkUiaLCT78wExMzqqlBHbalUo3L5ZZyuq4UOjq6X/9VUHvJyspU36TeQR0d3VLeUN3/dmoo+58HQFXBdUXVyMursUKhOHf+fxdO5uf/51oTlUq1Zs1iY2PTLZsC09JSNm1erX7Is2ePY1+9KPIQW1t7U1OzkCsX1CeHVCqVUqnkcDi6unqp/z1dr1KpqPYThc8XiMVpSuV/fvjb2NiZm1sEXz6nLkMul8tkslKegkgksra2vXY97OvVJJJ8qVSqPqWfmZVB9dSgbgr4gsJXTVL31nZxLXblkmhra7dv3/nI0f3W1raeHt7FrkP1nLaytO7da2BObk5iYoK+vqFMJsv87xdxYmLCNwvmcnmEEOo429du344MCwueNPE39bkfQkhWVoaBgaH6AGNmVoY6ewR8AdUEKXZrdevWY7FY0XeiqJsFBQXRd6Lc3Rt83bIpC0/PRvn5+eERIeol1F+Iu3uDmMcPJBIJtfDGjXBCSP36HqW8oZ6ejVgs1ukzx9RL1H8nANWNs2DBArprYIynUZl2dXUEOmX9ynBwcL53PzrkyoXMrIz0dPHlkAubNq/q2qU3l8s9e+7E2XNB8/5a7uZW38DA6J8Du+ztHR0dnJ2cXELDLoWGXlIoFJ/iPxw6tPd6ZHib1n4sFsvQ0Pjc+ZN37kTJZLLYVy82bV6traXt7Ozy9u2rGzfC7ewccnKyt25b9+zZY2Njky6dexJCcnKyI66GpKWlZGdnJScn2tk5mJtbXrp09tbtGyoVef786d+bVsnkMje3+qU8C11d/UvBZ+/cuSmXy1+9ehF04lBWViZ1fj4yKuL586cmJmYvXjzbsHFFXl6uhblV48a+hJDXr2MjoyK4XO77D+94XJ6Ntd3Zc0FJSV+EQtGNyIgDB3fLZDJPD+9v/gA3N7M4czZo6JBRhYsMDbskl8s7d+ohk8mGj+idmpqSlpZ6+syxAql09Kjx+nr6Z8+dSE1NNje3fHD/ztZt6ySS/HbtOjk7u5RUsJaWVljYpYeP7uno6FLdu9W7yM7J/n32JKFQ6OrqHhv7nPrP0MCIx+MFB59TKhUFMtnRo/uv3wjPzc3t2aMfn8/nC4RnzwW9//CORVjPXzytU8et8Ab1dPUSE7+cPnOMEFZqasq2bevj3r/9beY8S0vr5y+e3rt3e8jgkTwejxASH/8xPCKkW7c+xiUfQbW3d7odHXnx4uns7Kx0cVpo2KVduzd17dLb0cH55KkjMY8f8Hha0Xei9uzb2qC+5y/Dx7JYrJLeUAtzy+zs7CtXLr56/UIqlUbfublsxV+eno2+2de/sPf/5pjZahuaV/yMJtRMiKJyKG8UcTicVi3b5+RkXbsWeiMyIjcvp1PHHvXreyQnJ81bMLNVq/YD+w+j2gpv3saeOX2sTWs/K0vrZr4tP3yMCw29eO/+bZFIp0vnng4OToQQJ6datWrVfvz4QWjYpVevXlhb2zZv3trU1Kx+fc+4929PnDx06/YN36YtOFyuVCqlosjJqVZ2dmZ4xOXHTx7q6xv85NXY3s7RtY7bkyeProRefPHymbOTS/v2XUr/rnF2ctHXN3j48G7UzWupKckutV3fvn3Vv99QoVDYsIHXnTs3z5w9/in+w9ixk2xt7c+fP9mv7xAOh+Pu3uDNm9jQsEuvX790dXWvW7eeg4PT5ZDzl0POy+XyOX8uSU1NfvYsxs+v6Pn2IgwMDP/99/GoUeMLH+RUf63n5uXGx3+Munk1MirC2Nj0j1kLrK1tDAwMLS2sw8ODT50+mpeX26/vkKib19q162RjbVtKwXXd6r98+e+7d6+pzhHqXezY+ffDh/fy8/Oi70Sp//P29mnm20KlUp45GxR5I9zK2nbmjL+ePn2Un5/n4eGtp6tnamp+7Vro7duR2dlZ1HNUb5AQ0si7aW5uTvDlsxERISKhaOaMuVS3ggpEEZfLbdmyfWZmxrXroTdvXcvMymjVsr2bW31DQ6P69Tzv3b99/sLJ2FcvWrfq8NvMedRrWMob2qhRU6FQdPt2ZMTVkPjPH5v5tvRt2kLdTaMsEEVQMaySjmjD1w6v+Ni8twU+ZgAluXbsi3tTPaf6GOIIygfdFoDk5OQMGlJ868R/3JSuXXpV364nTx0TF/fm6+W+vi1n/76w+vbLLHiVQOMhioAIhcKdOw4Xe5eebolXtFSJeXOXy+TF9JsQ8MtxUEjj4VUCjYcoAsJms0u6cqi6qTs0QynwKoHGQ2duAACgGaIIAABohigCAACaIYoAAIBmiCIAAKAZoggAAGiGKAIAAJohigAAgGaIIgAAoBmiqBx0DXls9jemqgOoyfg6nG/N5ghQDERRObC5JCNVSncVAD+upA8SPWMMJwblhigqBytnfm5maXOeAtRkKpWKL2QbmmEWFSg3RFE5eLYyfH47E2kEUKwbJxLdfPTYHByhg3LD1HnlI81XHFn10beHuaWjkO5aAH4UBVLlrbNJDm7C+s2qd1YR0FSIonJTKFQRR5NfPcx2qq+Tm6Wgu5yqp1KpZAUFWoUm8AZaFBQUsFksLo9HdyGlEYg4yZ8kukbc+r76dbx16S4HmApRVEEKhSo1XiqXadSrl5eXJxQKV61a5e3t3aZNG7rL+YbMzMwVK1Y0adKkZ8+edNdSLd6+fXv16tVOnTpZW1vHxMQ0bNiQ9QP2TmOx9Iw4OvpcFjqXQiUgioAQQmJjY5csWTJz5syGDRvSXUtZbd68OTAw0M7ObteuXcbGxnSXU72WLl0aHBwcFRUlkUh4PB6Hw6G7IoCqhG4LNZpMJouMjCSEfPjwYfbs2QzKoZSUlPDwcELIx48fjx07Rnc51W7OnDlRUVGEkPz8/KZNm27ZsoXuigCqEqKo5kpOTv7555/z8/MJIR06dHBzc6O7onIICgr69OkT9e+wsLDk5GS6K/pODA0N796927RpU0JIeHj4rFmzXr16RXdRAJWFKKpxnj59On36dEKIlpZWdHR0hw4d6K6o3FJSUsLCwtQ3P378GBQURGtF35uXlxchpG3btn5+flQUXbx4sfBrAsAsiKIahGpGnDp1qk+fPoQQAwMDuiuqoKCgoI8fPxZeEh4enpKSQl9FtGnbtm3Xrl0JIXXq1AkNDQ0ODiaE3L17l+66AMoHUVQjxMbGtmzZMi0tjRAyf/78Zs2a0V1RpXz98//Tp0814YxRKWrVqrVy5cpOnToRQu7cudOkSRPqxBLddQGUCaJIk8XFxQUGBhJCWCzWxYsXPTw86K6oamRnZ1tYWFhYWHC5XH19fQsLC3Nz85s3b9Jd149i0qRJd+7cIYTk5uY2atRow4YNdFcE8A3ozK2ZCgoKsrKyAgICpk6d2rx5c7rLqS7+/v5jx4719vamu5Afl1KpjI6O9vX1ff369f79+4cNG1anTh26iwIoCq0iTXPv3r2BAwfm5eXp6OicOHFCg3OIEGJiYoIrbErHZrN9fX0JIS4uLs2aNXv06BEh5OrVq9HR0XSXBvA/iCLN8fLlS+r/ixcvNjAw4PP5dFdU7RITE3/EAQh+VJ06dRo4cCAhxNLS8sCBA5cuXSKEvH//nu66ABBFGuHZs2fe3t5SqZQQMmzYMBcXF7or+k5sbW21tDAlQbm5urpu2bKlffv2hJDQ0FA/P7/4+Hi6i4IaDVHEYCkpKdu2bSOE8Hi8+/fvM2ishKry/PlzbQzbWlE8Ho8QMnbs2EOHDlH/Hjp0aA3viAh0QRQxUl5eHiFk2rRpTk5O1DUldFdEj9zcXJFIRHcVjGdiYmJubk6NdCeXy6nroI8fP061swG+A0QRw6Slpc2aNevFixeEkIMHD/r5+dFdEZ1sbGx0dHTorkJz2NvbDxkyhBDi6OgYFxe3fft2QggGFoLvAFHEGNTR/GvXrvn5+f300090l0O/jIyMN2/eIIqqg46Ozu+//z5lyhTq6rQmTZo8fvyY7qJAkyGKGKCgoGDKlCmnT58mhPTp06dt27Z0V/RD+Pz5s7W1Nd1VaD4/P7+bN28aGhpSQ3Xs3LlTJpPRXRRoGkTRD+3Vq1dKpTItLa1fv36TJk2iu5wfS0pKCjUqKFQ3LpdrZ2dHCJk8ebJKpfrw4QMhJCIigu66QHMgin5cf//99/z581kslqWlpWZfqVoxd+/etbS0pLuKmsXY2Njf379WrVrqSwgkEglGbIHKQxT9cDIyMqjj8k2aNDly5Agu4SzJ48ePa2D/9R/H5MmT79+/z+Fw5HJ53759z549S3dFwGCIoh/LkydP+vTpY2RkREUR3eX8uCQSyfv3711dXekupKbj8Xg8Hm/16tWpqanUdMD379+nuyhgHkTRjyI0NJSazi48PNzW1pbucn50t27dwkHLH4ejo+Po0aMJIfr6+rt27Vq7di0hhLpECaAsEEU/hP79+3/58oUakYXuWpjh0qVLHTt2pLsKKMrAwGDHjh0jRowghBw7dmzevHnp6el0FwUMgEki6JSUlJSWlubm5hYfH29jY0N3OYwhkUjatm2LCYp+fBcvXtTV1W3RokVUVBRasVAKLt0FlJtKpdKM+Hzx4sWiRYu2bdumVCpxfUy5XL16lZoTHX4cxX4wqVlllUrlx48fO3XqdOrUKS6Xi3k9imCzcXSKga0ihUJBTYzNXDKZjMfjyeVyLvc/PwUEAoGuri7ddTFG3759V69e7ejoSHch8D9KpZLqufDN1XJzc4VCIQKJoqWlZWBgQHcV9EMaf2/5+fkSiYS6bJDuWhgpPDzcyckJOcRQbDZbS0uLGmhVoVDQXQ78KBBF3w/VoYjL5aIBVBm7du0aO3Ys3VVAxWlrawuFQurwQHp6ulKppLsioB+i6DvJzs6mooiaGAYq5ubNmy4uLjVnbkDNxufz9fT0qHME1KECqLFwjKjaUadztbS0MMlb5S1cuPDIkSN0VwFVRn3GSKVSicVi6uJuqIE0pFWkVCr3798/dOjQAQMG3L17lxCybt06aoh7WqxatWrcuHHUHHcKhYLNZiOHKm/NmjUjR440NjamuxCoegKBgMohmUyWn5//9Qpbt24dPHhwWTZF72c/ISGhc+fO165do6sAhtKQVtHly5dPnDgxatQoa2trd3d3QohQKBQIBPRWpT45RG8ZmuHJkyf//vvvzJkz6S4EqhePx5PJZBKJhM/nV2wLP8JnH8pLQ74l79+/37Bhw169eqmXBAQE0FoRoQ4+UKdnofJmz569Z88euquA70H9qcnOzq7Ake0f4bMP5aUJUdS1a1eqE07nzp0DAgK6d+8+YsSI5ORkNze3NWvWXL9+feXKlXPnzvX19SWEUDcXLFjQuHHjUraZnJy8f//+hw8f5uXlOTk59erVq0WLFlRP4uPHj3/58sXIyKhjx479+/dXX552/fr1w4cPJycn29raUnOLUYNqSySS/fv3X7t2raCgwMbGpnfv3i1btvxer42GWLZs2fjx4y0sLOguBMonMTFx165djx490tbWdnZ2Hj58eO3atb98+TJhwgQ/Pz9/f39CyJcvX8aPH9+1a9fRo0cvWrTow4cPtWrVevjwIZvN9vb2HjRoULEXgF+5cuXChQvv378XCAReXl7+/v7U1TmFP/uEkH79+k2YMOH27dt3794ViUSdO3cuy1G+kJCQc+fOxcfHi0SiJk2aDB8+3NDQUC6XHzx4MCwsLCsry9bWdujQoU2bNqXWz8jI2LlzZ3R0tLa2doMGDb75ClTdC6w5OAsWLKC7hvJRqVRFDiU7OTm9e/fO1NR04sSJ7u7uurq6dnZ2Hz584HK5HTp0cHBweP36dXh4eMeOHbOyshYsWNCmTZvevXuXsguxWDx16tSEhIQ+ffq0bNlSJpNpaWnVrVs3LCxs3bp1Xl5egwYNEolER48e5X1k3QgAACAASURBVHK59erVo67/X7VqlaOjY58+fUQi0Z07d3R1dbt166ZUKufNmxcbG0ttqqCgYP/+/SYmJtSML2o8Hg8nk0qyZ88eHo83ZMgQuguB0qhUqry8vMJLxGLxtGnTtLW1+/Xr5+np+fbt2yNHjvj4+Nja2vJ4vGPHjvn4+Ojr6y9evFhbW/v333/ncDjXr19/9+5d27ZtBw4caGtrGxwc/OjRIz8/P0LI7du3P3/+rB5l4+LFi0KhsF27dra2tuHh4e/fv2/VqhUhpPBnnxASFBQUFRXVsmXL4cOHczico0ePuri4lD64ycGDB3fv3l2/fv1evXo5Ojq+fv26VatWWlpaGzZsuHTpUs+ePTt37pySknL48OGGDRuamZkVFBTMmjXr5cuXvXr1at68+aNHj8RicbNmzRwcHEp6BQpf08rhcCp8KFKTaEKryMfH58SJE3w+X/0jxcvL69SpU+ruoePHjw8ICDh69Oj79+91dHS+eVXK4cOHMzMzt27dSo2Q3a5dO+qTtn//fnd391mzZhFCmjVrlpOTExQU1KNHDzabvXPnznr16i1cuJC6fDo5Ofndu3dU5+N///1337591Mn2Vq1aSSSSs2fPUp8u+KbQ0NDXr1+vWLGC7kKg3I4cOWJgYLBs2TLqdGmbNm3GjBkTEhLi7+/fo0ePa9eubd68uWnTprGxsRs3btTS0qIeZWdnR/1SrFOnjlAoXL169f379318fIqMzjBp0iT1VF4cDufYsWNSqVRbW7vIZ58Q0qFDhwEDBlC/WUNCQh4+fFjKEZHU1NRjx461adNGfVayb9++hJBPnz6FhYUNGjRo6NChhJDmzZuPGTPm0KFDy5cvv3DhQlxc3NKlSz09PQkhdevWpVp7pb8C1fB6M5smRNE3mZmZ/fLLLzt27GCz2atXr/7mKU3qzFORmRo+f/6clpZWeOgzLy+vkJCQz58/Z2dnZ2Zm/vrrrwUFBTo6OoUHlbp3755cLh81apT6UQqFQiQSVfVT1ExPnz4NDAw8dOgQ3YVARdy/fz8lJaXwR0Ymk6WkpFDhMXny5KlTp758+XLkyJEljZ3h7e1NCImNjS0cRdnZ2To6OjKZ7Ny5cxERESkpKdra2kqlMjMz08zM7OuNqNscHA7H2Ni49GHDHj16pFAounTpUmT5s2fPCCHUQX7q2LuXlxc1pfqtW7ccHByoHCrcPb30VwCKqBFRRLVs9u7d6+TkVLdu3W+unJGRof7DUsvNzaXGwFcvoQZNSE1NzczMJIRYW1tTOVRYenq6kZHR8uXLCy9En7qy+PTpE3KI0dLT0xs3bjxy5MjCC9W/w2rVquXi4hIXF0cNmVoskUjEYrGKXP0qEAgyMjJWr179+vXrIUOGuLq63rp168SJE2UZtYHL5ZY+2hA1pYWJiUmR5cV+/PPz8/Py8lJSUpydnUvaWimvABRWU74T9+7dy+FwYmNjL1++/M15bkQi0deTrJiamhJCqNShZGRkqAOpyF1qOjo61I81nAoql9jY2JkzZ54/f57uQqDidHR0qDP8xd577dq12NhYPp+/detW6qD319LS0lQqVZFg4HK5nz59iomJmT59OnXwPCEhoQprpiKE+ryrUQfYs7Oz1Ze1paenc7lcbW1tfX196qug2K2V8gpAYRpyiWvpYmJigoODx40b16VLl507d3769Kn09Rs2bBgTE5OYmKheIpfLjYyMzM3NC0+WHBkZqa2t7eTkZGxszGazr169+vWmPDw8FArFpUuX1EuKvXwPCrt///7ChQuRQ0zn4eHx/Pnz169fq5eo//gzMjJ27NjRunXradOmXbt2LTw8vNgtXLlyhTr7QnXtkUgk1LV6WVlZVLtKLBarb1bJJANU/7eQkBD1EmqPrq6uLBaLunyeEFJQUHDv3r26detyOBxnZ+fXr1/Hx8eX6xWAIjS/VZSfn79x40Z3d3c/Pz+pVBoTE7Ny5cr169eXMhbcoEGD7ty5M2PGjO7duxsaGj569IjP50+ZMmXIkCHr1q3buHGjl5dXTEzM7du3hwwZIhAIXFxc2rdvHxISUlBQ8NNPP4nF4nv37hkaGlInKi9fvrxnz56kpCRnZ+d3797dvn17+/bt6DNTkqtXrx49evTw4cN0FwKVNWTIkHv37s2dO7dXr14GBgYPHjxQKBTz5s0jhFDTdI0dO9bAwCA6Onrr1q1ubm6WlpaEkA8fPgQGBlpZWb148SIkJKRRo0Zubm6EEGdnZ4lEsmzZsrFjx7q6umppaR04cMDPz+/9+/fHjx8nhLx//57aQmXY2Nh07NgxODg4Ozvby8srKysrODh4+fLllpaW7dq1O3TokFKptLCwCAkJSU9Pp7o29O/fPyIiYtasWT179jQyMio8zkIprwAUoQmdualfT1wut02bNuolERERcrm8Q4cOu3fvfvLkycKFCw0MDLhcbp06dYKCgnJzc6kzosXS19dv0qTJ+/fvIyIiHj9+zOFwWrRo4ejo6OTkZGBgcP369dDQ0MzMzP79+/fv358a18fT0zMvLy86OvrBgwdsNltXV1cikXTr1o3D4fz88885OTmRkZE3b97Mzc3t0KGDu7t7kcmy0Jmbcvr06fDw8E2bNtFdCJTb1525dXV1fXx8Pn36FBER8eDBA5FI5OfnZ29vHxUVdejQoYCAAOpCCA8Pj7CwsAcPHrRr1y4yMjI/P7+goCAkJCQxMbFt27YTJ06kOtfZ29tLJJIHDx7UqVOndu3adnZ2YWFhYWFhcrl8+vTpqampz58/p47XqT/7VGfuWrVqeXl5USUFBwcLhcLSL+xr1KgRj8e7e/fujRs3Pn/+7OXl1bBhQ6FQ6OXllZube+XKlevXrwuFwsmTJ//000/U03Rzc4uNjY2MjHz37l2DBg1evHhBdeYu6RUovDt05qZg6rxKyczMFAqFlR9sG1PnEULmzZtnamo6adIkuguBiijj1HmlW7RoUWpq6t9//13eB1LT8am7dzMIps6jaP4BumLl5uaOGDGi2LtGjx79zX4NFKVSyefzMelD5UkkkmHDho0YMeLrTrQAZSESicr+q/ru3burV68u9q61a9fa2dlVaWlQJjU0igQCQUlHgfT09Mq4EYy3XSViYmImTJhw4MABJycnumsBBmOxWNQRwm8O/NigQYOSPv5fd+OG7wMH6CpIpVLl5uZ+fSFRxdTYA3Rnzpw5f/48xjnVAFVygK7yZDIZi8Vi0KV7OEBHqRGduauDVCqluwTGmzRpUmpqKnIIqhCPx2NQDoEaoqiCeDwerpqusJs3b1KDLo8ZM4buWkADUdfG0l0FlAPzfj6wWCzN6/tYo/o+rFq1Kj4+vvDFwqAB2Gz2j/PBNDIySkhIKGk8nh9KkWFeayzmnSv6EUil0oCAgH379tFdCMPEx8dPmDBh8ODB1EjJAAAU5rWKfgQp/9fefcZFcbVtAD/bC1voTVoQe0MjoGISSSRGsPcaS2yxpahJ7CYm5lETC3Zj7yUqNhQFrKioGAhGA2KnSVvqsn3fD5NnH15clkibZb3+Pz/IzHD23mHOXjtzpuTkUHccgX9v+/btDx482LBhg5ubG921wFvh22+/Xbx4MZ6k3CBgrKg67Ozs1q5dS3cVDcbjx4+HDh2qVCp//fVX5BDUm8DAwMquHwJzgwN0ULc2btx4+fLlZcuWVXhwLQCAAfaKqiMtLW3u3Ll0V2HuHj582LdvXx6Pd+TIEeQQ0EKj0ahUKrqrgKphrKg6RCKR4XbxYNSKFSuePHmCkSGgl0KhCA0NvXLlCt2FQBWwV1Qd1tbWP/30E45tGnX+/PnAwEBPT8/Nmzcjh4BeIpGoU6dOcXFxdBcCVcBYEdSa9PT0pUuX2tnZLVy40HwuMQEA84cDdNV09OhRW1vbjz76iO5CzMWGDRsiIyMXLlzo5+dHdy0A/6NUKtPS0hrE5a5vMxygqyZXV9eTJ0/SXYVZiI6OHj16tEAgOHXqFHIIzA2Pxxs2bJhOp6O7EDAFe0XVFBgY2Lx5c7qroNmzZ89+/vlnqVQaFhZGPUAdwAwNHz48Ozvb2dmZ7kKgUhgrgmpasWJFXFzc3LlzTTyaHQDg38ABuupLSEhYtGgR3VXQ4NSpU/7+/p6enseOHUMOgfnLzs4uKSmhuwowBVFUfb6+vomJiWlpaXQXUn9iY2MHDBjw9OnTmzdv4pam0FBs2bIlKiqK7irAFIwV1Uh4ePhbcoTzyZMnv/76K4vFWr16taenJ93lALyBli1bOjg40F0FmIKxopq6ePFicHAw3VXUIblcvmvXrkuXLs2aNatTp050lwMAFggH6Grqr7/+2rt3LyGkf//+lvdJ/dtvv/Xo0cPLy+vo0aOW9+7gLZGQkPD48WO6qwBTsFdUU3K5vE+fPqWlpWq1mslkLlmyJCQkhO6iasHJkydXrVo1fPjwKVOm0F0LQHX07NkzOzubEKLX6xkMBjXR1dX19OnTdJcGFWGsqEZ69+6dmZlp+JHH41nA44FjY2PDw8PFYvHZs2dFIhHd5QBUU7du3Y4cOcJgMAw5xGQyhwwZQnddYASiqPpCQkKo71wGTCazQd97LSUlZfXq1RwO5+uvv/by8qK7HIAaGTFixI0bN9LT0w1T3N3dEUXmCWNF1RcWFubt7V3+hiIsFovL5dJaVDXJZLJFixYtXrx43LhxYWFhyCGwAO7u7oGBgYYxCBaLRT1Ai+66wAhEUfX5+Pjs3bs3ICCAzf5n55LL5TbEDT0sLGzw4MEBAQEHDx709/enuxyAWjNs2DDDk0oaNWo0ePBguisC4xBFNcLj8TZt2tS7d2+pVEqNjnI4HLqLegNU9kil0qioqNDQULrLAahlHh4e1JmfLBZrwIABAoGA7orAOIwV1YL58+d7e3vv2bNHr9c3lLGiqKio1atXBwUF3bx50wJOtQCozPDhw+Pi4lgs1qBBg+iuBSpV9cnc96Jl2S+V8hJtfZXUUMnl8uzs7AYxyiKTyRRlZQ4ODuwGtQ9XntSewxMw3ZsKPFtY0V1L1VLiizOfKtRqXVGehu5a3kZZWVkcDsfOzo7uQt5GUnsOj890aybwMtlVTUVRXoby4MqX7brZSu05QhH2n8CcMEhOuqJEpuZyGR8MMt97uuj1+lNbMq0deQIxy86Zp8dDc+BtwyC56YqSAjWLRYKGOFa6VGVR9OqF4lp4bo8xbnVZI0BN3bmQw+MxA/uY6RfeU1sy3JqJmrSX0F0IAM3uXszlcEjXvvZG5xo/bUGn0186khM01KWOawOoKb+PHUqLNamJxXQXYsTdi/mOHgLkEAAhpGOwfVmpLuVekdG5xqMoPbWMy2Ny+RjNhgbAtbFV8l1zfBrNwzvFbk0bwFAWQP1o5GOVHG+8qxqPItkrtaOXsI6rAqgd9o34KoXZDcKoVXoujym1b5CXPAPUBXtXvlphfEjI+MkICrmWmF3XBjCOzWbmZSjprqIirVpfnK+muwoAM8LiMHIr6aq4xBUAAGiGKAIAAJohigAAgGaIIgAAoBmiCAAAaIYoAgAAmiGKAACAZogiAACgGaIIAABohigCAACaIYoAAIBmiCIAAKBZbUZRamrKzC8n9AztOnvOVELIkyepffoGXY+9XCuN/7hswadjB1a5WFZWZmZWRq28YgUajWbUp/03bV5Ti22uDVs+YNDHhh9ruMa0Wm1SUkL5KbX7J6hdg4f2XLV6Gd1VWLJxnw35YencKher9Q3bIrvq5StRQR91fPHiGfUjumqtq7UoUqvVCxZ9rdfrFy9aPm7sFEIIm80WicRsVv09iTw9I23EqD7JyQ/qonEGgyEWS/h8fl00TqnhGlv569JVa/7fFlP/fwJocOphw34duiq6agW19s6fPX/y6lXWwvnLWrVqS03x8PA6sP9UbbX/b2g1msqej15zLBZr04bdddQ4pYZrTKWsePf1+v8TQINTDxv269BVa7dBC1A7UbRn77aduzYTQqbPHC+RSE+eiD4feXr5iu8JIStXbPBt9+7kz0exWeyNG3azWCy1Wj1l6mgej79u7XYWy9SDYmMuXdi9Z+urV5lent463T8PUFKpVHv2/hYTE5md88rOzv7j4NCxYyazWKzMrIwx4wYRQr7/4bvvCenRo9d33yzJzn61fefGuLjY0tISd3fPEcPHdf/oExOveOjwni1bw/bsOubu7klN+erryWVl8sWLl48Y2YcQMmrk+M/GT1UoFGvC/nPjxlVCSNu27adPne3s7DLji88EfMGK5eupXzx8ZO/mLWvPR8TyeLxz50+Fhx958jRVIBD6+3WePm22tbVNhZcuv8Y6vhswanS/9Iy08gs4ODgeORSRlJSwd9+2pPsJhJDmzVpNmfJls6YtCCH/WbHk0uWLhJCgjzoSQg7sP5WYGF++QULIg4f3N29Zk5z8gM8XdOn8/ueffyURSwghCxbNcnfzZLPZZ86e0KjVnTp1/WLmdyKRyPQfPSkpYfeerQ8eJhFC2rV7d9zYKU2bNCeEXLhwdv/BnRkZaXZ29qEh/UeOGMdkMqmDEnv2/nbm7AmFoszXt6NSoTA0pVAotm3fEB1zXqVSurt5Dhky+sOgj02+uMXKzMrYuHFV/L04LpfXtEnz8eOnNm/WMiMz/bMJQ0NC+s2YNpvapZgwcVjfPoOnTP5iwaJZz54+btKk+d34WwwGMyAgcOqUr2xsbCs0a6LXlN+wH6Umz5g5/j/LwrZuW/f4cYqTk8vkiTMDAz+osmx01fILoKtWQ+0coAvqFjx2zGRCyKSJM+Z+9wMhpL2v36SJM6i5bDZ71tcLHqUmnzz1OyFk1+4tGRlp8+YuNZ1DUdHnl/44z87Wfsb0OX5+nR8/eURNZ7FY8fFxnbu8//mUrzq099+3f8ex4wcJIXa29vPn/UgIGTd2StiabaNGjCeEaLSav//+q2+fQZ9P/lIikf60bMHDv/8y8aKf9OjNZrOjos9RP756lZWQGN+790Aba9ulP/zCZv+T3AcO7oyMPDNo4IjJk2YWFRUKBALT6+fBgyQPD6/Jk2b27jUg9saV5Su/f32Z8muMEDJ27JQvv/iO+tejRy9CyMzp3xBCsrIylCrl6FETxnw6KSsr47u5MxUKBSFk1IjxHdr7uTi7hq3ZFrZmm52tfYUGnz17Mmv2FLVa/c2cxWNGT7x+/dL3339rmHvk6L6srIxlP62ZPm325StR+/ZvN/2O7ty99dWsycXFRVMmfzlp4kydVqvVaAghkZFnfl6+uEmT5gsXLOv2QfCOnZv2H9hJ/crasOV79m4L8A+cOf0bPo9fXFJMTdfpdPMXfHXz5tWRI8Z99eU8H59mS3+cF3HupOkCLFJeXu6MmeOLigunT5s9edJMtVr9xZcTnj597OrSaNzYKeHhR1JTU3Q63fIVS1xd3caP+5z6rZzc7BYtWq9YvuGz8VPj4mK/+Xa6RqOp0HJlvabChk0IUSqV3y/9btDAEWtWbXV2cvlx2fzCwgLTZaOroqvWXO3sFbm7e1LH5dq17dCyZRtCiJOTc7u2HQwLtGzRun//oTt3bXJ0cDp0eM8XM791a+RuokGlUrl+wy9t27ZfuWIDlVjp6S9TH6dQ2/fGDbsZDAa1ZEZm2tVrMUMGj+JyuVTUe3h4tWnjS811dWm0a8dRauGePfv2H9g9NvZyi+atKntda2ubroHdoqLOUcNdUdHnRCLRRx9+wufzuwZ2M7xoZlaGQCAYMXwsm80ODelX5fr5+qt5ht9ls9n79u9QKpU8Hq/8MhXWmOEroUKhOHJ0X7cPunft2o0Q0r17z+DgEGpWs2Ytv541Jel+gl/HTm5uHlKpdb4sz/DeKzS4b/92JpO5Yvl6sUhMCBGLJcv+sygx8V67dh0IIW5uHvPmLmUwGC2at7p6PebO3ZtTJn9h4h2t3/CLs7PrurAdXC6XENKv72BCiF6v37ZjQ5s2vgvm/UgIef+9D4uLiw4d3j1wwPC09BenzxynvqgSQnr06JWQGE81dfVazJ9Jfxzcf9re3oF642Vl8mPHD4b07FvlirUwe/dts7G2/XXlJuqTNLh7yKhP+52JODFj2uyBA4ZHR59fvfbnroHdHj68v3njXmrNE0K8PL2HDB5FCGnRvJWVleinZQtu377Rpcv75VuurNdU2LApM6bPob7qTpgwffKUUYl/3nv/vQ8rqxldFV21Vrpq/Y2SfTZuamzs5YWLZwcEBPbpXcUJNkn3EwoLCwYNHGHYc2KW24WSyfL37P3tzt1bxcVFhBDqD1aZ1Mcpu3ZvoQZItVptfn6e6Zfu1WvA7DlT799PbN263YWLZ4ODQ18f/+z+Uc/o6PPffjdj2tRZ3t4+Vb11olarj584dDEqIjs7i8fj63S6ggKZk5Nzlb9ICPlt+/riosIZ0+dQPzIYjGvXLx05uu/586dCoZAQIqvqHVESEuPbt/czrCs/v86EkOSUB9T2zefxDT3Qycnl/v1EE01lZmW8ePFswmfTDJ+GlLS0F7m5OUOHjDZM8fPrHHHuZFr6i2vXYgghgwaNNMyiDgUQQm7duq7RaEaM6mOYpdVqrayqOOZgkeLiYrNzXoX0es8wRa1W52S/oj7WZ81a8PnUTx88SJo0cUbjxk2MtuDv34UQ8vDv+xWi6I16jYD/z66Dk5MLISQ3N8dEzeiqBuiqNVF/USQUCj8M6nHw0O4B/YdVuXB2dhYhxNnZ9fVZ+fl5k6aMFAiE48d97urqtmPHxpdpzytr594fd779bkZ7347fzFlsJbRatGSOTq8z/dId2vs1auQeFX2OzeG8ePHs+8UrXl8mwL/Lz8vWbt6y5rOJw0JD+n35xXflD3FUoNfr583/MjnlwZhPJ7Vs2fbatZhDh/dUWQYlKSnhxInDc2YvtLW1o6ZQw3IDBwyfNGFGXn7u9z989y+bKi0tsZb+76i3WCyp7FOGw+bodFoTTRXI8gkhjg5OFaaXlJYQQqyt/zdQ8c+r5GS/ys4SiURSifT11mSyPDs7+1W/bC4/kVX5+rRg+bK8zp3fmzRhRvmJhq7etEnzZs1aPn6c0qvXgMpaEFmJGAyGvExeseU36TUGHDaHEGJ6Y0BXpaCr1lC9nml9IvywUChct37l1s37TR+0pf4SBQWy12edOn1MJsvfsG4X9VXF0dHZxPa9d+82V1e3ZT+tobY/w9c9ExgMRmhIv0OH9+j1+rZt23t5eRtdLMC/i1/HTseOH9y4abWTk8voUZ9VOMphkJh4L/7e7fnzfqR25NPTXlRZA0WhUCxf+X173449P/nna4hSqTxwcGdoSL/p02YRQrKzX1X4FRNnJdnbOxYVFRp+lMnyCSEik99SK0N9OObLKn7Fo7b48kML1KuIxRJrqU1JSYlKparw7YyaW1Agc3JyqXAY5C0kFksKCws8PLyMzo2OiXz48L5AIFgbtpw6rvK63NwcvV7/+kfPG/WaN4Kuiq5aK+rpbgt6vf6XX5ba2TlsWLcrLy9n3fqVppdv3Lgpk8k0jEmWV1RUYG1tY9hlLiwqMPxReTw+ISSv3NeHwqICn8ZNqY1bpVLJy+SG03tM6PlJH7m89PSZ4316DzK6gEqlovZbBw8aaW/v8OjR31SfzMvPNSyT9d/L9wqLCqivtOV/pMrgcLhlZfLXB5kpO3ZuysvL+frr+YYpCkWZUqls2rTF600RQvh8QX5+XmVvsFWrtgmJ8Yr/ngxz9Wo0IcRwtPqNuLt7Ojg4Rl44Y6hcr9frdDo7O3tnJ5fbt2MNS165EsXn8318mlE1R8ecf721Dh38tVrtqdO/G6aUlZVVoyoL0KGD//37ickpDw1TDKuioEC2bv3K7t17fjNncXT0+QsXzhptgRpDbtWyLSGEy+FSh8VM95oaeku6KpfDJYSUT4jy0FVrrp72ik6e+j0hMX7lig1eXt7Tps765dcf/fw6B3ULrmx5Jyfnnp/0ORsRrlIq/f275OXlxsVdt7GxI4T4+nY8EX5kx85NrVq1u3YtJi4uVqfTFRYWSKXWjo5Ori6Njvy+jy8QFBUVDug/zNe3Y2Tk6YhzJyVi6dFj+4uLi549fazX6yv7WkShRkT/SLhb2Wjt8ROHYm9cCe4ekpeXk5ub06xZS+pg67XVl44c3efr2/HGjStnI8KphVu2aMPlcn/btj40tP+TJ48OHNxJCHn6JLWRq1sTn2YKhWLJD99+PuWrRq5u5V/ir7/+/P3YgbZt29+9e+vufyf2Cu3v7e1z/MQhW1u70pKS3Xu2MpnMJ09Sqbnt2nY4d/7UqtXL2rT2FYslFUYLRo0YHxMT+e3cGb17DczOztq9Z2t7346+7d79l3/B8hgMxqSJM39atmDa9LE9evRmMpkXLp7t33dIcHDI2DGT/7Niycpflvr5db537/b12MtjPp0kEAiCugXv3bdt1eplT58+buLT7K8HfxoOOAR3Dzl95vjmLWszszKaNmmemppyPfbSrh2/1/NFl+ZgzKeTbt26PuebaUMGj7Kxsb19+4ZWp/3xh1+pk5p0Ot20z7+2traJ7d5z7brlrVq3o7aZp88e/7ZtvZubx/37iRHnTgYEBLZu3Y4Q4uPTLOLcyQ0bV02aOMNEr6lhzW9JV33H24fJZK5e+/P0abPb+3Ys/xLoqrXSVVlLlix5fWr64zKthjh7Vb2PbJCRmX7xYkRoSD+H/x4cyMnJjjh38uPgUCaTtWjJ7G7dgocNGU196Uh9nBx+4vCHQT1M7HW++25AaWlJ7I0rd+7coC6fLisr699vqKfnO3q9Lvzk0WtXo10buc+etTAp6Y+yMrmvb0cGg9GyZdvbd27EXIrMzMroGhgU4B/4/PmT4ycOJSTe7fZB8IB+Q2MuRTZp0tzFpZHptyMWS0RWIn+/zuUn7t23vXWrdh3a++XL8hIT4qOizz17/qRnzz5jx0xmMpmNvZsolYpTp38/d/6kE8SeDAAADelJREFUg71Tx3cDkpISRo38TCKRenl5n488fT7ytEajmT/vx9zc7Pv3E3r06PXOO40VirI7d262aNbKw8PLsMZcXd3mzv9CJst/9SrzVtx1w78Rw8d1aO8fFxcbfvLIy7TnEyfOcHf3PH362OBBI1kslre3T3FxYXTM+cQ/70ml1u928C/foEQibdO6/Z27N0+fOZac8jCo28dzZi+idrRjLl2Ql5b2/u8IxN27tx6l/j1i+FgT68fb28fHp2liYvzFqIiUlIeNGrl37Rrk4ODo49PUxsY25tKFc+dPFcjyR4wYN2rkeAaDwWQyO3d672Xa8ytXov5M+uMdr8aZmemenu907vwei8Xq9kFwSUnR5csXr16LKZWX9Pykb5s2vobB0ipp1fq/bxd0+Kji9R/00qj1f14raB34BlVJxJLALh88f/H04sWzd+7etLIShYb08/LyvnI1etfurTNnzGnbpj0hpEN7/8gLp+/cvvFJj96Xr0TJ5aVKpTLiXHhmZvrHwaFffTGXOrTSskWbjIy069cv9es3tEmTZpX1mv+3YefnnT5z/KMPP6Eu1lGr1QcO7vT360ydFluZt6GrikViF2fXe3/cYTKYfh07oatWs6tq9A9vFbzb3UinYBjdT78dma9SkHbdKl4oB2CGlHJd+PpnE34yPlRAF0Wpbt+yZ0O/qduqFiyalZP9asvmfXX6KgC1QqXQHVvzbNLPRjoFnecp/bZtffnDjgYSsXT/vjq8wrGkpGT4yF5GZ02e9EWv0P5199INCNYSGKCrmjPLWEt0RtGQIaONnpbKZNTtyRRCoXDrlgNGZ0nERk5hfDthLYEBuqo5s4y1RGcUSSVSo2ev1zUmk+li7DIIKA9rqUGgTmqoa+iq5swy1hIenQcAADRDFAEAAM0QRQAAQDNEEQAA0AxRBAAANEMUAQAAzRBFAABAM0QRAADQDFEEAAA0Mx5FDAYhpm7NDmBOGITDM7/tVa9n8/BVD+D/qayrGu8qQgmrtND489wAzE1poZrLZ9FdRUV8EUtepNFqaucJdQAWoLSo0q5qPIrsXHhlpYgiaBgKc1XO75jjc/bcmggLc5V0VwFgLgpyVK6NjXdV41Hk7MlnMcnL5NI6LgygFtyNzPX/2ByfreX7gfXdC3l0VwFgLuIv5HU09tw8U6ct9Jrg8uCm7PmDkrosDKBGtBpdxPa00AkuVlI67zFfGY/mwtZdJDGHMuguBIBmWq0+YvvLkHHOYhuO0QWMP8XVIGJHZmGeWmzDFYjNsavDW4svZKU9KmWzSUBPW7cmQrrLMeXBraKUP0o0ar3LO0KFXEt3OQD1ii9kpj+Ss9jEr4etR7NKu2oVUUQIyc9W5aUrS4vQhcCMcAUsG0e2swefwTS/c+deoyzT5qQpC3PVahXOYqBBTEyMra2tr68v3YW8jbh8po0jx9mziq5a9b6OrSPX1pFbq7UBvF14ApZbE6FbE7rreFtdiEsVOHv6fmBNdyFQKVz3AAAANEMUAQAAzRBFAGDhuFwum40Tr8waoggALByTyWQwGsDpLW8zRBEAWDiFQqFWq+muAkxBFAGAheNwOEwmPuvMGv48AGDh1Gq1TqejuwowBVEEAAA0QxQBgIWTSCR8vjneux0MEEUAYOGKiooUCgXdVYApiCIAsHA8Ho/FMruHK0J5iCIAsHBKpVKrxQ2dzRqiCAAAaIYoAgALZ21tLRAI6K4CTEEUAYCFKygoKCsro7sKMAVRBAAANEMUAYCFs7GxEQrN+qnzgCgCAAsnk8nkcjndVYApiCIAAKAZoggALJxUKsWNf8wcoggALFxhYSFu/GPmEEUAAEAzRBEAWDixWMzj8eiuAkxBFAGAhSsuLlYqlXRXAaYgigAAgGaIIgCwcFwul81m010FmIIoAgALp1KpNBoN3VWAKYgiALBwfD6fw+HQXQWYgigCAAunUCjUajXdVYApiCIAAKAZoggALJyNjQ0enWfmEEUAYOFkMhkenWfmEEUAYOE4HA5O5jZziCIAsHBqtRonc5s5RBEAANAMUQQAFk4oFOK6IjOHKAIACyeXy3FdkZlDFAGAhRMKhVwul+4qwBREEQBYOKVSidMWzByiCAAsnFar1el0dFcBpjD0ej3dNQAA1L7u3bvLZDJCCIPxvw86JyeniIgIukuDirBXBACWyd/fn8FgMBgMKo0ooaGhdNcFRiCKAMAyDRs2zNnZufwUZ2fnIUOG0FcRVApRBACWqW3bti1btjQcmtPr9UFBQQ4ODnTXBUYgigDAYo0ePdre3p76v4uLy6hRo+iuCIxDFAGAxWrTpk3r1q2pHaOgoCAnJye6KwLjEEUAYMnGjBljb2/v6uo6evRoumuBSuHG6QBgRory1XkZKnmxRl6s1en0KkXNrzZx7dRknEgkSr7BTCa5NWyLL2QxmHorMVsoYTl58vlCVo3LA4LrigDALOS/UqXEF6cmlqqUOp6Qw+KymRwWm8vWac3rA4rFZqgVap1aq9PpirIVNk7cpu2tWvhL+FbIpBpBFAEAncpKtFdP5MpytCweV2QvFEh4dFf0BkplipLcUrmszKed1Xv97OkupwFDFAEAbe5cLLgXne/oY2vTSEx3LTWS+6wgK0XWfYRTc7+G/UbogigCAHqc3Z6l0LLt3G3oLqTWZP2d4+bN7toXu0dvDFEEADQ4vj6DbWUlcRbRXUgty3suc3RhvNfXju5CGhhEEQDUt4MrX1o5SiWOVnQXUidynspEVpqeY5z/xbLwD1xXBAD16sK+V0I7saXmECHE4R2bkhJmfLSM7kIaEkQRANSfpBsFpXKW1MXCx/YdvO2e/a16+UhOdyENBqIIAOrP1WO50kbWdFdRH4QO4stHa3pF7dsDUQQA9ST2dJ6jtzWTyaC7kPogEPNYXE7y3SK6C2kYEEUAUB80Kt2LZIWDtzmeuh139+TshQFFRbW8E2Pf2PavuJLabdNSIYoAoD48/atU95Z94HD57MI8TW6Gku5CGoC3a8sAALo8Sii1shXSXUV9s7IVPEkqpbuKBgB35gaA+lCQq3FqLqiLllUqxbmoTX/8GalWKx3sPbt1HenbJpgQcvXGwYSkqPe7DD8Xtam4OLeRa/PBfec6OnhRv5WekRwesepl+gOJ2N7BzqMuCiOEiB2ssp4X1lHjlgRRBAB1rqxEW5yncmXX/u2rdTrdjv2zZLLMD98fIxLZPn4Sv+/IAqWqLODdPoSQF2n3r8TuH9x3nlar+f3Uz4eO/zBz8g5CyKucZ5t2fG4ltA4Jnspisi9e3l7rhVE4fPaLhLI6atySIIoAoM7JizQcfp08RiHpwaWnzxLmzQqXShwIIR3a9lCq5NdvHqaiiBAybuQvErEdIaRrpyGnz68tlRdaCaVnI9cxGMwZk7eLrGwIIQwm8/jpFXVRHpvL0ih1Wq2exXorzhusNkQRANS50iIth18nnzYPk2O1Os2yVf0NU3Q6rYD/v1vb8bj/HBW0sXYhhBQV5XDYvOTUW539BlI5RAhhMevwk5ArZMmLNGIbTt29hAVAFAFAndMTPaNu9gqKS/IkYvsp4zaUn8g0Fi1sFocKqqLiXK1WY2vjUicFvYbFYup1uNVnFRBFAFDnrCRstUJbFy0LBZKSUpmNtQuH82+fuUftDJWU1NM94spK1FZS7BJVASdzA0Cds5KwVQpNXbTs09hPp9PeuH3MMEWpquI0AT7fyt7OPfGvaI1GXRclladRa9kcJouNgaIqYK8IAOqcQMSS2nO1Wh2LVctff99t1zPubviZyHWygsxGLs0ysh4lPbj8zczDXC7fxG99HDThwO+L122d4N+hF4PJvHbzcO1WZaAu07o2rpNT2C0MoggA6oPEll2cLbd2qeVn5bHZnIljwiIubPjjzws375xwsPPo4j+Axarik61Du0/Kyoovx+4/c2Gdk4O3p3vrnNzntVsYpSS31KMxty5atjB4dB4A1IeU+OL4KyUuLRzpLqRePbubHjLO0dHN1C4aYK8IAOqJVyur+MumblOt1+sXLutudJZIaF0iL3h9eqvm7w8fuLi2KixTlPz0a1+jszzd2zx/mfT6dGuJ0+wZByprUK3QiKzZyKF/A3tFAFBPrp/Mzcpk2HtW+ryifFmG0ekajZrNNnISGpcrMFwbVHM6na6gMMv4PD2DMIx8VDKZLGupU2UNZv2d0y5Q2MJfUlsVWjBEEQDUn/VfpbYK9mLU0UVG5kRRospOzv50gSfdhTQMOJkbAOrP+wPtS169FbcHVeQXfzDQnu4qGgxEEQDUn7ZdrdkMdXGOhT9QTvZS5uLJ8mxhRXchDQaiCADqVc+xzgVphaUyi71fdd6LQg5THfCJHd2FNCQYKwIAGhxelWblKBXZWdrD9ArSCyUS7YdDHOgupIFBFAEAPU5uzmDw+BJnKd2F1Jrcx7n2Tsxug5FDbwxRBAC0uROZ/2dskYO3jcSplu/CUM8K0osyk/PfH+jQqhNO3a4ORBEA0KkoX30tPK+0WMfi80X2Qp6wId3EuqxIWZonl+fLPZoJAvvYcXgYfa8mRBEA0C/7pSIlviT1zxImk8kWcBgsFofH5vJZOh3dlf1/DBZDJVdrVFom0RXnlglELJ92Vi0CJBLbhpSgZghRBABmJD9LmZuuKi3SFOZptVq9Ul4nTzmqNqGYw2TppbZsKynLyZOPZ7PWFkQRAADQDEc2AQCAZogiAACgGaIIAABohigCAACaIYoAAIBmiCIAAKDZ/wGVMUCTptIS3gAAAABJRU5ErkJggg==", "text/plain": ["<ai_data_science_team.agents.data_visualization_agent.DataVisualizationAgent object at 0x7fc1f8b228f0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data_visualization_agent = DataVisualizationAgent(\n", "    model = llm,\n", "    log = LOG,\n", "    log_path = LOG_PATH\n", ")\n", "\n", "data_visualization_agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This creates an `app`, which is a langgraph agent with the main inputs:\n", "\n", "- **user_instructions**: The data cleaning agent will use these comments to modify the \"standard recipe\" \n", "  - Standard Recipe: The standard cleaning recipe which includes removing columns with more than 40% missing values, imputing missing values using mean (numeric) or mode (categorical), removing duplicate rows, and removing outliers. \n", "- **data_raw**: The raw data to be cleaned\n", "- **max_retries**: Used to limit the number of attempts to fix the python code generated by the agent. Set this to 3 to limit to 3 attempts. \n", "- **retry_count**: Set this to 0. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---DATA VISUALIZATION AGENT----\n", "    * CREATE CHART GENERATOR INSTRUCTIONS\n", "    * CREATE DATA VISUALIZATION CODE\n", "      File saved to: /Users/<USER>/Desktop/course_code/ai-data-science-team/logs/data_visualization.py\n", "    * EXECUTING AGENT CODE\n", "    * EXPLAIN AGENT CODE\n"]}], "source": ["\n", "data_visualization_agent.invoke_agent(\n", "    data_raw=df, \n", "    user_instructions=\"Make a boxplot of the monthly charges vs Churn. Churn should be on X axis and distribution on Y axis.\",\n", "    max_retries=3,\n", "    retry_count=0,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Response <a id=\"response\"></a>\n", "\n", "The response produced contains everything we need to understand the data visualization decisions made and get the plotly graph. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['messages',\n", " 'user_instructions',\n", " 'recommended_steps',\n", " 'data_raw',\n", " 'plotly_graph',\n", " 'all_datasets_summary',\n", " 'data_visualization_function',\n", " 'data_visualization_function_path',\n", " 'data_visualization_function_name',\n", " 'data_visualization_error',\n", " 'max_retries',\n", " 'retry_count']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["response = data_visualization_agent.get_response()\n", "\n", "list(response.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The data visualization <a id=\"the-data-visualization\"></a>"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "hovertemplate": "Churn=%{x}<br>MonthlyCharges=%{y}<extra></extra>", "legendgroup": "", "line": {"width": 0.65}, "marker": {"color": "#3381ff"}, "name": "", "notched": false, "offsetgroup": "", "orientation": "v", "showlegend": false, "type": "box", "x": ["No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "No", "Yes", "No", "Yes", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "Yes", "No", "No", "No", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "Yes", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "Yes", "Yes", "Yes", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No", "No", "Yes", "No", "No", "No", "No", "Yes", "No", "No", "No", "No", "No", "Yes", "No", "Yes", "No", "No", "No", "No", "No", "No", "Yes", "No"], "x0": " ", "xaxis": "x", "y": [29.85, 56.95, 53.85, 42.3, 70.7, 99.65, 89.1, 29.75, 104.8, 56.15, 49.95, 18.95, 100.35, 103.7, 105.5, 113.25, 20.65, 106.7, 55.2, 90.05, 39.65, 19.8, 20.15, 59.9, 59.6, 55.3, 99.35, 30.2, 90.25, 64.7, 96.35, 95.5, 66.15, 20.2, 45.25, 99.9, 69.7, 74.8, 106.35, 97.85, 49.55, 69.2, 20.75, 79.85, 76.2, 84.5, 49.25, 80.65, 79.75, 64.15, 90.25, 99.1, 69.5, 80.65, 74.85, 95.45, 99.65, 108.45, 24.95, 107.5, 100.5, 89.9, 42.1, 54.4, 94.4, 75.3, 78.9, 79.2, 20.15, 79.85, 49.05, 20.4, 111.6, 24.25, 64.5, 110.5, 55.65, 54.65, 74.75, 25.9, 79.35, 50.55, 75.15, 103.8, 20.15, 99.3, 62.15, 20.65, 19.95, 33.75, 82.05, 74.7, 84, 111.05, 100.9, 78.95, 66.85, 21.05, 21, 98.5, 20.2, 19.45, 95, 45.55, 110, 24.3, 104.15, 30.15, 94.35, 19.4, 96.75, 57.95, 91.65, 76.5, 54.6, 89.85, 31.05, 100.25, 20.65, 85.2, 99.8, 20.7, 74.4, 50.7, 20.85, 88.95, 78.05, 23.55, 19.75, 56.45, 85.95, 58.6, 50.55, 35.45, 44.35, 25.7, 75, 20.2, 19.6, 70.45, 88.05, 71.15, 101.05, 84.3, 23.95, 99.05, 19.6, 45.65, 64.5, 69.5, 68.55, 95, 108.15, 86.1, 19.7, 80.9, 84.15, 20.15, 64.25, 25.7, 56, 82.4, 69.7, 73.9, 20.6, 19.9, 70.9, 89.05, 45.3, 20.4, 84.25, 104.4, 81.95, 94.85, 20.55, 24.7, 74.45, 76.45, 105.35, 20.55, 29.95, 45.3, 84.5, 74.75, 79.25, 24.8, 51.8, 30.4, 19.65, 56.6, 71.9, 91, 19.75, 109.7, 19.3, 96.55, 24.1, 111.35, 112.25, 20.75, 101.9, 80.05, 105.55, 78.3, 68.85, 79.95, 55.45, 79.9, 106.6, 102.45, 46, 25.25, 19.75, 20, 86.8, 58.75, 45.25, 56.6, 84.2, 80, 70.15, 24.75, 20.2, 50.05, 19.35, 50.6, 81.15, 55.2, 89.9, 85.3, 108, 93.5, 84.6, 20.25, 25.15, 54.4, 29.6, 73.15, 95, 19.75, 86.6, 109.2, 74.7, 94.4, 54.8, 75.35, 65, 74.4, 48.55, 99, 93.5, 70.4, 40.2, 83.7, 19.85, 59.55, 115.1, 114.35, 44.6, 45, 41.15, 106.9, 89.85, 49.85, 113.3, 88.1, 24.9, 105, 19.35, 24.25, 94.45, 59.75, 24.8, 107.05, 70.6, 85.4, 105.05, 64.95, 55, 50.55, 55.15, 51.2, 25.4, 54.45, 95.15, 76, 44.35, 70, 74.5, 44.85, 76.1, 61.2, 86.8, 89.35, 19.7, 20.25, 76.05, 100.8, 74.55, 73.6, 64.9, 95.45, 90.4, 60.3, 81.85, 24.8, 74.9, 75.55, 101.15, 78.75, 19.25, 89.05, 115.05, 69.35, 80.6, 110.05, 19.9, 80.3, 93.15, 91.5, 82.45, 60, 44.8, 48.6, 60.05, 102.7, 82.9, 70.35, 35.9, 82.65, 19.85, 19.2, 94.9, 73.85, 80.6, 75.8, 104.6, 88.15, 94.8, 103.4, 54.65, 85.75, 67.45, 20.5, 20.25, 72.1, 90.4, 19.45, 44.95, 97, 62.8, 44.6, 89.15, 84.8, 41.9, 80.25, 54.1, 105.25, 30.75, 97.1, 20.2, 98.8, 50.3, 20.55, 75.9, 96.5, 59.95, 19.15, 98.65, 112.6, 20.6, 85.65, 35.75, 99.75, 96.1, 85.1, 25.35, 104.95, 89.65, 86.75, 86.2, 50.65, 64.8, 90.85, 108.1, 19.95, 85.45, 54.75, 90.4, 44, 95.6, 84.8, 44.3, 19.9, 95.05, 90.05, 109.9, 73.95, 54.6, 20.05, 19.75, 20.05, 99.45, 55.9, 19.7, 19.8, 95.4, 93.95, 19.9, 19.6, 81.35, 24.45, 74.95, 87.35, 70.65, 73.25, 98.7, 24.8, 83.3, 75.3, 24.3, 69.85, 100.55, 25.7, 40.7, 51.65, 105.1, 85.95, 75.6, 58.25, 19.4, 65.2, 53.45, 45.4, 19.75, 44.45, 20.85, 114.05, 89.85, 55.05, 112.95, 101.55, 114.65, 64.8, 80.4, 105.9, 69.55, 25.05, 94.75, 105.5, 24.7, 69.75, 60.2, 81.05, 24.4, 104.15, 92.9, 80.8, 20, 75.1, 19.65, 69.45, 101.15, 99.8, 116.05, 40.05, 102.1, 89.7, 19.9, 55.95, 20.65, 55, 70.05, 53.6, 74.7, 80.25, 76.05, 75.7, 96.1, 69, 19.65, 45.3, 81.45, 108.5, 83.55, 84.5, 100.15, 88.6, 52.55, 74.35, 104.8, 59, 74.4, 64.05, 20.4, 43.75, 60.9, 19.8, 28.45, 99.7, 116.25, 80.7, 65.2, 84.05, 79.45, 94.1, 78, 94.2, 80.5, 19.85, 94.3, 106.45, 74.35, 105.45, 95, 104.8, 54.3, 70.05, 75.2, 20.05, 105.4, 51.6, 85.5, 75.6, 100.05, 91.25, 115.75, 94.7, 19.6, 99.9, 21.1, 20.05, 79.95, 107.15, 85, 89.55, 81.55, 58.45, 95.65, 80.6, 113.1, 58.95, 19.55, 86.05, 45.55, 78.95, 86.3, 105.05, 101.9, 19.75, 110.3, 115.6, 19.35, 25.6, 80.35, 68.75, 19.9, 70.6, 70.2, 49.3, 107.25, 23.6, 69.7, 99.5, 64.3, 70.85, 101.9, 73.5, 100.25, 40.4, 19.25, 59.6, 64.9, 100.3, 110.85, 81.05, 98.05, 70.5, 94.55, 19.65, 19, 75.3, 89.2, 19, 20, 85.7, 63.25, 20.1, 99.15, 90.4, 111.9, 24.9, 83.5, 84.3, 45.6, 61.65, 54.85, 65.55, 90.35, 20.4, 74.55, 19.95, 74.25, 108.65, 109.55, 86.65, 81, 47.85, 114.55, 105.25, 29.95, 65, 20.55, 109.8, 69.5, 48.85, 25.25, 102.85, 87.55, 78.55, 34.55, 92.05, 85.05, 19.7, 20, 95.15, 84.25, 104.6, 111.65, 90.05, 110.75, 55, 89.85, 20.35, 54.55, 105.5, 99.45, 70.9, 104.55, 85.25, 25.4, 56.15, 89.55, 89.85, 25.25, 94.55, 45.7, 69.65, 89.5, 70, 69.55, 74.6, 20.1, 24.8, 19.65, 95.1, 88.85, 78.8, 19.85, 20.35, 24.25, 45.25, 20.05, 69.55, 19.5, 74.75, 69.65, 30.2, 45.65, 57.8, 19.85, 25.55, 75.05, 24.85, 49.15, 110.35, 24.55, 34.7, 107.95, 81.4, 80, 73.8, 64.4, 103.75, 71.1, 49.9, 24.6, 49.25, 30.1, 83.4, 20.45, 75.25, 20.55, 75.1, 20.05, 20.65, 85.15, 50.15, 84.95, 66.5, 63.3, 83.15, 84.9, 20.55, 49.25, 79.85, 59.6, 104.65, 75.3, 80.1, 19.55, 81, 24.7, 86, 25.4, 89.15, 58.25, 85.65, 50.35, 80.35, 20.2, 20.55, 85.95, 45.35, 94.5, 21.25, 26.25, 80.85, 91.7, 74.2, 87.25, 20.35, 75.5, 79.05, 90.15, 50.6, 110.45, 101, 79.35, 89.85, 65, 80.45, 98.55, 24.1, 44.05, 110.8, 114.95, 75.05, 19.25, 90.05, 56.7, 80.15, 71.35, 20.25, 90.35, 98.55, 19.7, 19.85, 85.9, 90.35, 20.8, 89.25, 70.3, 66.85, 19.9, 35.8, 78.85, 20.4, 74.25, 64.8, 20.45, 93.35, 19.9, 88.9, 95.8, 110.65, 40.3, 82, 107, 45.35, 73.35, 44.8, 54.75, 52.2, 40.6, 110, 55.3, 60.85, 78.4, 69.65, 59.85, 76.9, 19.85, 67.65, 45, 64.2, 81.7, 25.55, 20, 96.75, 75.65, 98.5, 23.8, 64.2, 85.35, 76.8, 55.2, 108.55, 101.3, 69.55, 103.25, 104, 25.25, 30.4, 20.05, 84.6, 86.2, 103.7, 111.2, 88, 106.35, 79.15, 103.1, 63.95, 25.8, 89.45, 95.6, 25.55, 90.95, 44.85, 108.55, 25.05, 74.1, 88.8, 78.85, 93.25, 71.4, 44.4, 79.2, 20.4, 100, 105, 19.8, 30.85, 89.9, 20.55, 84.85, 33.15, 92, 89.8, 115.8, 85.15, 24.85, 64.35, 20.5, 100.15, 86.05, 50.8, 89, 64.8, 19.8, 93.4, 73.65, 95.1, 94.65, 80.6, 39, 20.5, 85.55, 26.4, 98.2, 97.55, 19.95, 50.8, 99.7, 34.8, 105.1, 60.15, 64.75, 54.65, 110.1, 19.3, 83.9, 111.25, 35.8, 20.05, 84.35, 110.5, 91.2, 100.55, 89.3, 103.85, 81.1, 24.6, 81.2, 94.3, 116.1, 105.55, 98.9, 94.4, 19.5, 98.3, 93.85, 105.6, 81.35, 100.5, 56.4, 65.35, 19.95, 111.25, 72.85, 89, 106.1, 20.05, 25.2, 73.55, 75.4, 65.55, 80.7, 104.55, 24.15, 20.45, 75.4, 79.7, 81.7, 76.3, 79.4, 81.15, 103.75, 86.45, 75.1, 80.6, 19.3, 84.6, 33.6, 83.25, 80.85, 79.05, 108.05, 19.9, 21.05, 30.15, 79.85, 65.5, 104.1, 74.4, 20.5, 91.35, 99.05, 20.5, 44.95, 75.6, 55.1, 58.95, 95.1, 44.7, 25.45, 56.75, 81.75, 86.1, 29.8, 20.5, 60.9, 73.25, 45.7, 100.3, 19.25, 20.85, 77.35, 96, 90.55, 93.85, 70.1, 30.35, 75.95, 108.05, 69.9, 75.25, 103.75, 54.95, 19.5, 19.6, 47.85, 86.6, 23.75, 80.6, 43.8, 19.75, 19.15, 19.6, 80.3, 24.35, 25.25, 26.1, 20, 85.3, 70, 94.3, 20.7, 70.3, 95.35, 75.5, 69.55, 19.85, 20, 95.85, 90.1, 68.95, 99.55, 20.75, 50.15, 58.65, 95.9, 49.5, 57.45, 53.65, 80.1, 24.4, 40.05, 19.5, 51.05, 54.35, 84.7, 86.1, 70.35, 110, 100.6, 94.9, 83.75, 88.3, 69.75, 71.6, 92.1, 23.65, 81.85, 25.1, 114.7, 49.15, 80.9, 79.45, 90.45, 19.3, 70.2, 69.75, 54.25, 99.3, 74, 50.25, 19.8, 19.65, 43.65, 35.5, 80.75, 39.5, 97.1, 19.55, 80, 84.7, 89.55, 90.6, 20.05, 112.4, 50.2, 62.25, 55.7, 90.05, 19.65, 89.25, 99.05, 54, 69.75, 49.05, 56.75, 98.05, 21.1, 96.65, 24.5, 114.5, 79.2, 69.55, 20.05, 98.85, 25.75, 80.95, 19.6, 74.3, 89.7, 87.65, 100.45, 74.75, 107.45, 75.35, 64.95, 100.45, 68.5, 80.55, 81.25, 90.4, 89.55, 55.7, 24.8, 20, 56.15, 105.2, 19.55, 79.75, 97.45, 24.25, 24.6, 50.15, 39.6, 94.4, 89.85, 78.95, 98.85, 53.85, 24.25, 89.45, 105.25, 59.5, 70.55, 82.5, 44.85, 61.6, 49.05, 105.65, 74.65, 66.25, 19.4, 86.05, 19.15, 64.7, 104.05, 19.25, 81.95, 114.65, 20, 19.8, 65.15, 19.65, 88.95, 20.2, 75.2, 56.8, 35.55, 75.5, 35.6, 60.25, 95.15, 96.65, 40.35, 18.85, 54.85, 64.3, 24.65, 76.1, 18.7, 97.95, 94.1, 80.4, 95.1, 31.35, 72.35, 89.75, 82.7, 19.9, 53.8, 51.55, 19.65, 44.05, 114, 94.4, 100.4, 19.85, 54.25, 80, 109.9, 79.2, 101.35, 94.3, 49.8, 60.05, 53.75, 93.45, 87.9, 60.15, 61.05, 104.05, 99.25, 85.7, 104.85, 69.15, 90.45, 74.45, 50.45, 60, 85.25, 19.45, 20.75, 78.9, 104.5, 49.4, 94.25, 25, 25.55, 74.9, 70.15, 69.4, 80.25, 93.15, 69, 66.35, 69.55, 20.2, 86, 80.3, 20.4, 23.75, 90.55, 70.45, 65.75, 24.6, 69.25, 75.9, 45.85, 49.95, 24.65, 90.4, 100.85, 75.35, 87.2, 64.4, 78.3, 24.7, 105.85, 98.3, 76.95, 19.45, 96.15, 58.7, 20.15, 64.5, 28.5, 45.3, 19.4, 90.45, 105.15, 83.15, 90.15, 45.05, 103.2, 75.8, 19.45, 79.3, 88.8, 30.9, 85.9, 34.2, 20.15, 95.25, 50.3, 80.15, 51.25, 89.6, 95.2, 94.8, 80.25, 76.1, 110.15, 115.55, 24.65, 53.6, 19.45, 88.2, 101.15, 56.8, 99.4, 20.1, 60.7, 20.95, 114.85, 19.25, 62.8, 105.5, 19.85, 89.5, 74.1, 107.5, 19.55, 68.8, 84.45, 75, 84.5, 111.2, 44.75, 80.6, 80.7, 75.6, 57.6, 44.05, 110.6, 58.2, 81, 19.7, 85.6, 59.55, 115.55, 75.55, 86.6, 85.2, 97.65, 45.1, 70.95, 109.55, 89.55, 20.9, 19.95, 24.6, 66.7, 19.45, 94.8, 65.85, 19.95, 24.65, 20.35, 69.25, 51.25, 99.5, 54.25, 19.4, 56.25, 25.15, 23.95, 35.4, 25.2, 45, 75.35, 20.4, 20.15, 105, 56.05, 54.7, 20, 73.05, 20.5, 100.75, 87.25, 19.95, 79.95, 49.65, 65.65, 20.45, 60.95, 20.35, 88.35, 19.5, 75.2, 111.45, 70.15, 94.75, 95.05, 78.45, 70.2, 92, 85.5, 41.05, 85.6, 82.15, 84.4, 60.9, 20.25, 79.2, 95.3, 19.85, 84.35, 19.85, 70, 82.3, 66.8, 44.6, 98.45, 70.7, 24.95, 49.95, 69.25, 102.5, 86.55, 24.3, 58.35, 94.25, 68.75, 85.8, 20.1, 20.35, 110.8, 73, 100.05, 82.85, 84.35, 19.55, 19.95, 99.8, 35, 66.25, 23.3, 76, 25.3, 44.55, 104.1, 92.55, 93.85, 101.45, 84.3, 94.55, 95.5, 100.3, 55.5, 49.85, 89.55, 19.15, 99.8, 84.4, 113.05, 101.1, 19.95, 74.15, 92, 73.85, 50.45, 24.45, 24.8, 64.85, 20.75, 68.95, 99.95, 109.4, 91.4, 49, 50.25, 75.55, 19.9, 97.8, 100.3, 55.8, 111.15, 98.55, 50.05, 80.8, 20.85, 19.5, 19.35, 69.5, 48.8, 94.5, 20.65, 106.05, 100, 108.3, 20.55, 99.65, 85.3, 95.9, 20, 70.4, 64.95, 74.6, 49.2, 73.75, 92.3, 98.8, 19.2, 88.65, 74.4, 98.75, 95.95, 105.4, 20.25, 106, 104.7, 49.05, 35.55, 65.1, 96.85, 69.75, 99.2, 96.7, 55.05, 106.8, 51.25, 57.75, 70.85, 19.55, 88.2, 79.5, 19.75, 98.15, 20.25, 79.15, 75.65, 94.25, 40.2, 19.95, 55.35, 102.15, 71.1, 74.7, 54.1, 19.65, 88.45, 76.65, 80.4, 19.25, 84.8, 25.8, 19.5, 68.6, 92.6, 100.55, 20.55, 42.6, 19.6, 67.45, 68.85, 43.55, 109.85, 20.65, 95.4, 21, 56.2, 18.4, 90, 25.75, 19.6, 75.35, 19.8, 64.2, 75.75, 78.95, 100.85, 50.3, 80.3, 19.85, 21.1, 69.95, 50, 104.75, 19.85, 107.5, 85.9, 45.85, 80.8, 25.25, 80.55, 81.5, 20.9, 106.1, 91.7, 67.25, 95.6, 20.35, 45.05, 74.95, 34.65, 69.35, 95.35, 81.55, 75.4, 67.8, 111.4, 46.3, 20.4, 20.05, 45, 96.1, 19.65, 99.5, 60.65, 98.6, 59.5, 80.45, 71.7, 36, 65.2, 48.95, 53.5, 80.45, 109.05, 26.3, 106.8, 64.95, 19.35, 21.1, 77.95, 18.85, 26, 74.7, 70.35, 96.9, 19.55, 80.4, 88.8, 94.65, 90.25, 64.65, 95.75, 19.55, 104.1, 89.05, 20.1, 111.55, 60.5, 90.95, 87.4, 19.7, 50.95, 20.05, 19.4, 59.45, 94.75, 81.5, 29.05, 86.45, 70.6, 97.2, 98.25, 75.75, 59.2, 75.9, 90.05, 70.95, 102.6, 85.35, 106.1, 43.8, 59, 69.95, 24.35, 29.45, 84.4, 45.05, 20.65, 87.1, 19.85, 90.35, 109.8, 84.65, 65.5, 79.5, 80.95, 56.15, 85.8, 79.1, 34.4, 20.75, 18.8, 44.3, 90.8, 25.6, 105.95, 70.8, 25.4, 108.8, 69.75, 94.65, 96.05, 76.85, 20.25, 24.8, 115.65, 74.6, 50.15, 103.15, 72.1, 113.6, 25.1, 78.9, 80.15, 25.4, 105.4, 45.75, 24.45, 25, 85.25, 19.6, 50.15, 70.55, 60.05, 26.4, 20.15, 58.85, 97.55, 19.65, 25.25, 114.45, 34.7, 70.7, 85.3, 75.55, 84.8, 20.65, 20.45, 102.45, 104.4, 35.65, 99.75, 90.45, 97.65, 73.85, 74.4, 69.1, 82.75, 24.4, 55.25, 61.35, 76.75, 19.4, 54.75, 19.7, 19.9, 107.95, 83.8, 74.25, 56.4, 20.1, 94.9, 94.2, 49.9, 71.05, 81.65, 89.45, 59.85, 69.6, 99, 19.05, 45.4, 114.45, 19.5, 44.25, 90.55, 69.9, 20.4, 71.4, 87.15, 24.85, 104.45, 19.8, 116.45, 84.75, 20.05, 110.75, 89.7, 89.95, 48.7, 96.6, 74.3, 54.3, 74.85, 79.95, 20.05, 19.4, 54.9, 24.45, 89.65, 45.4, 75.7, 110.65, 20.55, 115.15, 58.55, 93.25, 113.2, 90.5, 79, 19.35, 48.75, 109.05, 25, 54.9, 24.75, 91.15, 20.15, 104.35, 66.05, 71.65, 20.35, 92.2, 84.25, 105.2, 19.6, 30.4, 78.1, 61.5, 69.4, 24.75, 91.05, 89.65, 73.65, 19.4, 26.2, 98.7, 43.85, 69.7, 38.55, 53.1, 20.65, 64.45, 25.1, 76.35, 79.15, 85, 95.15, 79.35, 96.65, 75.5, 19.7, 20.5, 19.2, 98.35, 74.35, 51.35, 45.65, 85.3, 86.55, 73.85, 20.3, 54.2, 90.65, 50.9, 25.05, 74.85, 20.5, 63.55, 44.85, 47.95, 45.1, 45, 96, 20.05, 90.05, 25.3, 108.65, 24.3, 75.95, 19.7, 66.4, 35.75, 18.8, 19.4, 19.3, 45.55, 67.45, 35.1, 46.2, 45.15, 43.3, 20.1, 57.15, 58.9, 73.2, 85.35, 19.45, 45.95, 50.5, 25.1, 60.7, 99, 104.4, 83.75, 44.05, 24.1, 45.55, 93.8, 19.7, 70.65, 86.45, 114.1, 95.2, 88.55, 20.75, 70.05, 86, 44.65, 60.2, 100.5, 55.45, 70.3, 60.4, 72.65, 55.8, 31.1, 21, 45.1, 50.95, 69.1, 43.95, 86.5, 69.95, 50.4, 78.95, 90.95, 19.9, 20.15, 90.6, 92, 94.45, 24.85, 36, 78.5, 19.95, 20.65, 30.5, 106.1, 20.5, 95.5, 64.6, 51.1, 84.8, 89.1, 54.95, 50.9, 20.45, 85.95, 60.35, 19.8, 85.35, 72.1, 99.8, 107.35, 19.55, 81.05, 20.5, 111.8, 20.2, 19.7, 79.1, 19.85, 60.5, 19.55, 20.9, 21.05, 71.5, 54.65, 19.2, 49.8, 25.5, 20.5, 90.4, 90.25, 80.75, 104.6, 91.85, 50.2, 95.5, 75.35, 75.45, 95.4, 101.3, 53.1, 84.85, 34.25, 88.6, 60.15, 99.95, 70.7, 54.8, 49.55, 54.8, 78.6, 100.3, 53.6, 81.1, 19.35, 85.6, 80.8, 74.95, 19.6, 93.55, 90.7, 69.75, 20, 95.25, 102.1, 19.95, 80.85, 90.9, 29.2, 93.3, 89.15, 108.85, 46.35, 84.75, 78.75, 83.55, 45.7, 19.6, 69.95, 67.85, 105.65, 44.6, 74.95, 75.5, 20.15, 45.2, 95.25, 89.85, 100.45, 47.15, 80.2, 87.1, 79.25, 75.9, 85.7, 98.75, 20.1, 61.8, 49.9, 86.45, 20.4, 45.3, 104.1, 75.4, 108.15, 86.25, 81, 95.7, 116.85, 105.75, 20.15, 19.6, 90.6, 60.95, 25.05, 88.15, 20.2, 60.3, 63.95, 74.3, 70.6, 90.8, 79.35, 90.55, 19.45, 64.45, 69.65, 19.5, 110.5, 24.7, 77.4, 96.8, 85.4, 47.6, 19.4, 103.85, 83.35, 49.4, 108.45, 81, 79.2, 86.65, 92.95, 90.35, 48.7, 25.15, 76.4, 19.55, 85.35, 24.8, 103.15, 100.75, 95.6, 59.75, 94.1, 19.35, 19.9, 108.15, 101.05, 59.1, 71.35, 55.85, 106.05, 84.1, 75.3, 24.7, 20.15, 69.75, 93.2, 80.85, 33.65, 55.8, 39.7, 29.5, 20.15, 79.55, 24.8, 19.65, 79.95, 19.3, 94.05, 90.75, 78.85, 99.5, 99.2, 80.55, 70.2, 85.2, 75.25, 59.45, 93.35, 44.95, 26.1, 20.2, 21.25, 59.4, 95, 61.9, 118.65, 54.35, 64.45, 80.15, 20.2, 21, 20.45, 75.85, 80.45, 24.95, 75.5, 44.45, 42.35, 74.55, 75.3, 94.8, 48.15, 19.65, 70.55, 20.15, 106.6, 91, 25.4, 69.95, 66.85, 86.15, 20.15, 64.85, 74.85, 50.5, 72.9, 115.05, 19, 19.55, 101.1, 84.1, 24.15, 50.1, 74.6, 19.75, 85, 80.55, 106.8, 84.5, 25.05, 83.7, 75.8, 96.6, 98.5, 101.1, 20.2, 94.05, 95.25, 74.4, 81, 60.25, 60.85, 43.95, 86.05, 20.25, 85.15, 19.4, 102.65, 19.9, 19.55, 95.5, 84.15, 103.2, 50.2, 88.55, 54.75, 19.95, 116.25, 31.2, 24.45, 84.2, 91.3, 85.65, 21.2, 79.5, 25.55, 20.2, 63.85, 61.95, 25.75, 58.2, 85.85, 70.1, 104.9, 111.3, 99.85, 95.25, 86.25, 100.8, 19.55, 104, 104.4, 19.5, 25.25, 86.3, 49.85, 108.95, 89.9, 82, 89.95, 79.35, 64.05, 101.15, 89.95, 76.45, 39.1, 34.6, 19.55, 104.45, 70.5, 20.35, 70, 19.45, 69.9, 59.7, 78.35, 71.45, 45.85, 95.85, 35.7, 89.55, 24.95, 24.85, 100.8, 64.4, 105.35, 102.45, 19.65, 54.45, 70.5, 20.1, 69.35, 19.8, 74.4, 93.05, 51.2, 65.6, 80.55, 52.7, 20.85, 80.1, 52.15, 80.2, 98.15, 114.95, 112.95, 104.45, 113.65, 20.6, 70.9, 86.85, 91.55, 49.85, 19.8, 99.85, 74.5, 104.15, 109.15, 48.2, 25.1, 100.15, 65.2, 99.5, 71.55, 55.9, 93.9, 64.4, 108.4, 85.3, 107.45, 48.75, 85.65, 91.3, 85.95, 106.7, 25.15, 45.2, 110.35, 79.2, 55.5, 103.25, 90.25, 91.25, 47.8, 100.9, 97.7, 69.85, 65.6, 104.65, 90.45, 63.7, 104.5, 20.1, 104.3, 93.25, 73.45, 20.7, 25.25, 100.5, 90.6, 89.4, 95.45, 20.45, 98.6, 83.05, 19.95, 109.15, 85.7, 102.05, 94.7, 64.4, 26.8, 66.05, 65.2, 85.05, 55.8, 70.4, 104.75, 19.95, 94.25, 45, 114.9, 106.4, 46.1, 39.7, 20.05, 95.75, 24.4, 33.6, 90.45, 84, 67.4, 19.7, 80.35, 19.6, 54.2, 45.2, 75.1, 19.7, 72.75, 20.05, 45.95, 39.2, 44.75, 82.65, 93.9, 70.15, 85.55, 117.15, 99.25, 112.55, 25.7, 90.3, 49.4, 19.4, 109.7, 61.25, 55.3, 70.3, 106.35, 103.75, 19.5, 39.5, 26.05, 91.05, 29.65, 50.2, 105.3, 55.45, 85.45, 19.8, 59.25, 90.7, 103.7, 79.05, 90.7, 95, 88.35, 30.25, 49.85, 93, 54.55, 19.7, 84.8, 94.45, 94.2, 96.25, 70.7, 20.85, 60, 80.45, 84.95, 33.55, 49.65, 20.2, 94.55, 100.5, 35.75, 86.45, 53.8, 38.55, 39.9, 70.05, 20.1, 112.95, 20.3, 35.65, 35.9, 99.25, 82.95, 55.65, 24.45, 25.2, 50.8, 19.65, 59.8, 73.55, 61.4, 103.35, 19.9, 19.45, 81.5, 84.8, 109.55, 99.95, 74.4, 90, 74.9, 104.85, 59.65, 110.45, 106.1, 74.2, 74.45, 24.55, 89.35, 24.55, 90.65, 105.05, 20.45, 19.55, 19.7, 70.45, 85.65, 77.15, 35.25, 20.55, 97.95, 48.55, 20, 25.25, 98.4, 70.9, 19.85, 106.35, 99.5, 84.7, 86.05, 44.55, 75.85, 93.85, 25, 45, 100.7, 20.5, 80.45, 90.45, 60.45, 55.25, 78.45, 100.55, 20.35, 54.45, 90.75, 75.35, 20.25, 20.05, 19.6, 53.8, 70.2, 75.5, 20.35, 26.05, 20.6, 75.7, 20.1, 24.3, 24.5, 110.5, 25.25, 74.25, 90.1, 68.75, 19.2, 89.7, 115.1, 96.4, 69.5, 99.65, 91.45, 84.75, 85.25, 78.75, 20.25, 19.9, 97.75, 19.4, 83.3, 80.1, 62.7, 100.4, 24.45, 101.1, 50.9, 107.2, 92.2, 25.3, 113.4, 40.55, 26, 111.95, 53.8, 72.1, 98.15, 78.85, 70.75, 76.15, 39.1, 69.95, 20.05, 20.05, 19.45, 26.9, 19.2, 50, 60, 84.55, 45.45, 20.05, 115.55, 93.7, 99, 50.55, 105.95, 82, 25, 91.55, 95.75, 19.35, 24.85, 94.05, 100.4, 25, 54.75, 95.65, 19.25, 108.25, 94.6, 98.9, 20.15, 101.3, 20, 105.3, 69.85, 65.25, 19.8, 19.6, 20.05, 49.4, 76.05, 88.4, 100.6, 19.45, 20.3, 107.65, 80.45, 58.85, 109.6, 75.15, 73, 70.1, 98.65, 111.45, 114.9, 100.55, 20.4, 104.35, 69.75, 34.5, 105.55, 30.1, 70.3, 80.45, 80.2, 94.35, 91.35, 44.6, 19.6, 19.9, 110.45, 68.35, 79.1, 51, 80.55, 66.7, 86.4, 50.05, 25.7, 83.4, 70.7, 84.65, 99.25, 64.75, 100.15, 84.8, 25.25, 113, 40.65, 105, 54.45, 94.95, 59.9, 85.3, 83.35, 33.5, 19.8, 81.8, 20, 59.6, 25, 84.35, 90.35, 55.55, 75.35, 90.75, 89.6, 59.3, 66.1, 18.8, 86.45, 52.1, 47.4, 49.25, 109.15, 94.95, 93.55, 79.5, 115.05, 19.75, 95.15, 95.15, 105.4, 20.1, 101.35, 20.05, 20.7, 20.35, 70.05, 19.7, 74.65, 85.45, 40.4, 50.4, 79.65, 105.2, 100.65, 79.85, 91, 78.75, 116.75, 80.45, 59.1, 49.8, 19.3, 19.65, 81.4, 38.9, 87.95, 19.85, 96.35, 24.15, 19.1, 44, 50.1, 60.6, 25.65, 76.4, 98.7, 100.8, 53.95, 20.4, 90.1, 29.35, 20.45, 95.1, 25.25, 44.9, 92.65, 43.7, 72.6, 51.55, 79.25, 18.95, 20.5, 19.95, 24.5, 20.6, 94.85, 61.05, 85.7, 106.65, 108.25, 20.4, 55.3, 20.25, 72.95, 89.45, 104.65, 75.2, 101.15, 44.4, 89.5, 68.75, 111.05, 99, 86.05, 21, 19.4, 44.55, 77.2, 19.45, 24.85, 35.4, 95.65, 41.35, 19.6, 20.95, 84.45, 20.25, 19.65, 20.65, 34.7, 99.3, 81.05, 67.6, 70.15, 115, 84.8, 19.7, 19.75, 92.55, 63.15, 74, 29.1, 50.05, 60.05, 74.3, 20, 74.65, 85.35, 74.3, 44.4, 85.4, 94.1, 98.1, 108.9, 56.2, 26.1, 85.45, 88.95, 109.65, 74.35, 48.85, 80.1, 56.05, 74.55, 89.8, 100.95, 94.9, 19.1, 20.35, 106.05, 104.9, 19.65, 24.1, 59.85, 86.1, 19.45, 97.1, 36.65, 103.9, 19.75, 104.05, 24.55, 48.7, 88.35, 109.55, 20.65, 94.65, 55.2, 24.05, 74.4, 79.9, 20.45, 19.25, 26.35, 43.8, 50.15, 20.45, 69.7, 61.4, 98.1, 70.75, 61.15, 20.25, 63.85, 98.7, 20.5, 20, 19.3, 84.4, 25.1, 48.25, 19.85, 99.6, 94.2, 62.15, 79.3, 56.25, 20.3, 99, 90.6, 85.9, 79.2, 70.35, 19.35, 50.15, 63.8, 20.55, 88.55, 101.4, 81.95, 69.35, 44.6, 63.75, 109.25, 84.6, 20.45, 85.75, 91.1, 107.95, 86.1, 22.95, 94.7, 19.45, 85.1, 19.7, 99.15, 87, 102.95, 79.95, 64, 64.9, 25.75, 90.15, 116.1, 104.95, 45.05, 71, 50, 70.55, 79.7, 20.45, 59, 60.35, 19.85, 19.95, 26.45, 63.4, 53.95, 69.25, 95.1, 74.1, 35.5, 70.95, 79.2, 48.8, 89, 99.4, 55.45, 25.4, 73.5, 93.5, 63.9, 64.85, 63.8, 44.45, 19.95, 43.35, 49.65, 85.1, 95.5, 92.35, 89.8, 74.55, 103.05, 116, 69.9, 95.1, 40.25, 25.75, 105.35, 113.6, 24, 19.4, 86.1, 102.65, 92.85, 97.75, 83.8, 54.45, 97.95, 19.95, 24.6, 50.95, 75.6, 80.75, 90.4, 99.8, 60.25, 20.2, 64.15, 20.25, 105.85, 75.45, 93.85, 99, 80.3, 19.55, 100.75, 100.75, 53.75, 31, 25.6, 58.35, 80, 46.35, 113.75, 90.4, 109.3, 70.25, 90.3, 65.25, 100.15, 94.5, 60.65, 24.1, 19.5, 85.95, 53.5, 25.45, 20.5, 20.85, 89.9, 26, 113.2, 69.05, 20.1, 109.65, 19.2, 33.9, 90, 34, 20.4, 38.6, 25.25, 60.6, 89.95, 74.75, 20.6, 84.45, 20.4, 81.7, 79.5, 89.15, 20.3, 74.95, 74.4, 20, 25, 80.45, 19.75, 65.65, 71, 89.2, 86.75, 55.3, 61.5, 25.1, 55.15, 34.05, 19.95, 19.95, 89.7, 20.4, 26.3, 84.95, 20.7, 43.25, 48.35, 79.55, 71.05, 19.45, 110.8, 84.5, 69.3, 49.35, 20.35, 105.6, 64.45, 108.6, 49.9, 30.3, 30.4, 45.4, 65.65, 103.3, 84.15, 44.45, 19.75, 85.4, 89.9, 55.05, 104.1, 106.6, 75.2, 70.5, 19.6, 55.85, 24.05, 38.1, 106.4, 34.25, 100.05, 68.65, 45.8, 75.75, 84.4, 96.4, 20.55, 50.95, 90.5, 79.4, 58.75, 59.45, 105.7, 56.25, 53.3, 85.55, 68.65, 24.3, 77.85, 59.9, 23.95, 20.15, 105.35, 95.65, 87.05, 81, 82.45, 53.5, 20.5, 25.1, 54.4, 58.6, 84.8, 61.4, 20.4, 79.65, 20.15, 94.45, 79.8, 54.2, 19.45, 74.05, 49.15, 19.4, 113.65, 106, 25.95, 19.1, 103.4, 100.55, 95.4, 75.15, 84.45, 89.15, 107.9, 19.5, 85.95, 24.95, 59.4, 19.5, 69.95, 82.85, 19, 38.85, 30.6, 20.35, 95, 74.4, 78.45, 74.3, 51.05, 19.2, 99.55, 70, 109.1, 45.3, 29.85, 76.45, 95.1, 19.8, 72.8, 18.95, 76.65, 99.15, 101.75, 75.45, 64.1, 25.65, 75.1, 95.85, 54.4, 72.75, 19.85, 19.05, 44.95, 49.55, 94.85, 46.25, 19.35, 69.6, 90.7, 101.4, 20.25, 48.8, 74.35, 19.35, 68.75, 100.2, 20.85, 95.9, 19.35, 45, 81.5, 25.5, 48.9, 84.1, 19.6, 20, 81.3, 95.2, 36.45, 83.3, 25.05, 20.3, 89.85, 49.85, 19.8, 54.65, 29.35, 19.15, 19.1, 55.55, 80.55, 20.25, 69.5, 106, 25.5, 104.3, 79.6, 55.25, 88.05, 20.4, 117.6, 20, 19.65, 70.55, 93.85, 65.8, 20.05, 80, 35.4, 79.6, 80.25, 50.45, 20.45, 79.6, 24.7, 77.3, 29.75, 44.9, 29.8, 74.65, 71.95, 20.75, 56.3, 105.25, 94.2, 19.55, 84.45, 53.65, 29.9, 19.7, 43.7, 55.3, 19.85, 19.65, 49.45, 106.55, 20.1, 20.45, 39.7, 54.5, 83.8, 55.15, 111.6, 86.65, 55.55, 20.55, 106.75, 62.1, 104.5, 101.8, 110.6, 84.9, 93.2, 24.4, 70.55, 78.45, 85, 87.45, 85.8, 91.1, 70.75, 20.1, 20.05, 74.8, 24.8, 100.85, 101.35, 81.7, 68.25, 105.1, 20.4, 79.15, 20, 79.4, 57.2, 58.6, 94.8, 102.5, 20.35, 84.9, 69.2, 95.45, 100.95, 20.85, 88.5, 35, 55.15, 50.95, 64, 69.1, 80.2, 49.3, 84.35, 20.05, 117.2, 20.1, 69.6, 103.45, 77.95, 109.95, 94.75, 80, 79.65, 25.2, 19.9, 78.45, 44.8, 20.3, 19.2, 80.05, 107.35, 47.85, 70.8, 29.5, 70.75, 59.1, 25.55, 84.45, 20.25, 75.55, 85.65, 70.15, 95.3, 70.25, 50.3, 97.8, 19.85, 46.3, 19.35, 106.3, 25, 20.3, 75.35, 89.4, 88, 83.15, 43.8, 62.05, 20.1, 74.15, 101.35, 84.05, 20.9, 105.9, 99.5, 44.15, 53.9, 85.45, 85.05, 44.1, 90.2, 50.85, 59.2, 53.45, 19.95, 83.2, 74.65, 54.9, 57.5, 103.9, 19.65, 93.8, 89.25, 94.15, 55.6, 48.7, 19.25, 104.9, 75.45, 54.85, 19.9, 19.4, 25.05, 84.45, 19.3, 95.1, 79.85, 25.55, 75.5, 73.75, 96.05, 68.4, 20.65, 55.15, 70.6, 19.95, 19, 44.1, 107.6, 61.55, 90.7, 99.25, 91.7, 100.7, 78.45, 84.3, 19.55, 88.95, 20.45, 55.6, 86.8, 20.95, 20.05, 50.7, 113.65, 53.4, 101.9, 59.5, 87.8, 41.9, 83, 69.85, 56.3, 109.55, 92.15, 69.5, 97, 58.35, 50.6, 89.5, 70.4, 69.8, 94.3, 93.8, 19.55, 95.95, 101.05, 94.8, 107.75, 54.6, 71.3, 19.5, 56.3, 94.7, 104.15, 90.55, 60.8, 98.8, 98.15, 35.35, 103.15, 107.75, 81.4, 61.45, 95.7, 104.8, 70.95, 44.95, 97.65, 35.65, 90.55, 85.25, 19.5, 88.8, 25.1, 100.05, 55.7, 85.2, 91.15, 83.85, 45.9, 25.1, 91.4, 19.7, 91.5, 51.3, 21.1, 104.75, 106.15, 85.75, 20.3, 100.75, 74.15, 78.55, 45.3, 19.85, 50.7, 45, 77.8, 83.45, 73.25, 94.8, 20.1, 59.9, 90.1, 51.05, 70.95, 29.2, 46.6, 85.35, 75.35, 74.3, 69.3, 75.2, 20.9, 94.3, 76.45, 54, 104.25, 19.95, 24.95, 84.75, 19.75, 113.65, 44.9, 75.25, 24.6, 25, 20.95, 110.6, 55.5, 43.3, 109.5, 19.45, 84.85, 19.6, 53.45, 19.8, 112.1, 84.8, 95.05, 50.35, 74.6, 19.7, 74.2, 69, 19.35, 59.45, 19.8, 105.2, 109.2, 79.15, 53.65, 100.2, 45.15, 108.65, 40.65, 55.35, 105.6, 93.8, 95.7, 83.2, 90.05, 97.65, 68.05, 96.2, 79.6, 102.1, 23.4, 71.05, 85.25, 19.45, 59.45, 92.2, 19.85, 43.9, 80.5, 89.8, 90.5, 90.45, 50.75, 84.6, 89.65, 99.15, 19.95, 20.5, 62.1, 79.5, 19.55, 20.35, 51.7, 23.3, 65.4, 65.1, 81.2, 72.9, 74.5, 80.5, 60.3, 75, 90.15, 40, 99.45, 69.05, 59.7, 19.85, 86.25, 45.65, 70.1, 40.75, 70.2, 55.35, 95.7, 46.3, 81.3, 84.2, 20, 66.15, 45.85, 19.6, 49.8, 101.75, 55.15, 75.25, 103.95, 100.15, 99.65, 73.7, 50.05, 60.25, 105.75, 87.3, 48.35, 54.25, 85.3, 50, 24.4, 90.95, 72.25, 96.1, 19.85, 55.3, 20.1, 69.5, 25.15, 20.95, 49.55, 79.65, 71.25, 113.8, 24.55, 19.7, 20.25, 50.15, 100.5, 95.9, 74.45, 104.1, 19.05, 25, 19.05, 81.9, 69.7, 90.15, 25.35, 24.65, 19.55, 25.25, 60, 89.9, 19.4, 49.8, 24.1, 54.25, 109.9, 35.5, 87.55, 45.15, 88.4, 50.8, 99, 84.4, 96.55, 59.75, 111.5, 24.25, 75.1, 70.15, 101.75, 45.8, 20.5, 70.4, 30.55, 84.9, 20.1, 40.65, 101, 69.1, 54.5, 75.35, 44.45, 75, 100, 98.05, 71.15, 54.15, 63.9, 69.15, 64.65, 108.75, 98.85, 49.15, 89.6, 83.25, 70.25, 19.4, 24.5, 79.15, 20.1, 73, 61.4, 84.3, 19.9, 20.4, 50.75, 20.45, 75.75, 65.4, 80.4, 59.75, 78.5, 102, 48.95, 99.65, 18.25, 54.55, 20.65, 40.65, 20.45, 24.8, 70.8, 89.05, 96.6, 88.8, 20.05, 104.5, 69.8, 77.15, 35.05, 108.1, 84.05, 20.2, 50.6, 49.2, 24.6, 71.65, 104.9, 106.5, 49.35, 75.5, 94.25, 68.95, 58.5, 78.9, 93.85, 79.2, 109.45, 59.2, 29.15, 20.05, 76.05, 24.45, 66.5, 49.55, 89.35, 73.6, 82.65, 49, 80.35, 25.2, 25.45, 55.8, 110.9, 77.75, 26.2, 19.9, 79.05, 95, 25.2, 80.85, 98.4, 56.35, 19.3, 50.4, 79.4, 55.25, 19.1, 84.05, 105.2, 101.4, 89.8, 75.75, 95.3, 109.75, 19.85, 19.3, 69.1, 91.25, 20.25, 54.75, 81.45, 49.1, 80.2, 100.3, 65.25, 90.95, 85.45, 20, 94.1, 79.85, 71.65, 73.55, 104.65, 19.3, 20.15, 44.55, 54.45, 19.65, 105, 88.7, 74.25, 75.15, 20.25, 109.1, 30.75, 112.9, 74.2, 94.05, 78.85, 55.3, 19.35, 25.35, 20.45, 19.35, 101, 100.2, 89.05, 78.65, 74.75, 70.1, 19.9, 58.35, 105.65, 100.5, 20.05, 25.65, 96.5, 95, 70.85, 85.95, 73.9, 45.45, 20, 49.2, 109.45, 83.25, 19.25, 19.65, 72.8, 109.65, 65, 114.1, 20.65, 86.95, 94.75, 25.35, 105.45, 25.4, 102.55, 100.2, 24, 25.6, 73.5, 74.05, 98.25, 54.4, 101.55, 103.1, 34.2, 43.75, 111.95, 100.65, 55.95, 116.05, 45.75, 82, 65.15, 44.8, 79.8, 88.85, 74.95, 106.85, 74.95, 80.15, 19.3, 109.25, 56.1, 19.7, 51.3, 118.6, 24.15, 20.3, 115.5, 25.05, 109.1, 19.65, 111.3, 29.9, 80.6, 20.8, 35.2, 78.8, 89.95, 116.05, 19.55, 106.4, 49.4, 115.25, 24.8, 19.9, 81.25, 69.95, 69.1, 90.2, 93.55, 86.4, 66.3, 94.65, 80.85, 82.05, 72.1, 34.7, 20.55, 95.95, 44.8, 109.4, 71.05, 78.55, 19.7, 40.25, 19.85, 68.25, 20.15, 50.95, 78.65, 25.15, 20.25, 42.9, 44, 20.25, 34.25, 58.5, 55.8, 88.9, 57.65, 96.2, 79.15, 108.05, 74.4, 94.8, 45.9, 105.3, 102.6, 73.85, 61.35, 57.55, 29.25, 84.55, 19.6, 111.75, 106.5, 107.7, 19.3, 20.05, 69.95, 63.7, 24.75, 50.9, 60.4, 79.25, 85.8, 24.45, 110.1, 90.7, 25.3, 105.7, 85.2, 24.35, 24.25, 25.1, 54.55, 96.6, 76.5, 81.15, 38.5, 92.9, 93.5, 84.7, 66, 101.5, 74.9, 20.75, 61.45, 54.5, 69.6, 99.75, 109.75, 80.85, 20.3, 67.8, 24.05, 19.8, 25.7, 56.15, 86.7, 20.4, 19.65, 50.55, 54.35, 108.1, 54.45, 45.35, 59, 69.45, 100.55, 64.95, 20.5, 18.85, 19.8, 25.05, 74.8, 114.3, 24.45, 109.2, 45.05, 51, 110.45, 84.65, 60.05, 44.65, 93.25, 20.25, 25.45, 20.6, 94.1, 34.8, 60.75, 51.35, 64.05, 84.8, 71, 50.15, 94.6, 59.75, 100.25, 98.9, 97.7, 40.3, 60.25, 56.25, 46.2, 50.6, 24.9, 84.85, 65.7, 63.35, 50.1, 70.5, 94.85, 50.15, 19.75, 64.65, 79.6, 19.5, 99.55, 74, 38.9, 79.55, 65.45, 98.7, 46.3, 99.35, 95.8, 67.5, 78.15, 26.1, 69.6, 84.35, 100.2, 78.05, 40.35, 79.2, 20.9, 73.6, 74.75, 49.9, 68.9, 20.25, 76, 74, 82.3, 89.4, 99.15, 20.2, 29.45, 19.8, 59.15, 44.75, 90.8, 49.55, 106.7, 93.55, 94.45, 19.45, 25.05, 67.95, 65.25, 99.45, 20.35, 19.95, 77.4, 19.7, 99.7, 74.8, 19.15, 78.95, 95.55, 62.85, 71.55, 94.95, 86.1, 19.55, 24.8, 39.3, 84.05, 36.25, 20.25, 23.9, 98.6, 103.65, 92.9, 19.9, 20.1, 85.45, 80.5, 99.9, 39.85, 60.5, 84.8, 103.85, 67.8, 75.2, 24.85, 19.35, 49.35, 89, 55, 76.15, 20.3, 74.9, 117.35, 19.75, 45.2, 25.2, 89.75, 75, 49.95, 65.7, 67.05, 110.9, 87.95, 19.8, 75.7, 62.15, 101.25, 115.15, 18.95, 19.5, 86.55, 28.6, 20.4, 19.8, 45.65, 56.4, 73.3, 24.35, 101.35, 98.65, 33.6, 79.9, 20.7, 104.05, 20.25, 103.3, 73.7, 96.2, 108.75, 20.15, 19.75, 25.95, 70.05, 24.05, 84.75, 23.05, 104.15, 59.95, 19.55, 19.6, 20.05, 85.55, 78.6, 116.8, 43.55, 60.8, 54.9, 65.2, 102.95, 90.6, 50.8, 90.05, 108.2, 92, 75.1, 25.05, 75.15, 19.5, 19.3, 112.2, 70.3, 19.6, 20.25, 75.85, 80.65, 68.5, 115.75, 73.5, 80.6, 69.95, 59.55, 19.05, 95.65, 19.95, 70.05, 19.4, 36.1, 94, 61.15, 19.75, 64.1, 19.75, 19.7, 110.2, 106.35, 90.55, 65.9, 104.5, 52.5, 56.1, 88.75, 84.45, 75.3, 26, 99.4, 109.55, 19.6, 73.15, 54.65, 66.4, 115.55, 104.45, 100.05, 102, 91.15, 89.7, 90.2, 92.4, 19.9, 25.15, 79.85, 18.85, 25.75, 49.6, 20.95, 97.05, 25.4, 19.7, 35, 101.25, 70.2, 90.95, 73.85, 88.05, 105.95, 91.85, 20.1, 40.1, 110.3, 73.9, 89.8, 85.15, 60.95, 72.25, 73.55, 46, 58.55, 24.6, 19.75, 86.35, 25.5, 19, 19.55, 110.1, 96.55, 69.75, 50.6, 65.6, 40.1, 82.1, 79.1, 101.25, 79.55, 90.65, 20.55, 75.75, 110, 20.85, 80.35, 70.15, 84.05, 67.45, 20.75, 89.1, 69.9, 51.1, 94.4, 78.25, 25.55, 60, 90.55, 76.4, 84.95, 110.1, 99.65, 45.4, 69, 48.65, 44.15, 59.85, 75.75, 80.65, 20.55, 66.4, 100.2, 19.1, 80.3, 44.55, 20.35, 91.8, 74.9, 20.2, 50.35, 18.8, 20.45, 64.75, 98.7, 89.45, 58.75, 20.7, 85.6, 80.3, 79.8, 79.85, 54.1, 80.85, 24.75, 80.9, 24.5, 20.15, 20.05, 19.6, 114.3, 100.3, 80, 20.85, 89.95, 20, 90.85, 48.75, 80, 79.7, 20.35, 57.55, 20.25, 19.4, 100.4, 57.95, 59.5, 19.2, 86.5, 59.55, 103.95, 25.1, 103.95, 68.95, 103.1, 24.7, 110.2, 48.95, 62.45, 89.55, 83.55, 78.9, 20.35, 71.45, 46.35, 94.65, 49.9, 25.45, 89.15, 20.75, 66.1, 75.4, 70.45, 60.3, 21.05, 69.35, 88.85, 97, 66.4, 24.75, 69.2, 79.5, 100.65, 103.3, 79.7, 61.4, 69.8, 40.55, 75.65, 90.7, 80.5, 60.6, 101.15, 24.95, 20.3, 60, 20.25, 78.5, 44.75, 19.85, 98, 79.9, 107.7, 99.7, 104.7, 58.6, 93.9, 86.45, 98.5, 19.4, 50.45, 24.95, 75, 94.65, 100.25, 78.2, 94.2, 88.45, 69.85, 81.7, 50.05, 79.9, 69.55, 25.4, 90.1, 44.65, 83.75, 80.35, 98.1, 53.35, 19.55, 20.9, 48.95, 54.2, 24.45, 69.4, 40.15, 74.9, 25.6, 70.35, 91.7, 89.2, 24.1, 74.15, 53.85, 115.6, 19.75, 24.05, 25.3, 84.3, 70.1, 89.75, 97.95, 20, 78.3, 103.9, 20.7, 96.8, 94.4, 20.15, 26, 77.35, 66.05, 19.9, 84.3, 68.15, 80.85, 75.5, 92.45, 80.6, 83.2, 87.55, 99.55, 81.25, 109.4, 19.95, 45.55, 20.7, 75.3, 99.25, 93.4, 73.75, 80.45, 88.15, 49.2, 19.65, 79.35, 79.75, 105.15, 49, 100.05, 69.35, 49.8, 85.8, 79.7, 20.95, 50.55, 79.3, 19.5, 80.55, 44.15, 84.5, 105.5, 84.3, 92.7, 26.25, 96.95, 20.45, 115.8, 108.2, 20.2, 67.75, 54.9, 85.25, 20.15, 90.35, 55.75, 114.6, 80.05, 20, 66.8, 100.3, 105.35, 85.2, 48.8, 18.95, 69.8, 106.15, 20.55, 105.75, 25.25, 19.75, 104.85, 60.95, 81.15, 19.1, 20.8, 90.15, 90.1, 74.1, 85.05, 118.75, 85.9, 95, 20.15, 101.3, 21.2, 24.2, 20.3, 102.8, 85.3, 89.6, 99.95, 56.25, 50.95, 115.85, 103.65, 26.1, 35.1, 99.1, 67.25, 25, 59.55, 77.8, 55.1, 117.8, 24.15, 45.25, 79.5, 20.25, 64.75, 54.6, 20.7, 94.75, 79.65, 115.8, 49.45, 83.8, 95.35, 94.7, 74.05, 89.6, 116.6, 54.2, 19.3, 65.05, 92.5, 19.45, 24.05, 18.75, 20.15, 20, 71, 75.55, 93.6, 70, 24.4, 74.8, 65.25, 50.55, 104.4, 70.7, 45.25, 70.3, 108.95, 26.45, 86.2, 19.65, 51.2, 19.05, 74.75, 75.8, 25.1, 44.45, 104.3, 19.5, 89, 20.15, 74.9, 74.9, 36.15, 19.2, 19.25, 61.2, 20.45, 35.05, 100.25, 44, 102.8, 50.35, 100, 20, 99.85, 94.2, 86.4, 58.4, 83.85, 88.3, 94.1, 104.05, 108.9, 107.4, 94.7, 90.85, 19.9, 66.4, 100.65, 100.7, 25.6, 19.85, 20.75, 95.8, 94.65, 80.55, 106.65, 45.85, 104.35, 55.45, 78.85, 61.15, 78.95, 44.45, 109.2, 61.3, 96.85, 40.55, 19.8, 108.25, 105.05, 90.45, 86.4, 66.9, 110.7, 20, 84.9, 102.1, 20.25, 70.15, 74.35, 80.05, 62.05, 49.2, 20.5, 38.25, 54.95, 96.6, 19.9, 19.9, 84.6, 80, 85.25, 81.25, 115.5, 104.1, 79, 39.1, 94.65, 20.8, 59.5, 20.05, 100.45, 76.5, 20.6, 20.3, 49.2, 39.55, 23.15, 20.45, 80.85, 25.25, 91.25, 72.45, 60.1, 19.7, 78.95, 75.1, 25, 69.15, 91.55, 45.15, 35.8, 113.15, 19.85, 19.8, 19.9, 19.7, 79.4, 59.1, 53.95, 91.15, 99.3, 68.95, 51.55, 24.4, 96.8, 70.05, 19.5, 78.75, 69.2, 19.55, 80.65, 103.65, 54.7, 54.15, 71.1, 84.85, 20, 106.25, 99.25, 19.35, 20.8, 94.75, 114.05, 74.9, 19.8, 94, 80.85, 54.65, 91.7, 118.6, 24.55, 19.45, 116.15, 80.6, 20.3, 89.85, 46, 66.25, 99.8, 90, 70.45, 75, 19.9, 80.3, 19.75, 84.3, 54.05, 104.9, 53.95, 97.25, 83.05, 105.5, 81, 41.1, 45, 74.55, 40.2, 70.5, 19.75, 24.65, 104.25, 78.35, 69.8, 109.7, 73.75, 33.45, 94.6, 54.55, 20.2, 20.3, 39.4, 69.15, 76.25, 93.9, 51.35, 100.05, 70.4, 20.3, 94.45, 46.4, 104.05, 91.15, 24.9, 59.6, 108.5, 40.55, 58.95, 70.95, 20.75, 113.15, 48.8, 63.05, 100.85, 99.5, 80.55, 64.4, 75.2, 84.9, 19.3, 83.9, 117.45, 104.4, 74.65, 59.05, 69.1, 20.55, 76.55, 62.5, 29.4, 94.9, 111.65, 19.9, 20.45, 106.05, 113.45, 92.55, 65.6, 84.35, 44.65, 71.1, 85.15, 49.7, 30.2, 25.25, 84.05, 85.7, 74.7, 56.35, 90.8, 107.55, 19.85, 95.9, 23.85, 106.15, 83.85, 85.35, 84.8, 90.85, 76.1, 74.55, 39.2, 79.55, 19.6, 19.55, 39.15, 20.1, 99.95, 59.8, 49.75, 35.75, 108.5, 60.15, 19.05, 46, 84, 44.55, 103.45, 80.65, 57.2, 110.75, 24.7, 97.05, 76.35, 89.4, 18.9, 74.45, 19.8, 50.9, 84.4, 24.4, 20.05, 81, 98.35, 55.5, 51, 91.65, 84.3, 100.2, 19.4, 90.85, 69.4, 94.45, 20.4, 94.75, 20.15, 95.7, 44.35, 74.55, 73.6, 74.95, 47.95, 50.1, 63.6, 53, 19.85, 24.35, 19.55, 25.05, 93.8, 36.85, 103.75, 56.75, 20.8, 44.1, 24.45, 25.6, 50.75, 104.4, 39.3, 59.65, 83.3, 79.55, 24.45, 19.2, 29.8, 45.5, 106.45, 30.05, 65.65, 96.05, 75.1, 74.05, 44.7, 110.75, 19.7, 49.5, 55, 43.95, 74.35, 111.15, 104.7, 55.7, 20.6, 19.65, 115.8, 88.65, 94.5, 20.1, 34.65, 52.3, 65, 19.85, 35.45, 19.7, 95.6, 19.85, 81.85, 109.3, 70.3, 25.4, 69.8, 20, 85.55, 109.9, 50.3, 94.5, 101.5, 89.15, 19.4, 29.9, 78.8, 85.35, 79.65, 19.3, 79.6, 96.8, 20.65, 19.8, 90.6, 104.6, 80.05, 45.15, 73.15, 99.1, 20.2, 106.05, 105.35, 45.65, 79.95, 54.45, 25.1, 84.7, 75.85, 48.8, 99.15, 35.2, 76.25, 55.9, 82.35, 40.4, 24.9, 54.3, 66.3, 20.9, 75.35, 85.15, 75.35, 104.45, 49.45, 19.45, 92.15, 93.8, 19.85, 100.25, 95.7, 93.15, 69.7, 19.8, 71.35, 20.75, 40.6, 20.4, 20.35, 19.75, 54.4, 94.7, 30.5, 20.45, 66.15, 89.85, 45.05, 86.85, 96.75, 77, 20.1, 75.3, 106.65, 110.15, 82.85, 20.1, 99.2, 59.45, 58.6, 49.7, 65.85, 73.5, 85.5, 20.05, 113.65, 83.4, 65.65, 70.4, 61.35, 85.9, 75.65, 49.75, 70.9, 49.85, 75.3, 20.1, 94, 103.05, 118.35, 99.7, 81.9, 30.45, 96.1, 66.2, 104.25, 80.2, 19.75, 72.6, 116.5, 106.8, 24.95, 89.25, 19.25, 104.55, 87.2, 30.75, 25.7, 86.2, 30.1, 99.35, 19.2, 20.1, 20.35, 25.65, 94.55, 104.2, 94.4, 56.1, 68.25, 24.75, 76.25, 74.35, 54.15, 19.45, 34.95, 53.65, 69.65, 104, 70.35, 80.8, 64.85, 19.65, 45.9, 20, 44.8, 80.3, 20.35, 45.8, 84.1, 108.95, 69.35, 64.35, 90.8, 24.95, 79.6, 84.7, 70.8, 36.45, 104.4, 101.5, 54.3, 103.95, 91.1, 19.95, 26.45, 89.4, 75.1, 108.1, 110.15, 80.35, 111.5, 106.5, 19.9, 111.1, 70.7, 24.85, 91.2, 65.6, 40.65, 59.45, 109.95, 60.45, 84.9, 38.5, 92.55, 73.55, 20.15, 34.7, 24.5, 19.7, 20.6, 58, 107.45, 65.5, 25.45, 100.15, 104.45, 21.15, 96.2, 44.4, 107.55, 94.35, 98.75, 20.3, 101.15, 105.75, 81.15, 89.55, 54.75, 53.75, 105.75, 105.85, 64.2, 88.7, 87.7, 89.3, 20.15, 79.75, 94.55, 20.05, 67.2, 94.55, 69.05, 107.5, 73, 114.75, 76.05, 96.25, 101.1, 104.7, 77.9, 90.65, 110.45, 68.7, 44.85, 29.8, 88.9, 58.75, 19.85, 86.9, 59.65, 55.25, 66.4, 90.1, 20.15, 108.1, 53.75, 56.9, 89.3, 109.6, 25.15, 79.15, 66.75, 95.2, 48.8, 45.7, 80.7, 74.5, 20.55, 79.65, 115.1, 59.7, 86.45, 33.7, 80.1, 104.05, 108.75, 41.1, 20.35, 105.9, 101.3, 80.05, 89.2, 65.5, 40.45, 70.45, 78.8, 83.65, 90.1, 82.45, 20.25, 66.25, 19.5, 51.25, 89.7, 64.55, 45.6, 93.65, 49.65, 73.6, 109.75, 61.45, 106.4, 81.9, 105.2, 54.6, 20.55, 20, 19.7, 66.05, 34, 92.5, 54.05, 58.9, 88.35, 107.95, 96.9, 19.1, 50, 45.4, 85.45, 84.1, 74.45, 64.75, 66.25, 76.9, 89.8, 74.6, 116.95, 40.65, 114.35, 69.7, 95.5, 98.65, 61.65, 89.35, 95.4, 35.4, 19.95, 19.25, 29.65, 84.5, 20.4, 24.75, 25.35, 90.7, 20, 59.75, 82.5, 70.3, 20.35, 90.8, 103.95, 104.95, 105.25, 74.75, 50.8, 23.75, 61.3, 75.8, 98, 80.25, 78.9, 52, 84.75, 64.4, 85.45, 45.8, 30.5, 19.9, 69.15, 99.45, 49.25, 39.35, 70.6, 105.1, 81, 20.1, 84.85, 19.75, 19.75, 70.4, 20.45, 20.35, 86.2, 95.65, 103.8, 97.2, 63.55, 24.95, 89.15, 99, 24.8, 85.55, 94, 105.65, 50.3, 95, 61.4, 80.55, 78.5, 114.3, 20.05, 62.65, 80.85, 92.7, 100.45, 75.2, 84.75, 89.45, 79.5, 72.15, 19.8, 76.4, 100.9, 95.3, 90.95, 54.5, 61.6, 79.9, 96.15, 49.6, 65.3, 25, 45.45, 107.75, 89.1, 19.65, 44.75, 101.6, 103.15, 84.65, 95.65, 75.1, 61.35, 69.55, 19.7, 31.05, 51, 51, 88.85, 20.05, 65.1, 70.15, 44.35, 20.75, 56.05, 19.95, 98.6, 79.7, 79, 89.45, 74.2, 81, 49.6, 84.6, 55, 84.85, 84.2, 106.3, 69.05, 45.4, 73.65, 73.9, 77.75, 99.35, 50.75, 87.1, 20.15, 98.7, 25.2, 55.7, 65.35, 25.3, 84.35, 84.95, 73.85, 24.25, 51.8, 46, 79.4, 60.5, 25.1, 71.8, 20.05, 88.4, 30.25, 20.2, 59.9, 25.15, 46, 101.3, 76.95, 55.3, 92.45, 48.45, 19.35, 51.75, 86.7, 94.4, 55.7, 84.25, 64.65, 70.15, 69.2, 54.65, 24.75, 23.95, 105, 59.85, 20.05, 92.15, 44.8, 20.9, 95.4, 80.35, 85.1, 34.7, 115.05, 81.1, 19.95, 20.55, 106.6, 86.15, 78.85, 106.75, 86.55, 42.4, 89.45, 24.25, 97.9, 20.5, 19.6, 20.25, 55.7, 20.6, 19.8, 79.8, 80.2, 116.4, 31.65, 94.15, 20.65, 76.85, 20.15, 55.25, 39.05, 82.15, 103, 95.1, 83.9, 95.15, 79.8, 74.8, 69.85, 20.45, 78.35, 53.55, 19.1, 20, 93.9, 19.95, 113.15, 24, 84.95, 80.5, 19.3, 19.15, 91.3, 49.65, 54.35, 19.15, 88.45, 19.75, 75.5, 83.75, 19.4, 26.5, 90.5, 19.15, 94.85, 69.95, 40.9, 80.25, 48.6, 70.8, 60.2, 55.2, 55.8, 54.15, 80.15, 75.5, 100.4, 62.55, 70.45, 85.5, 20.2, 54.5, 20.75, 20.35, 91, 104.8, 74.75, 104.05, 51.1, 89.8, 20.55, 64.05, 74.85, 96.65, 20.05, 103.45, 25, 20.3, 26.35, 19.9, 54.7, 46.35, 90.25, 19.95, 20.65, 79.6, 25.45, 19.5, 75.9, 76.2, 66.15, 19.25, 69.1, 39.1, 20.05, 59.8, 84.3, 48.6, 79, 105.35, 25.1, 49.75, 94.75, 93, 71.9, 77.55, 19.85, 70.25, 95.25, 84.6, 25.05, 53.15, 20.15, 101.25, 100.55, 24.1, 25.3, 71.8, 19.7, 49.85, 69.6, 19.75, 80.8, 60, 86.55, 20.85, 64.2, 35, 50.75, 105.5, 19.2, 85.15, 90.65, 20, 74.65, 61.2, 19.95, 54.8, 73.45, 51.45, 80.45, 54.2, 109.5, 104.4, 85.3, 79.3, 76.5, 105.1, 25.4, 86.85, 19.65, 75.7, 45.55, 78.1, 19.3, 110.5, 90.8, 20.3, 81.35, 97.95, 108.15, 55.3, 56.55, 80.5, 19.7, 104.05, 52.85, 104.3, 80.65, 71.35, 24.65, 21.3, 110.2, 89.4, 51.05, 19.8, 19.9, 87.3, 19.85, 89.4, 20, 20.05, 83.25, 20.6, 102.9, 39.1, 99.95, 114.5, 20.2, 55.8, 24.2, 81, 72.8, 99.85, 99.5, 70.15, 20.25, 26, 19.9, 19.05, 96.5, 19.85, 25.7, 20.3, 70.15, 91.55, 39.4, 105.7, 70.25, 93.75, 96.55, 60, 59.8, 90.65, 109, 68.1, 20.4, 81.95, 60.55, 65.6, 82.5, 82.3, 68.15, 20.3, 95.55, 20.2, 89.2, 69.65, 89.3, 74.8, 20.2, 84.4, 87.55, 25.15, 19.8, 50.85, 102.4, 96.3, 55.5, 109.75, 106.4, 60, 88.8, 85.2, 35.1, 80.05, 75.55, 49.55, 81.3, 23.9, 66.4, 19.6, 18.8, 108.4, 85.95, 85.45, 80.9, 71, 111.8, 20.6, 85.05, 44.6, 44.4, 105.1, 115.15, 59.8, 26.3, 70.55, 20.05, 79.85, 70.3, 79.35, 90.05, 24.45, 59.95, 25.35, 90.8, 70.45, 34.3, 105.05, 19.3, 19.15, 51.4, 71.85, 75.4, 49.7, 45.25, 78.75, 81.6, 70.4, 75.8, 76.1, 94, 103.95, 19.95, 71.3, 110.8, 69.1, 96.1, 48.8, 50.55, 44.65, 88.25, 19.45, 89.3, 70, 19.25, 70.5, 97.35, 19.65, 20.85, 19.65, 19.35, 44, 94.4, 25.9, 55.65, 69.65, 75.4, 100.6, 71, 86, 106.95, 21.2, 61.05, 29.6, 79.95, 19.7, 20.3, 59.9, 24.35, 19.75, 50.3, 95.6, 50.25, 85.35, 41.6, 51.65, 24, 100.85, 59.85, 25.45, 23.9, 24.15, 75.7, 40.2, 84.5, 50.85, 91.6, 98.9, 85, 78.95, 44.3, 20.2, 80.2, 60.9, 34.2, 85.2, 87.15, 54.3, 19.1, 112.75, 19.95, 19.5, 65.55, 78.8, 78.2, 105.25, 89.25, 20.65, 68.7, 78.65, 24.75, 19.75, 89.1, 84.7, 98, 94.45, 105, 93.85, 59.9, 19.95, 84, 108.9, 33.6, 85.85, 34.85, 48.75, 84.85, 56.65, 95.3, 73.9, 24.5, 84.6, 44.95, 24.7, 100.3, 25.45, 50.7, 55, 68.4, 89.9, 78.55, 55.05, 19.8, 84.45, 35.9, 80.75, 78.65, 61.75, 63.7, 99.45, 25.2, 74.05, 87.6, 89.15, 20, 55, 104.4, 20.05, 89.75, 34.3, 20.65, 84.25, 19.65, 79.85, 20.2, 19.8, 50.35, 85.15, 74.6, 79.15, 20.35, 21.05, 94.6, 94.7, 94.25, 72.45, 74.95, 105.2, 111.95, 19.85, 89.75, 20.05, 108.95, 19.65, 24.9, 82.85, 93.2, 84.8, 71.75, 30.35, 54.85, 19.5, 103.85, 24.2, 19.35, 83.6, 100.65, 94.1, 74.55, 108.45, 56.15, 20.35, 80.55, 61.25, 20.45, 18.9, 19.6, 91.5, 45.2, 19.45, 25.45, 80.85, 94.9, 49.05, 29.3, 105.3, 88.95, 20.25, 110.85, 110.5, 109.4, 114.2, 36.5, 70.75, 19.95, 19.6, 40.15, 76.6, 19.6, 85.3, 65.85, 94.45, 20.05, 99.4, 20, 78.45, 25.1, 97.35, 55, 71.1, 61.55, 45.9, 40.3, 87.1, 49.5, 73.8, 19.2, 45.3, 25, 94.95, 35.3, 44.55, 76.75, 81, 105.55, 18.8, 24.9, 23.45, 64.9, 61.35, 113.95, 90.15, 54.1, 29.7, 49.8, 101.1, 24.4, 95, 50.65, 69.9, 39.95, 55.4, 90.6, 103.25, 86.85, 94.25, 47.05, 20.55, 19.65, 70.2, 81, 75.9, 24.7, 99.05, 110.25, 85, 19.75, 23.9, 111.25, 55.1, 19.95, 25.15, 54.15, 59.8, 83.85, 104.9, 75.3, 66.65, 109.5, 73.85, 19.3, 118.2, 51.45, 59.45, 19.5, 19.55, 93.55, 59.3, 102.25, 95.9, 109.8, 78.1, 39.9, 64.9, 95.05, 53.4, 24.9, 44.7, 114, 20.25, 53.85, 83.85, 20.2, 19.95, 104.2, 50.25, 20.35, 90, 54.2, 99.5, 99.1, 66.9, 25.85, 91.05, 71, 93.2, 20.95, 109.2, 19.35, 85.8, 19.85, 19.65, 20.5, 89.65, 74.35, 49.45, 89.1, 75.15, 70.65, 104.2, 90.05, 79.25, 44.9, 19.4, 88.75, 70.1, 91, 29.65, 90.8, 77.85, 54.3, 18.95, 95.15, 102.4, 99.9, 88.7, 54.3, 55.7, 103.95, 110.85, 20.15, 20.05, 91.95, 80.5, 55.65, 74.7, 104.15, 83.65, 72.2, 110.05, 51.5, 25.5, 89.55, 19.5, 80.7, 77.5, 105.1, 25.15, 95.25, 95.65, 85, 80.8, 24.85, 54.75, 85.75, 50.75, 20.15, 20.05, 98.25, 71.6, 81.45, 58.4, 25.7, 53.7, 19.6, 89.4, 69, 84.2, 106.1, 25.75, 46.05, 64.95, 85.45, 20.05, 76.4, 100.5, 20.7, 25.3, 40.05, 100.6, 69.95, 74, 99.4, 93.3, 49.15, 107.45, 83.6, 99.05, 80.1, 65.3, 89.55, 60.8, 74.5, 99.15, 19.25, 39.45, 44.85, 97.2, 110.55, 35.05, 73, 19.9, 76.95, 35.4, 20.45, 96.75, 54.2, 100.1, 45.25, 83.85, 70.1, 20.85, 33.45, 20.2, 85.9, 61, 70.65, 86.9, 69.4, 20.35, 20.35, 104.3, 44.95, 49.45, 20.6, 19.55, 99, 93.5, 54.55, 20.05, 83.95, 79.45, 116.2, 93.7, 79.85, 100, 19.6, 19.7, 20.2, 50.4, 113.35, 80, 80.95, 24.9, 54.9, 75.55, 109.25, 77.65, 95, 116.3, 19.9, 70.35, 25.6, 44.45, 100.15, 105.4, 95.85, 73.85, 70.1, 25.25, 79.15, 21.05, 24.95, 64.5, 19.65, 79, 105.95, 75.85, 91.85, 43.6, 91.25, 89.75, 104.4, 90.15, 40.3, 105.25, 106, 104, 69.65, 74.3, 100.9, 20.25, 49.9, 96.9, 100.35, 104.1, 20.1, 74.95, 56.55, 49.25, 68.6, 69.05, 19.7, 20.05, 103.7, 94.4, 54.95, 93.7, 110.25, 98.9, 89.75, 80.45, 79.4, 20.3, 62.8, 74.9, 74.85, 25.85, 101.95, 68.3, 48.4, 94, 105.05, 89.3, 25.15, 19.5, 92.95, 20.7, 74.3, 19.35, 44.65, 84.05, 80.7, 104.35, 19.55, 74.05, 40.1, 20.1, 101.7, 83.55, 56.85, 20.4, 19.55, 106.15, 78.95, 49.75, 92.4, 58.2, 102.6, 91.95, 65.25, 106, 73.1, 59.75, 55.1, 59.8, 116.6, 109.3, 101.4, 50.65, 56.15, 106.5, 19.2, 83, 70.1, 108.3, 91.05, 25.25, 45.35, 43.9, 77.5, 79.3, 84.9, 79.25, 71.05, 53.75, 24.25, 54.2, 44.25, 50.05, 20.15, 69.25, 69.35, 19.35, 19.15, 61, 20.5, 50.5, 50.2, 79.6, 24.9, 74.4, 106.9, 101.35, 55.35, 50.55, 19.5, 79.45, 90.65, 89.85, 79, 104.65, 19.55, 19.9, 116.25, 87.75, 100.05, 81.3, 44.3, 70.35, 44.45, 49.15, 29.45, 100.55, 85.3, 95.65, 69.1, 70.35, 20.6, 74.15, 75.05, 44.6, 21.45, 43.45, 20.05, 94.15, 94.4, 19.55, 75.9, 64.15, 109.55, 110.8, 55, 53.45, 69.95, 101.45, 97, 90.6, 73.55, 67.95, 94.35, 69.5, 18.85, 19.4, 69.2, 19.75, 54.6, 29.8, 69.65, 101.85, 103.05, 82.3, 20.3, 35.1, 105.7, 56.25, 60.35, 79.25, 59.8, 84.6, 93.4, 94.2, 25.05, 99.65, 50.65, 60.9, 59.65, 64.7, 25.1, 48.95, 54.85, 45.3, 91.35, 85.85, 25.1, 34, 45.9, 95.2, 20.5, 100.6, 55.3, 20.35, 74.85, 36.1, 65.8, 20.35, 105.8, 96.75, 102.35, 24.4, 115.65, 79.85, 73.05, 64.35, 20.5, 76, 54.75, 104.75, 74.65, 51.15, 41.95, 54.35, 56.25, 106.1, 96, 79.75, 61.45, 68.65, 19.65, 19, 100, 20.25, 98.7, 19.8, 73.8, 100.2, 74.9, 20.05, 106.2, 116.55, 99.7, 19.7, 19.5, 29.15, 55, 90.8, 51, 90.1, 59.05, 20.3, 72.95, 73.55, 84.3, 78, 72.1, 106.75, 19.25, 20.55, 20, 24.65, 103.5, 23.85, 25.8, 70.85, 69.8, 59.45, 54.55, 20.05, 82.55, 81.25, 70.75, 74.3, 94.1, 29.7, 109.7, 96.35, 66.6, 44.5, 110.9, 105, 25.3, 55.15, 20.1, 80.1, 69.05, 69.9, 20.4, 19.7, 50.1, 101.4, 83.45, 86.65, 20.15, 80.8, 19.4, 62.05, 76.45, 60.05, 91.3, 95.75, 20.35, 94.05, 84.1, 78.75, 55.55, 62.65, 74.5, 102.1, 20.1, 70.3, 53.65, 20.75, 103.4, 50.8, 50.15, 79, 74.6, 96.5, 20.1, 19.4, 77.55, 20.05, 19.85, 20.2, 67.45, 18.55, 29.75, 86.5, 24.2, 23.55, 20.45, 81.45, 92.3, 69.15, 53.65, 39.65, 54.65, 104.8, 29.3, 83.85, 79.55, 103.65, 99.05, 73.35, 100.05, 20.35, 43.95, 23.5, 70.7, 94.3, 29.15, 20.85, 37.7, 95.5, 91.05, 92.45, 44.15, 36.05, 50.25, 109.75, 79.2, 20.3, 112.35, 94.3, 41.15, 74.65, 48.25, 76.15, 71.1, 96.55, 79.3, 89.6, 20.5, 106.3, 100.35, 85.6, 45.25, 106.15, 51.1, 19.9, 25.7, 74.3, 99.4, 69.7, 98.35, 85.45, 95.9, 100.75, 89.2, 74.1, 100.6, 75, 25.75, 84.1, 79.3, 107.05, 20.05, 70.2, 19.5, 70.75, 45.3, 115.15, 72.95, 19.65, 19.55, 89.55, 50.35, 50.25, 87.25, 20.8, 109.25, 20.35, 55.9, 79.2, 96, 79.2, 24, 101.35, 100.1, 56.5, 35.45, 85, 79.4, 35.2, 19.65, 49.85, 68.75, 61.9, 79.9, 89.75, 59.3, 19.4, 93.65, 49.4, 19.9, 55, 72.9, 69.2, 25.6, 19.75, 55.7, 117.5, 19.85, 78.9, 20.65, 62.3, 92.5, 19.65, 79.75, 79.95, 29.9, 19.75, 45, 44.8, 69.65, 51.1, 53.15, 24.7, 111.6, 48.55, 109.95, 20.8, 20.2, 25.6, 39.65, 24.9, 108.4, 19.55, 85.1, 56.7, 69.05, 70.15, 111.15, 105.95, 89.35, 89.1, 91.25, 90.35, 105.55, 19.1, 20.4, 100.45, 74.95, 29.7, 50.35, 85.7, 47.85, 94, 69.85, 70.3, 25.85, 71.1, 98.8, 93.35, 99.85, 80.3, 50.55, 80.45, 81.3, 20.7, 79.05, 19.05, 19.6, 20.2, 86.8, 20.9, 103.6, 38.8, 88.4, 84.2, 79.7, 99, 100.75, 19.3, 55.75, 19.95, 91.75, 89.65, 45.85, 79.55, 55.95, 69, 83.55, 65.7, 94.9, 61.9, 111.1, 20, 67.7, 25.15, 92.85, 89.1, 111.3, 101.9, 91.65, 88.85, 60.6, 25.3, 65.5, 95.45, 19.95, 91.1, 54.15, 74.6, 94.6, 81.15, 89.05, 49.2, 19.45, 104.3, 69.7, 89.5, 86.05, 25.2, 35.15, 99.65, 105.35, 35.15, 73.75, 101.35, 24.3, 80.7, 89.85, 61.1, 29.05, 99.7, 55.9, 105.9, 46, 43.95, 80.4, 100.05, 45.1, 94, 68.95, 68.45, 69, 43.85, 44.5, 18.7, 70.25, 55.35, 53.55, 114.6, 20.1, 85.5, 108.75, 103, 97.85, 19.55, 84.05, 103.75, 89.4, 19.7, 79.85, 74.45, 74.1, 69.35, 18.8, 73.85, 64.4, 55.8, 20.05, 75.15, 99.15, 56.75, 69.6, 104.15, 110.8, 80.15, 35.75, 69.9, 89.2, 55.65, 50.7, 20, 30.5, 19.1, 98.3, 45.55, 101.05, 103.7, 36.25, 49.4, 19.9, 107.4, 82, 19.8, 45.05, 64.55, 86.25, 19.75, 89.1, 95.55, 75.4, 101.25, 102.6, 56.3, 94.2, 43.05, 89.5, 74.4, 20.5, 74.35, 99.75, 111.95, 94, 98.85, 64.35, 72, 49.7, 80.7, 24.2, 39, 65.45, 74.35, 83.2, 25, 40.2, 94.1, 108.35, 69.5, 76, 93.6, 95.65, 100.55, 88.05, 24.45, 89.55, 66.5, 76.1, 80.5, 35.45, 20.55, 49.9, 105.4, 35.75, 95.1, 19.3, 104.5, 63.1, 75.05, 81, 74.45, 60.4, 84.95, 93.4, 89.2, 85.2, 49.95, 20.65, 70.65, 20.15, 19.2, 59.8, 104.95, 103.5, 84.8, 95.05, 44.2, 73.35, 64.1, 44.4, 20.05, 60, 75.75, 69.5, 102.95, 78.7, 60.65, 21.15, 84.8, 103.2, 29.6, 74.4, 105.65], "y0": " ", "yaxis": "y"}], "layout": {"boxmode": "group", "hoverlabel": {"font": {"size": 8.8}}, "legend": {"tracegroupgap": 0}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "white", "showlakes": true, "showland": true, "subunitcolor": "#C8D4E3"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "white", "polar": {"angularaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}, "bgcolor": "white", "radialaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "yaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "zaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "baxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "bgcolor": "white", "caxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}}}, "title": {"font": {"size": 13.2}, "text": "Distribution of Monthly Charges by Churn Status"}, "xaxis": {"anchor": "y", "domain": [0, 1], "tickfont": {"size": 8.8}, "title": {"text": "Churn Status"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "tickfont": {"size": 8.8}, "title": {"text": "Monthly Charges"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["data_visualization_agent.get_plotly_graph()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Boxplot](img/boxplot.jpg)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Data Visualization Function <a id=\"data-visualization-function\"></a>"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/markdown": ["```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: data_visualization_agent\n", "# Time Created: 2025-01-10 08:10:20\n", "\n", "def data_visualization(data_raw):\n", "    import pandas as pd\n", "    import json\n", "    import plotly.express as px\n", "    import plotly.io as pio\n", "\n", "\n", "\n", "\n", "    # Create the box plot\n", "    fig = px.box(data_raw, x='Churn', y='MonthlyCharges',\n", "                 title='Distribution of Monthly Charges by Churn Status',\n", "                 color_discrete_sequence=['#3381ff'])\n", "\n", "    # Update layout properties\n", "    fig.update_layout(\n", "        xaxis_title='Churn Status',\n", "        yaxis_title='Monthly Charges',\n", "        title_font_size=13.2,\n", "        xaxis_tickfont_size=8.8,\n", "        yaxis_tickfont_size=8.8,\n", "        hoverlabel=dict(font_size=8.8),\n", "        plot_bgcolor='white',\n", "        template='plotly_white'\n", "    )\n", "\n", "    # Update boxplot line width\n", "    fig.update_traces(line=dict(width=0.65))\n", "\n", "    # Convert figure to JSON\n", "    fig_json = pio.to_json(fig)\n", "    fig_dict = json.loads(fig_json)\n", "\n", "    return fig_dict\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data_visualization_agent.get_data_visualization_function(markdown=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ds4b_301p_dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}