{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Build SQL Data Analysis Agents\n", "\n", "### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multi-Agents for SQL Data Analysis\n", "\n", "**The SQL Data Analysis agent** we build in this tutorial is a multi-agent that can perform SQL queries on a database and optionally visualize the results. The agent combines 2 subagents:\n", "\n", "1. **SQL Database Agent:** Query a database, perform basic data analysis, and return the results as a data frame.\n", "2. **Data Visualization Agent:** Visualize the results if a user requests a plot.\n", "\n", "This tutorial will show you how to connect up to a database and perform Data Analysis using SQL queries and visualizations, **all in one agent.** \n", "\n", "### Database\n", "\n", "The database is the Northwind Database, a sample database that is used for ERP system demos. The Northwind Database is a contains tables for Customers, Orders, Products, and more."]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "bat"}}, "source": ["# Build SQL Data Analysis Agents\n", "\n", "### Table of Contents\n", "\n", "1. [Introduction](#introduction)\n", "2. [Load Libraries](#load-libraries)\n", "3. [Setup AI and Logging](#setup-ai-and-logging)\n", "4. [Connect to a SQL Database](#connect-to-a-sql-database)\n", "5. [Create The Agent](#create-the-agent)\n", "6. [Run the Agent](#run-the-agent)\n", "7. [Response](#response)\n", "    1. [SQL Query Code](#sql-query-code)\n", "    2. [Pandas Data Frame From SQL Query](#pandas-data-frame-from-sql-query)\n", "    3. [Python Pipeline Function](#python-pipeline-function)\n", "8. [Make a Plot of Sales Revenue By Week And Territory](#make-a-plot-of-sales-revenue-by-week-and-territory)\n", "9. [Free Generative AI Data Science Workshop](#free-generative-ai-data-science-workshop)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# * Libraries\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "import sqlalchemy as sql\n", "import pandas as pd\n", "import os\n", "import yaml\n", "\n", "from ai_data_science_team.multiagents import SQLDataAnalyst\n", "from ai_data_science_team.agents import SQLDatabaseAgent, DataVisualizationAgent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup AI and Logging\n", "\n", "This section of code sets up the LLM inputs and the logging information. Logging is used to store AI-generated code and files during the AI Data Science Teams processing of files. \n", "\n", "*Important Note:* This example uses OpenAI's API. But any LLM can be used such as Anthropic or local LLMs with Ollama."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x7fb9286fd2d0>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x7fb8e8b31930>, root_client=<openai.OpenAI object at 0x7fb9286fee60>, root_async_client=<openai.AsyncOpenAI object at 0x7fb9286fd150>, model_name='gpt-4o', model_kwargs={}, openai_api_key=SecretStr('**********'))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# * Setup\n", "\n", "MODEL    = \"gpt-4o\"\n", "LOG      = True\n", "LOG_PATH = os.path.join(os.getcwd(), \"logs/\")\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = yaml.safe_load(open('../credentials.yml'))['openai']\n", "\n", "llm = ChatOpenAI(model = MODEL)\n", "\n", "llm\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Connect to a SQL Database\n", "\n", "Next, let's connect to the leads data from a SQL database. We will need to use a `sqlalchemy` connection to use the SQL Database Agent."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlalchemy.engine.base.Connection at 0x7fb9286fe5f0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_engine = sql.create_engine(\"sqlite:///data/northwind.db\")\n", "\n", "conn = sql_engine.connect()\n", "\n", "conn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create The Agent\n", "\n", "Run this code to create the agent with `SQLDataAnalyst()`. This will create a multi-agent that can perform SQL queries on a database and optionally visualize the results. This agent combines 2 subagents:\n", "\n", "#### SQLDatabaseAgent\n", "\n", "The `SQLDatabaseAgent` is a subagent that queries a database, performs basic data analysis, and returns the results as a data frame.\n", "\n", "#### DataVisualizationAgent\n", "\n", "The `DataVisualizationAgent` is a subagent that visualizes the results if a user requests a plot.\n", "\n", "Run this code to create the multi-agent:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAR0AAAGwCAIAAAAbiiYcAAAAAXNSR0IArs4c6QAAIABJREFUeJzt3XdYU1cbAPCTHTLZe4kIKMhQUETr3qJVq1YFt1ZbZ9W2rlq1jtaq1RZX3XvgQhygOFFUXCCoIIiKbEiAkEB2vj/iR6mGiPEmN4H39/TpIzc357wZb8655957DkGlUiEAAKaIeAcAQCMEeQUA9iCvAMAe5BUA2IO8AgB7kFcAYI+MdwDgc5XkSoSV8mqBXC5TSWqUeIfTIDQzIoVGZHBILA7FxoWKdzjYg7wyVS9ThS+fiF6lC91aMRVyFZNDtrCjEEl4h9VgJW/FIoGCQiPmZog8/FgerVnN/Bh4B4UZApwXNjkZ96uSYstcvBluPoxmfiwKjYB3RJ9FXK18lSbMzxEXvKzpONCqeQAL74gwAHllSgR8+eWDRVwrSthAawbHdNqmhqkskyXFlinkqHekHdXMtI/8Ia9Mxqt00c1TpV9OczK3peAdix6VFUhP/Z0XPtnBsbkZ3rHoDvLKNBTmiB9dLR8w2QHvQAzk1N95XYfbWtqb6pAG5JUJeHpXkJMmGjilqSSV2qm/8wO7mnu0ZuIdiC5MuxfbFBTnSp7eqWxqSYUQGjrT6VZMqYAnwzsQXUBeGTW5THXnXNmI713wDgQfkQvcrh4rxTsKXUBeGbVbZ8o8G8W4s26IZIKTJ/3uBR7egXwyyCvjJeDL3zwX+XXk4h0InkJ6W6beqJBJTGwUAPLKeD1JrOg81MYwdQmFwoyMDLyerl3X4baPr5XrqXA9gbwyXk8SK9xaGujSnpEjR8bExOD1dO2cvRjpdyr1VLieQF4ZqdyMamdPBpFkoGuUpFKpbk9Un6fR+ekNweSQWFxySa5Ef1VgDvLKSOVn17Row9ZHyXv37u3fv3+nTp0mTZqUnJyMEAoPD+fz+dHR0cHBweHh4erdzp49GxkZGRoa2r1798WLF5eXv+uJ/f77771797558+aQIUOCg4Pv37+v8enY8gnmvH1RrY+S9QSuZzdSJW/Fzi2wv5AnOTk5Kiqqb9++YWFhSUlJ1dXVCKG1a9fOmDGjbdu2ERERVOq7SxzS0tLc3d379+/P5/OPHj0qEok2btyofkgoFG7ZsmXBggU1NTUhISEan44tBoeUnVKjj5L1BPLKSIkECiYX+0+noKAAITRixAh/f//+/furN7Zq1YpMJltbWwcGBtbuuWjRIgLhXS+UTCbv3r1bIpHQaDR1r2/JkiV+fn5ano4tBocsqpLrqXB9gH6gkRIJ5AwO9nnVqVMnDofz888/37p1S/ueMpls//79I0eO7Nq165kzZ5RKZW1XkE6n1yaVYTDYpGqBwpA1fibIKyNFpRFJehi0sLa23r17t5ub25w5cyZNmlRSUqJxN5VKNWfOnN27dw8aNCgqKkrdsimV725GZjAMfQMiiUSg0Ezpu2pKsTYpJDJBWKmXno+7u/tff/21devW7OzsZcuW1W6vewX2o0ePkpOTFyxYMHr0aD8/P09Pz48Wq9cLuIWVcjLFlG7fhLwyUkwOuVqgl7xSj4mHhIR88cUXtSdzzczMysrKavepqKhACPn4+NT9s7a9+tB7T8dcTZWCwTal+zhh3MJI2brRxSLsJ4F5+vTpTz/9NGLECAaDkZSU1KpVK/X2oKCguLi4vXv3cjgcf3//1q1bU6nUqKioIUOGZGVl7dmzByGUnZ3t7Oyssdj3nt6Q9u2TSMRKaycatmXqFaluTwAYD6lYmfVY2CIQ44tuKysrX7x4cenSpeTk5DZt2ixatIjFYiGE/P39MzMzL1y4kJGR4evr6+fn5+HhERsbGxsbK5fLV65cWVJSkpKSEh4efvv27VevXo0ZM6Zuse89vVmzZtiGfTumzLsth2NlMjdKw32NRkohV21fkPPduuZ4B4I/mVS16+ecab+b0lsB/UAjRSITfELY+S/FTs3p9e2zadOm06dPf7i9ZcuWz58/1/iUPXv2YN6YvOfWrVtLlizR+JCzs3NeXt6H2//55x8vL6/6CnybWe3XwcQu6of2yngVvRYnnikbPkfzIY26UycSiT7cTiDU+7Ha2tqSyfr9MRWLxXw+X+ND9QVmY2NDodTbxzuw6s2gqY5ca5PpBEJ7ZdTs3elMNiknTVTfHA9cLpfLNbofcjqd7ujoiFVpT+8KnDzNTCupYJzd2IUNss58WIV3FHjKeSIKC7fGO4pPBnll1MxtKM1bMy8dKMY7EHzEbCsI6MKlM03vW2p6ETc1Xm3ZLHPyrTN6POtqnK4cKXH1Zrh6m+Sk7TBuYRqeJgkqymQdB1nhHYiBXD1a4taSYbpztUN7ZRp8wzg0M+K5HQV4B6J3Crnq5F95Ns40000qaK9MzOunomvHSwO6cNt0t8A7Fr1IjuO/fCLsOtzWoVm9Z+1MAuSViVEpUdK5suf3BEHdLNxaMkzrqrn6FOdK8jKrky/x2/awCOljSTClK9c1g7wySWKRMu1W5csnVTUipVcbFoFAYHJIHCuqQmEa6zUSSQRBmUwkkBMQ4fl9AduS3CKA5f+FOcmkbgbRAvLKtAkr5AU54qpyWbVAgQhIWIHxrSW5ubkkEsnJyQnbYllcMoGAGBwSx5Li5GlmxjKle0AaAvIKaBMVFcVkMidMmIB3ICYGxgMBwB7kFQDYg+tugTZsNtvMzITXI8UL5BXQpqqqSsu0FqA+0A8E2lAoFC13RoH6QF4BbWQymUxmkiuR4gvyCmhDo9HUc0eDTwLHV0AbiUSi7/v2GyV4y4A2HA4HxgN1AHkFtBEIBAqFKS04YCTg+AoA7EFeAW0oFAocX+kA8gpoI5PJ5HJTWtDNSEBeAW0oFIqeljZt3CCvgDYymUyva903VpBXAGAPDkmBNiwWC85f6QDyCmgjFArhjnIdQD8QAOxBewW0gfsadQN5BbSB+xp1A/1AALAH7RXQhs1mMxgmuaIHviCvgDbQD9QN9AMBwB60V0AbGA/UDeQV0Ab6gbqBfiAA2IO8AtrA/IG6gbwC2sD8gbqB4yugDVzPrhvIK6ANXM+uG+gHAoA9yCugDY1Gg/ktdAD9QKANzCOtG3jLgDZw3a1uIK+ANnC9hW4gr4A20F7pBvIKaAPtlW5gPBBoY2ZmBuvK6YAAZ/3AhwYNGoQQUqlUQqGQSCQymUyVSkUgEM6ePYt3aKYB+oFAAwcHh+TkZBKJpP6zsrJSpVJ17doV77hMBvQDgQbjx4+3tLSsu8Xa2nrs2LH4RWRiIK+ABh06dPD09Ky7xdfXNyAgAL+ITAzkFdBszJgxHA5H/W8rK6vx48fjHZEpgbwCmnXq1Mnb21s9rOXr6+vv7493RKYE8grUKzIyksvlWllZjRs3Du9YTAyMBzaUXKbiF0qFlXKlsqmcmbBjBQR49qHRaEzUPDtViHc4BkIkENiWZEt7KolM0LkQOH/VIMnx/KxHQhKFYGFLk0rh+oPGjG5GKisQIxVq2Y4d2NVct0Igrz4u8UyZQk5o28sK70CAQd09V2phSw7uZaHDc+H46iPunOepVERIqiYoNNymvESWcr1Ch+dCXmkjEigKXoqDuls2YF/QCIWG22bcr1LIP7lPB3mlTXmRFOl+7AoaA5UK8Yukn/osyCtthJUyS3s63lEAPFk70QW8T55BEfJKG6UCSSUKvKMAeJLUKHQY2oO8AgB7kFcAYA/yCgDsQV4BgD3IKwCwB3kFAPYgrwDAHuQVANiDvAIAe5BXAGAP8goA7EFe4en6jYRuPYJzc19/0rPy8t926xF85Wr8R/d89jxdIpF8dLes7MxuPYLv3En8pDBMi1AofJGVYbDqIK8arbj42OkzxovFNXgHYhQmfzPy4sUYg1UHedVoNaSlMiF5ebmf83Sp9JPvofocMB8Txg4f2Xsm5nhVlcDT03v8uKlt27RDCBUU5m/fvunR42QymdK714DMF8+6de395aBhDS+2oqJ885b1t5NuUKm0oMDg2u0lJcW79my5d++2SCR0cXEbPWpCzx591Y3Vxk2/IYQGD+2JEPrpx1/69hmYlpZy4ODOtPQUhJCPt++0aXO8vVrWFnX1+qVt/2wqKirw9PSeOmWWv3+QlvLre6WFRQVbtmx4+OgelUrzauEzceJ3Pt6ttLwuLeXzeGV/R/3x8OE9MoXStm37mzevbN96sFmz5gihmLMnjkcfLCsrsbd37NG979cjxtBotKzszJmzJv62+q9/dv798uULOzuHqVNmdezYBSE0cnR4eTn/TEz0mZhoOzv7o4fPffoH+2kgr7CUlpayY2dUjx5924eEJd9PqqmuRgjx+bxZsydJxOIRI8bY2drfSLySmvqoW9feDS9WKpXO//G7/Py3I4ZH2ts7xsRE1z4kV8gzMp5+OWgYl2N+89bVVauXODm5tPTxbd+u44jhkcejD65ZtZHJZDk7uyKEiooKJFLJmMjJRCIxJiZ6wcJZRw7F0unvbtx8/erlsK9GC4VVJ08dmffDt5v+3NGqVev6yn/4KPnDV8rjlc2cNdHJyWXG9PkEAuHSpfOz50zetuWAOhk0qq98hUKxaPEcfjlv9uwFfH7Zjp1RQYHB6nL27vsn+sTBoUNGurl5vH37+tjx/Xn5uYsWrFA30ct/XTBzxg8O9o579m5buXrx0cPnuFzzZb+s/fGnGYEBbYcPi6AYZBlyyCsslZQWI4SGfDnC19e/V6/+6o1Hj+3n8co2R+1t1dIPIdS+fUd1G9JwZ2KOv3yZ9cfazcFt2yOEfFv5j5vwrq1zdHDauzuaQCAghPr1+3LIVz1v377e0sfXwsLS0dEZIdSypR+X+26yrp49+9VG5e3dau68aWnpKSHBoeotEyd826HDFwihXj37j584bOeuzRvWb6uv/KKigg9f6YGDOy3MLdf/sVW91Hevnv0jxw4+d+H0zOnz63tp9ZX//Hn6i6yMX5b+1rVLT4RQbu7ri3FnpVKpQFB56PDuJYtXdencQ12ClZXNnxvXzPh/FTNn/NC9W2+E0OTJM6ZOi0x98qjzF919vFuRyWQrK+vWrQM/6Z3XGeQVltq2acdmc1av+XnmjB9CQzupNz56nOzVwkedVLpJvHXNw8NTnVQIIeL/l89Ry375Yu++7ZmZzxBCCoWCz+fVVw6BQEi8de149ME3b16pVzct17SztbVNp47dEq5clMvlZDJZY/mh7Tt9+Erv3btdUlrcP/yL2qJkMllpSbH2V6exfPUvlPqnASHk7OyqVCpraqofPrwnl8tXrV6yavUS9UPqifrKSkvUf5rRzdT/sLNzQAiVlZU24A3GHuQVlszNLaL+2r1564aFi+f4+QUsXbLGxsa2qkrQooXP5xRbUlJUXwmPHt//acHMoMDgH3/4hclgLl32g1JV77Sh+w/s3LN321dDR30zeSaPX7Z8xYL6draxsVUoFGKx+EXWc43lW1lZf/hK+eW8Dh2++GbyzLpFMZksLS+tvvidnFzU/WqvFj4IoefP062tbbhccx6/DCG0etVGWxu7uuU4Ojq/ev2y7hYKmYIQUirxmUYB8gpjrq7uv6/569Hj+0t/mf/72mXr/thiZWXD+7xfTXOuRXk5X+NDBw7sdHR0Xr1qo7rrVftrXat23lWJRHL4yJ4B/QfPmD5PPWCgpcbycj6dTmcymVrK//CVstmcysoKV1f3hr+0+sr39moZEhz6z46/iosLKyrLbyfdWLJ4FUKIzebU1t7wWt57KwwAxtkxph7PbRMUEhr6hfpEpLdXy4zMZxpPSlIpVISQQFCpvcwWLXwyM5+9ffvmw4cqBRWezb3UX0qpVFpdU127zLb6O1rbERKLayQSidf/BwArBRUIIY1rcovF4rv3bgUGBhMIBC3lf/hK27Rpl56emvnieW1RNTUfOXumpfyZM35wdnZ9m/fGnGsR9fce9YFWUFAIgUA4feZYw6uofTd4vLKG7IkJaK+wlJ39YsnSuYO/HGFmxkhOTlIPMX89YuyFizHzf/hu+LAIGxvb5OSk2v2beXgSicQ/N62ZMX1+3dHz94waNf7S5fOzv58y7KvRVpbWV67G1T4UGBgcHx974WIMh82NPnmoqkrw+tVL9VrAvn4BJBIpasu6fn0GSaSSQQO/8vDwPHX6qKWllUgo3Lf/HyKRmJOTXVvUzt2b+eW86mpRXHysQFA5ftxULeVnZD5bvuKn917puLHf3L1764cfp48YHmlhYZmcnKRQKlauWK/lHauvfIVC8d2MccOHRTo5uRAIhKoqgVAoZLFYzk4uQ4eMPHnqyKIl33fq2JXHKzsTc3zN6k1eH+tpt24ddOVq3OEje9lsjm8rfw8PT+37fybSsmXL9FqBSSvNkwj4chdvZgP3r6oSZGVlXLt26dGj5ICANt/PWcRkslgsdoB/mxdZGdevX05/muru5pGdnRka2snHuxWbxXawd3z0+D6RQKwdl/sQh83x8wt8/izt+o3LL1++CAho+/Tpk86de3g08/RtFfDmTc6p00dTUh907dJr6OCvr16Lb9HCx8HBicPm2NjYXb9++c6dxKoqQZ8+4QH+be7du30m5vjbvDdTpsx0cXGLjT05fFhERWW5etzs1Omjd+4kOjo6L1qwQj3QUl/5TAbz5csX771SDpvTMazLm9xXly+fv//gDpPJGtB/sLu7h5Z3rL7ynZxcsrIyzp0/df1Gws2bVy4nXDh37mSHDp3NzS1CQjowGMw7dxKvXovPy8/tGNYlrENnMzMzPp8Xe+5Uj+59XVzc1EMmh4/saRfSoVWr1gghX1//7OzMywkXsrIyfHx83VybNfAzffNMaO1ItbT/tNF5WPdAm2d3BW+zxWEDbTEss7KyYvDQnnNmL/ik88JNkEKhUC8crlKpCgrzJ08ZOWJ45ITx0wwcxo3oIp9glmegttGXD0E/0FjMmjP51avsD7eHhXVZ+NNyPCLCjA4vTSKRfDdjnK2tfYB/GwqFmpb2WCwWN2/upf9gsQF5ZSyWLlkjk2uYr/jDIT6To8NLIxAIvXsNuHo1fs/ebVQqtVkzz1+W/tb5i+56jhQz0A/URh/9QGBadOsHwjg7ANiDvAIAe5BXAGAP8goA7EFeAYA9yCsAsAd5BQD2IK8AwB7kFQDYg7wCAHuQV9pQaEQaHd6iJo3OJFFon/wdgC+NNpb21PzsaryjAHjKzRBaO37y1GiQV9pYOVAZHJJYhM/cIwB3lWVSOxc6k/vJt31AXn1E5yE2Vw4X4B0FwIFSobp+rKjrcBsdngv3iXxcRans0G9vQvvbsq0oHHOKEt6xRo1IJFTypFXl8uSLJeN+bsbkkhrwpPdBXjWIUoGS4/mFr2pkEqVYVO8EfY2PRCIhEAhUg8y9bCSYXBKJQnD0MGvXx1LnQiCvgDZRUVFMJnPChAl4B2Ji4PgKAOxBXgGAPZg3BmjD4XDMzEx+4hrDg7wC2ggEAoUCTt99MugHAm2YTCa0VzqA9gpoIxKJ8A7BJEFeAW1YLJZ6BTrwSSCvgDZCoRDOcOoAjq+ANnB8pRtor4A2cHylG2ivAMAetFdAGxaLBf1AHUBeAW1g3EI30A8EAHuQV0AbIpFIIBDwjsL0QF4BbZRKJfQDdQB5BbQhk8lkMhyEfzLIK6CNXC6Xy+V4R2F6IK8AwB7kFdDGzMyMRqPhHYXpga4z0KampoZIhB/fTwZvGQDYg/YKaAPXMekG8gpoA9cx6Qb6gQBgD9oroA3Mc6YbyCugDcxzphvoBwKAPcgroA3Mb6Eb6AcCbWB+C91AewW0oVKpFAoF7yhMD+QV0EYqlcpkMryjMD2QVwBgD46vgDZwHZNuIK+ANnAdk24gr4A2sO6BbiCvgDbQXukG8gpow2az4fhKB5BXQJuqqiqlUol3FKYH8gpoA+2VbgjQewYf6tmzJ4lEUqlUYrGYSCTSaDSVSkWhUM6fP493aKYB2iuggZWVVXZ2du0M0urRizZt2uAdl8mA6y2ABhEREe9Nb2Zubh4ZGYlfRCYG8gpoMGjQIHd397pbPDw8unTpgl9EJgbyCmj29ddfU6lU9b+5XO6YMWPwjsiUQF4Bzb788ktXV1eEkEql8vT07Ny5M94RmRLIK1CviIgIKpXK5XJHjx6NdywmBsYD/0uF5DJVtUAOJx8QQp079D3ueo7L5Qb6hlWWwV1YCKkIbEsSkfTxhfbg/NW/Mu5Xpd6s5BdL2FyKQgFvC3gfjUHiF4nt3c0COps392dq2RPaq3ceX68ozJF0HmbPMof3BGgjLJcnx5WKq5W+oez69oH2CiGEHiaUlxXKwwbZ4B0IMBk3oovcWzH8wjgaH4VxCySskOfniCGpwCfpMtw+K0Uok2huliCvUGm+RAVXbINPJxMrywokGh+CvEJVfLmtKx3vKIDpsXdnCHiah0khr5BMqpTWQIMFPllNtVwug34gAIYCeQUA9iCvAMAe5BUA2IO8AgB7kFcAYA/yCgDsQV4BgD3IKwCwB3kFAPYgrwDAHuQVnoRC4YusjM8v59nzdIlE84XVn0kul0eOHbJ120YMy7x+I6Fbj+Dc3NfqP3Nysgd92e3W7eu6laZQKNLSUupu+cwCMQF5hafJ34y8eDHmMwuJi4+dPmO8WFyDUVD/QSAQ2GwOna7H6/3JZDKLxSaTdLxN+4/1v27YuBrDAjEB95x/LpVKVTvf8qeSSqWfH4CeWio1Eom0dfM+/ZWPEHJ1dT986KzOT5d+8PI/s0BMQF7pYsKkEc3cm7u7Nz91+qhEIo4+FsdisS5dOn/oyJ6CgjwrK+sB/YdEjJ5AJBIfPLz3w4/TN/+9p1Wr1urn9hvQacjgr7+ZMnPk6PDycv6ZmOgzMdF2dvZHD59T7xBz9sTx6INlZSX29o49uvf9esSY96Z0risuPnbjpt8QQoOH9kQI/fTjL337DFT3DLdt35iZ+YxONwvr0Pnbb7/nsDXfMY4QOnps//Z//tq/96SLi5t6y/dzp9bUVP/yy++jIwYhhCIjJk6a+J1YLN74129JSTcRQv7+QTO+m29v7zBz9iQzutna36PUTzx2/MC27ZviLtym0WgX486eOXM851W2mRmjXUiHGdPnm5tbfBj/72uXI4T+WLs5uG37yDGD8wvy6u5gY2N7/OiFtLSUAwd3pqWnIIR8vH2nTZvj7dUSIfTb2mXXrl9GCHXrEYwQOnzobGrqw7oFankrliyd5+LsRiaTz50/LZfJQkM7zZ61gMVi6fql+A/IKx3dv39HLBGvXvlndU01i8WKjz/329plPXr0nTTxu2fP0nbv2YoQGhM5SUsJy35Z++NPMwID2g4fFkH5/8yye/f9E33i4NAhI93cPN6+fX3s+P68/NxFC1bUV0j7dh1HDI88Hn1wzaqNTCbL2dkVIfT6dc68+dPc3Zv/+MMvlRXle/ZuKykpWr9ua32F9O0zcNfuLQlXLk4YPw0hVFxclJL6cP68JRbmlr+uWLd8xQL1boeP7ImPPzdh/DQrK+v4S+c+un7Ps2dprq7uvXr1Ly/nnzp9VFQtWrPq/eO0oMCQb6bM/GfH3+o/x4+fJhIJ1f9+npEeH39u1owfEUJFRQUSqWRM5GQikRgTE71g4awjh2LpdHrk6ImlJcWFhfkLF6xACFlZWr9XoPa34nj0we7deq9etTH3zat1G1ZaWdlMmzpb+4tqIMgrHZHI5J8Xr1Z/t1Qq1c7dm1u3DlyyaCVCqPMX3auqBEeP7ftq6CgtJfh4tyKTyVZW1q1bB6q3lJWVHjq8e8niVV0691BvsbKy+XPjmhnT59fX2lhYWDo6OiOEWrb043LN1RsPHtpFJBLX/h7FZrERQmw2Z/VvS1NTHwUEaF4QxNzcolPHrgkJ7/Iq4cpFFovVo3tfOp3eqWPX2l5uYVGBmZnZ6FHjyWTygP6DP/oWzf1+Ue1zyWTywUO7JRLJe22vnZ19gP+/UfXs0Vf9D7FYfDz6YNcuPTt16ooQ6tmzX69e/dUPeXu3mjtvWlp6SkhwqLOzK5drzi/n1b6H7xWo/a1wdnZdtPBXAoHQ0sf35q2r9x/cwSqvYNxCRy1b+tX+YOfl5ZaVlXb+onvtoyEhHaqrq/Pycz+pzIcP78nl8lWrl/Tu20H9399RfyCEykpLPqmclNSHQUEh6m+SOhiEUOaLZ1qeEh4+tKAwPz09FSF06fL5Xr0GfDhW0bNHP7FY/NOCmTk52Q0JQyaTHT22f9KUkQO/7Hr+whmlUllRUd7Al7BjV1SVoHLmjB/UfxIIhMRb12bOnjRocPff1y5DCJXzeQ0pR/tbQafRazPfzs6hrKy0geF9FLRXOjKj/9sLEoqECCFzc8vaLWw2R50P1PoPjT7E45chhFav2mhrY1d3u7pFajiRSGjO/fdI5l0wWr80bYJCnJxcEq5cJFMoubmvl/+y9sN92rcLW7N607btGydNGTmg/+A5sxeQyfV+f1Qq1aLFczJfPBs39ptWrfwTE68ePbZf2bAJetLSUk6fPvbD/J8tLa3UW/Yf2Lln77avho76ZvJMHr9s+YoFDSyq4W8FhUxRKhUNKbMhIK8woE6DysqK2i3l5Xz1pyiVfWTEr+78jez/d/ZcXd3rf8bHy7G2thUIKt8LhsWqdxJJdYMwoP/go8f2q1Qqf/8gd3cPjbu1bxcWEhx68tSRLVv/tLNzGBM5qb6x0NTURw8fJS9etFLdtcvPa2jTLRaLf/9jeVBgcL++g9RbJBLJ4SN7BvQfPGP6PIRQSUmxltf+Hh3eCkxAPxADVlbW9nYOycm3a7fcuJFAp9M9Pb0tzC0RQmW8dz+QPF6ZTPbvDD5mdDMer6z2z6CgEAKBcPrMsdotNTUfPyulbjnr/gb7+vqnpD4Ui8XqP2/evIIQqj0CqU+/voOqq0Wx504NGjhM4w7qswJEInH4sAhra5usrAyEkDnXQt3MqhUVFaj/USmoQAh5tfCp+6d6CXAqhYoQqvt1r2v3nq08XuncuYtrt4jFNRKJxMur5YdFIYTodDM+n1f2pEUDAAAgAElEQVTf4uK6vRWfD9orbIwfN/W3tcv+WPdrSEiHR4+Sb92+Pm7sN2ZmZq6u7nZ29gcP7rIwt6yuqd61a3Pdb0Dr1kFXrsYdPrKXzeb4tvL38PAcOmTkyVNHFi35vlPHrjxe2ZmY42tWb6r9dmrk6xdAIpGitqzr12eQRCoZNPCryNETr16N/2nhzIHhX5WUFO3b/09QYHBgQFvtL0E9evE45UHdA8W6Tp0+ejvpRq+e/Xm80rKyUm/vVuojlsQ/rx2PPhgYGJyUdOP8hTPqnVu1bE2lUnfsjBowYEhOTtbhI3sQQq9ysp0cnZt5eBKJxD83rZkxfX5QYHDdKp4+fXLi5GF//6AHD+4++P/G8AFDPDw8T50+amlpJRIK9+3/h0gk1h7jBfi3uRh3dsOfq1v7BbLZnLCw/6wnpNtb8flIy5Yt03cdRq7wlVgqVjl4MBr+lJiz0Rbmll269Kzd4unpZWFhefXapYtxZyvK+aNHT4iMmEggEIhEop9fYPL9O8ejD2ZlZYwfOzXpzs2WPn5t27ZX/5pmZ2deTriQlZXh4+Pr5tosJKQDg8G8cyfx6rX4vPzcjmFdwjp01j6izWFzbGzsrl+/fOdOYlWVoE+fcA6H29ov6P6DO7HnTma+eN6ta+8f5i/VchKsFpvNYTFZ7UI61N144OAuP9+ANkEh/HJeasrDhCsXX7/J6ddv0PhxU4lEYnOPFhKJ+GzsiYtxMTbWdsFt26elpURGTOJwuO7uHnHxsXHxsXK5fPGilWVlJenpKX36hLNZbAd7x0eP7xMJxJDg0NLSkgsXY3r3GuDo6Lxw8ezycn5xceHde7dq/xs9akKboHb37t0+E3P8bd6bKVNmuri4xcaeHD4sgkQieXh4VlVVXrkal/rkEZdr3rZNu7oFankrrl67VC0SDQwfqn6ZDx7czcrOGD1qfMO/Bm9fiDiWZFsXDW8szM+OHl4pF1Yo2/S0wjsQYGKSYkucm9N9O2g4BQL9QBNw9+6tVWuWaHwo6q89bm7NGlKIUCgcFRGu8aGp38wOHzDk82IE/wF5ZQICA4P/2X5Y40M21rYNLITBYNRXCIfN/YzogAaQVyaATqc72Dt+ZiFEIvHzCwENBOPsAGAP8goA7EFeAYA9yCsAsAd5BQD2IK8AwB7kFQDYg7wCAHuQVwBgD/IKAOzBdUyISiNSPzKzEAAaMBgkMlXz7dLQXiGuNaXolV4miwWNW35OtYUNVeNDkFfIzpVOgLcBfDoqjWij6aZGyCuEEKIxiF5BrGtHC/EOBJiSS/vyAzpz65tBHO4Xfic7RZR6syKouxXXmko1g58boJlYpBCUye5fKu040MrFu965GyCv/pWXVZNyo6Igp0alQkq5Ub8tKpVKpVIRiY0n/5VKJYFA0HkFCcMwY5PkUpVzC7M23SxsXbXNFwJ5pYFCrkJG/K4UFxevX79+7VoNU2eaLrlc/v33369cuZLLNd6bl1UIkSkNynzIK1OSnZ1Np9OZTKaFxfsLczQOVVVV+fn55ubm9vb2eMfyWRpPR6LRS09PX7x4sb29fWNNKoQQm812d3efPHny69ev8Y7ls0B7ZQLUy3A8ePAgODi4Abs3BqmpqZ6enkwmE+9AdATtlbHLycnp1q0bQqjpJBVCKCAggEqlhoaGlpR82loqRgLyyti9ePEiKSkJ7yhwQKFQEhMTnz3TtryQ0YK8MlKFhYWzZs1CCPXt2xfvWHBDoVC6du2KEJo1a1Z9KxsYJ8grIzVv3rylS5fiHYWxmDZtWkREBN5RfAIYtzA6ly5d6t27N95RGKnExMQvvvgC7yg+DtorI6JSqQYMGODu/smLyjUdpaWlO3fuxDuKj4P2ylgUFxdzudyKigpTPyWqb1evXu3eXfMKXcYD2iujEBUVVVhYSKfTIak+Sp1UGzZswDsQbSCv8Pfs2TMWixUYqPfFORuTCRMmhIdrXnbIGEA/EE9CobCsrMzc3Nzc3BzvWExPTU2N9pUscQTtFW4qKioGDBjg7OwMSaUbdVLNnTvXCE9tQXuFj+rq6pSUlLCwMLwDaQzmzJmzceNGvKP4D8grHJw6dapLly5WVrCicaMF/UBDy83Nff78OSQVtt68ebNgwQK8o/gXtFeGlpOT4+HhgXcUjVBKSsr9+/enTJmCdyAI8sqgrly5IpFI+vfvj3cgQO+gH2gg8fHxhYWFkFT6tnv37szMTLyjgPYKNDp9+vS5cOECiUTCMQZor/QuOzt7/fr1eEfRhMTHx+ObVJBXeieRSKKioubNm4d3IE3L/fv3s7OzcQwA+oGgcWrXrt2dO3fwarggr/To3r17BAKhXbt2eAfSFJWVlQkEArxOaUBe6UthYeGUKVPOnTuHdyAAB3B8pS8KheL48eN4R9Gk3b59G6+LMCCv9EIsFjMYDAaj3vUmgAF07NiRw+G8evXK8FVDP1Avvvvuu3HjxrVv3x7vQAA+oL3CXn5+vqOjIySVkbh+/bpEIjFwpdBegUZuy5YtdDp94sSJhqwU2ivsJSQkSKVSvKMA70RERBj+QBfyCmOZmZm7d++mUjUvkw4Mj8vljhw50sCVQj8QY+np6WKxuEmt/WH87ty5I5FI1FO9Gwa0Vxjz8/ODpDI21tbW27ZtM2SNkFcYi42NraysxDsK8B8tWrT49ttvxWKxwWqEfiDGOnXqlJCQQKfT8Q4E4AnaKyyJxeLZs2dDUhmhBw8enDhxwmDVQV5hiU6nDx8+HO8ogAaWlpbHjh0zWHXQD8RSRUVFUlISTGJhnFJSUlq3bm2YO7KgvcJSSUnJgQMH8I4CaBYYGGiw2xyhvcLAqlWrTpw4QSAQiEQiQqh2uvBHjx7hHRr41969ex0dHQ2zFia0VxgYPXq0m5ubOqkQQkQikUAghIaG4h0X+A8mk2mwXzqyYapp3Jo1axYaGlp3uInD4UyaNAnXoMD7Bg4cWFVVZZi6oL3CxvDhw11cXNT/VqlUfn5+bdu2xTso8B90Ot3GxsYwdUFeYaN58+YhISHqf1tbW0NjZZy++uorwyyWBXmFmVGjRjk7OyOEvL29YVFT46RQKAoKCgxQERxfYaZZs2YhISECgWDcuHF4xwI027Ztm2FWx/zIOHvJW8mjaxVFr2tqqhQGiMbUqVQqlVJFJEEv4ONsXekyidLNh9EhvBEuBaYtr149rb57gRfQxcrCjmrGwnnCa9DIEAiE8mJJJU96N7Zk0q8eZCrBAJUePHjQ0tLSABfE1NsPfHZXkPlQFP6Ni74jAE2WlSPNypHm0oK57aeXM/70NECNcrk8JyfHABVpbq9qRMr4/UU9RjsaIAIA8rOri1+JugzT+yC4WCyWSqUcDkffFWk+Eih6VUMgGKJdBgAhZO1If/HYEGds6XS6AZKq3rwS8OX27mYGqB4AhBCNQbRzMxPy5fquKCUl5fvvv9d3LfUeX0mqFVLD3bMMAOIXSQxwvpZGo5WWluq/HjgvDJqSli1b7tu3zwAVQV6BpgXuawQAY5WVld27dzdARZBXoAmh0+mGme0M8go0ITQaLSkpyQAVQV4BgD3IK9C0dOzY0QBdQcgr0LSQSCSFQu83Z8D9V6BpuXbtmgGG2qG9Ak0LnL8CAHuDBw/m8/n6rgXyCjQtYrHYAMdX+sqrlauXjB3/1Ud3KyoqLCzSyzwecrk8cuyQrds2Yljm9RsJ3XoE5+a+xqQKhUKRlpZSd0tOTvagL7vdun0di2Dxob8PFCvHjx+3stL7nf94tlf5BXmjIwdlZj7TR+EEAoHN5uh1yZzPrOKP9b9u2Li67hYymcxisckkUx1M0usHihUOh1M7M7H+4PkRKuRy/c0OTyKRtm7W75XLn1mFVCJ5b4urq/vhQ2c/Oy7dqVSqgsJ8J0dn3Z6u1w8UK2PHjt2wYYO1tbVea8Eyr65eu7Rv/z/FxYXubh51Zz+8GHf2zJnjOa+yzcwY7UI6zJg+39zcorCoYNyEYQih5SsWLEeoT5/wBT8uk0ql+w/suHo1vqS02MrKunevAePHTdUygPM84+l308fNm7s4fMAQ9Za9+/45fGRP1F97pn4biRCKjJg4aeJ3YrF441+/JSXdRAj5+wfN+G6+vb3DzNmTzOhma3+PUj/x2PED27Zvirtwm0ajaQz4vaoLiwpGRwyqrSJyzOD8gry6O9jY2B4/eiEtLeXAwZ1p6SkIIR9v32nT5nh7tUQI/bZ22bXrlxFC3XoEI4QOHzqbmvrw97XLEUJ/rN0c3LY9QojHK9u67c97ybflcnlrv8BpU+d4eHgihJYsnefi7EYmk8+dPy2XyUJDO82etYDFYmn5aEpKinft2XLv3m2RSOji4jZ61ISePfqqH3r2PH3zlvU5OVlWltbuzZpnZ2fu33uKSqWKxeKduzZfuRonlUpcnN1GjBjTvVtvhNCJk4evXrs0fFjErl2befyyFi185s9d4urqrvED/fQvkd7x+Xy5XO83UGKWVwlX4latXhIUGDxieGRRUcHhI3udnN7NOfPsWZqrq3uvXv3Ly/mnTh8VVYvWrNpoZWm9eNHKVauXTBg/LSgw2MLCUt0CPHx4r0NYZ0cH5+zszIOHdrPZnBHDI+urtKWPbwtP70uXz9fm1eWEC1269HR1df91xbrlKxaoNx4+sic+/tyE8dOsrKzjL50zM/vIrdAaA35vHwtzy7pVjB8/TSQSqv/9PCM9Pv7crBk/IoSKigokUsmYyMlEIjEmJnrBwllHDsXS6fTI0RNLS4oLC/MXLliBELKytA4KDPlmysx/dvytLkQsFs+dP00gqPxmyiw6jX7k2L6586cd2H+azWIjhI5HH+zerffqVRtz37xat2GllZXNtKmztbwiuUKekfH0y0HDuBzzm7eurlq9xMnJpaWPb3Fx0fwfvm3RwmfxwpX3km+fO396yuQZVCpVqVQuXvJ9UVFBxOgJ5uaWKSkPfl25SCyu6d/vS4TQ8+fpx48fmDdviVwu37Bh1Zrff9m6eZ/GD9QI7dixQ9+NFWZ5JZFIojav8/cP+mPtZnXzkp//NvvlC/Wjc79fVDtbBplMPnhot0QiodFoXi181J2f1q3fzQ5LIpG2bN5Xu3NBYd7NxKta8gohNGDAkI2bfisqKrS3d3j69ElBQd7Cn5bT6fROHbvWllNYVGBmZjZ61HgymTyg/+CPvpz6Aq67z3tV1P78i8Xi49EHu3bp2alTV4RQz579evV6N6uWt3erufOmpaWnhASHOju7crnm/HJe7Wu3s7MP8G9TW/7lhAu5ua/Xr9vaJigEIdS6ddDoyEGnTh0dN3YKQsjZ2XXRwl8JBEJLH9+bt67ef3BHe145Ojjt3R2tjrZfvy+HfNXz9u3rLX18LydcqKmp+eXn3ywtrTp27JL65NHde7dGjxp/M/Hqk7THRw7FWlvbqF9dTU31yVNH1HmFEFq18k9LSyuE0NChI7ds/bNSUMnlcD/8QI2Qg4ODAWrBJq/S0lMqKyuGfTW6ts9GrNN5k8lkp04fvZxwoaSkiEajK5XKiopyOzt7jUWVl/P3H9hx/8HdqioBQkj986xFj+59t23fmHDlYmTExEuXz3t4ePr5Bby3T88e/a5ciftpwczp381Td6W0+6SA37NjV1SVoHLmjB/UfxIIhMRb145HH3zz5hWDwUAIlfN5DSknNfUhi8lSJxVCyN7ewdXVPfPFuyEBOo1em9J2dg7p6akfLTD75Yu9+7arBxUUCgWfz0MIlZYWM5lMdYYQCARHR+fi4kKE0N27t+Ry+ejIQbVPVygUTOa/XU063ay2doQQr6yUy+E25HXhbvbs2UuWLNH3AgjY5FVJSRFCyN5ew7xoKpVq0eI5mS+ejRv7TatW/omJV48e269UaZ7LgM/nfTMtwsyMMXHCt46Ozrt3b3mb90Z71SwWq3u3PglXLn49Ysy165cnTfzuw33atwtbs3rTtu0bJ00ZOaD/4DmzF5DJ9b7wTwr4PWlpKadPH/th/s/qbypCaP+BnXv2bvtq6KhvJs/k8cuWr1jQwKKEIiH3vwd1HA6XV6ZhbgYKmaJUfuSEzKPH939aMDMoMPjHH35hMphLl/2gDsPJyUUkEuXkZHt4eMpksuzszMDAYIRQeTnPysp6w7ptdQshaXrTKGQKQkjxsQCMR05Ojkwm03ct2OSVOdcCIVRRUf7hQ6mpjx4+Sl68aKW6p5Sfl6ulnLOxJ8vL+Zv/3qtuHGxt7T+aV+qu4IWLMQcO7pTLZT179NO4T/t2YSHBoSdPHdmy9U87O4cxkZPqm8jtkwKuSywW//7H8qDA4H593/3MSySSw0f2DOg/eMb0eerBg/eeomX0zMba9tmztLpb+HyenW2D2swPHTiw09HRefWqjeofFLP/tzZ9eodHnzi0aMmc3r0GpKQ+lMvl48d+gxBiszkVFeV2dg7v9X4bgQ0bNhhgtR5sBvKbN/ciEokJVy5++FCloAIhpO551/6pHi2k0ejqLkTtzgJBhbm5RW2Pq1JQ0ZBx21Yt/Tybex08tLtnj35MJvPDHaRSqXoZxeHDIqytbbKyMtS/BTx+We0+Rf8/m6klYCqFihASCCo1hrF7z1Yer3Tu3MW1W8TiGolE4uXV8sOi1F0pPp9X37Ixvr7+VVWC58/T1X++fJmVn/9W5+OWSkGFZ3MvdVJJpdLqmmp1vVyu+Yzp82k0+qtXL4Pbhu7YftjZ2RUh1KZNO4VCcTb235XyampqPlrLhx+oEWrRogWFQtF3Ldi0V3Z29v36Djp/4YxUImnXLozHK7t375aFhRVCqFXL1lQqdcfOqAEDhuTkZB0+sgch9Con28nR2dbWztHB6fiJg3QzM4GgcuiQkYGBwafPHN+9Z6uvb0Bi4tV7924rlcrKygou9yNrQAwYMGTTX78PHKj5Co9Tp4/eTrrRq2d/Hq+0rKzU27sVQigkpEPin9eORx8MDAxOSrpx/sIZ9c5aAm7m4UkkEv/ctGbG9PlBgcF1q3j69MmJk4f9/YMePLj74P8bwwcM8fDwPHX6qKWllUgo3Lf/HyKRmJOTrX40wL/NxbizG/5c3dovkM3mhIV1rltgzx79Dh3es2zFT+qxxAMHdpqbW3w5aLhuH1BgYHB8fOyFizEcNjf65KGqKsHrVy9VKlVG5rO1fyyfNeNHMoVCJBILC/MtLa1IJFKvnv1jz53atn1TYVGBVwuf7OwXt25f27v7hPaT4B9+oEbY3M2ZM2fp0qWWlvodriQtW6bhJEN+do1CjuybfcLUnG3btheJhLeTbty/n6S+EKGmpmbI4K+ZTKa7u0dcfGxcfKxcLl+8aGVZWUl6ekqfPuEEAqFVK//k+0lXr8UXFhV06tjN19dfpVKeiYlOvHnF0cll/ryf09Ie19RUB/73S/whZ2e3zMyno0b+Z4GcAwd3+fkGtAkK4ZfzUlMeJly5+PpNTr9+g8aPm0okEpt7tJBIxGdjT1yMi7Gxtgtu2z4tLSUyYhKHw60vYDaL7WDv+OjxfSKBGBIcWreKhYtnl5fzi4sL7967Vfvf6FET2gS1u3fv9pmY42/z3kyZMtPFxS029uTwYREkEsnDw7OqqvLK1bjUJ4+4XPO2bdqVlpZcuBjTu9cAR0dnIpEY1qHzq1fZZ2NP3Lt328ur5dKf19jbO6jPE1aLRAPDh6pf5oMHd7OyM0aPGq/l/fFtFfDmTc6p00dTUh907dJr6OCvr16Lb9HCx8XZ9c7dxHPnT9+8eeXGzStx8bF37iT26jWARqN17dJLKBRcv375ZuJVUbWwX98vW7cOJBKJz56n3b9/J2L0BPWvfl5e7pWr8QMHfmVlaf3hB8r62LBTref3Klq249DM9H4lxF9//TVw4EDtp/s+n+b52ZPj+BIxCuxmpKcgAIYUCoV6FFehUCTeurZ8xYLawX1DOrnp9dAZzhxLvV8AlJGR0bx5c313BU3gUjShUDgqIlzjQ1O/mV17RriJ0+1dys19Pfv7KR1Cv/Bs7iWRSm7evEKn052dXPUcLJ58fHwMUIsJ5BWDwfhn+2GND3HYpnHOxAB0e5eYTFaP7n3v3k28nHCBxWK39gucM2ehra2dPiPF2axZs1auXKnv1Q9MIK+IRKKDpjNjoC7d3iUrK+sZ0+epTwM0Eenp6Qa4OBjuawRNy9atW9nsho6m6MwE2isAMOTt7W2AWqC9Ak3LmDFjDHCfCOQVaEKUSmVGRoaWq0OxAnkFmpa9e/caoBbIK9CEEIlEX19fQ1RkgDoAMBJ8Pn/hwoUGqAjyCjQhFRUV2dnZBqgI8go0IXZ2dr/++qsBKtI8MEKhEo19uirQuJhb05D+v3NMJtMw1wdqbq+YXBKv8P3Z7QDQE4VcVZBTzbHS+/D3/fv3d+7cqe9a6s0rK0eaSgktFjAQQZnMo7V+b4hSe/36dVlZWQN2/Fya779CCCWeLkMEItyCBQzgzN+5AybbW9pT9V1RSUmJSqWys9P7Bfv15hVC6OYpnlymCuhqSaXD8AbQC2G5/MqRgj5j7G2c9Z5UhqQtrxBCj69VpN2uVCpUDBZcoftxKpVKqVQaZuUyU8exobx+KnTxYrbrY2HjbKBpMP74449u3boFB39kWofP95FsCepmHtjVXFguFwn0fqliI/D27dsdO3asWLEC70BMAIFA6PG1LYWmebY5PUlNTQ0P13xXNbY+3goRCIhtSWbrf+KBRkAgVVXJ3tq763FxIPA5li5d6uHhYYCKIFtAE+Ll5WWYimBAAksEAkHfEycAnb19+3bp0qWGqQvyCmOVlZpnwwW4e/78uQFmZleDfiCWiESim5sb3lEAzQIDAwMC3l9rRk8gr7BEo9EyMzPxjgJoZmtra7C6oB+IJRqN5ugIU7IZqenTpxvmIibIK4xZWFg8fPgQ7yiABjweLysrywAroKpBXmGJTCaz2eyKigq8AwHvY7PZJ0+eNFh1kFcYa9GiRUFBAd5RgPdRqVQDTMdZC/IKYw4ODllZWXhHAd43YsSIly9fGqw6yCuM+fn5lZYa9YKFTVBxcbFMJmvevLnBaoS8wpifn9+VK1fwjgL8h52d3enTpw1ZI+QVxry8vHg8Ho/HwzsQ8K+Kigr1GtMGA3mFvYEDB6akpOAdBXinqKgoMjKSSjXofZOQV9gLCws7duwY3lGAd5KTk6dMmWLgSuE6Juy1bdu2pKQkLy/P2dkZ71gAGjRokOErhfZKLyIiIhISEvCOAqC3b9/euXPH8PVCXunF8OHD9+7dW1VVhXcgTd38+fNtbGwMX+9H5o0BOjtx4kR2dvaCBQvwDqTpKi0tLSws9Pf3N3zV0F7py7Bhw0pKSoqKivAOpOmysbHBJakgr/Rr+vTps2bNwjuKJuro0aP79+/Hq3bIKz1q3rx5//79o6Ki8A6kyeHxeLdv3x47dixeAcDxld4tX7588ODBBrsDHBgDyCtDGDx48N9//+3i4oJ3IE3C3bt3CQRC+/btcYwB+oGGcObMmREjRhj4ErWm6e7duwcPHsQ3qaC9MhyZTDZlypQ9e/YQCAadObmpkUqlBr4UUCNorwyEQqFs3749JCREKBTiHUujdeLECaVSiXcUCPLKoGg02oMHD8LDw+Gklj4MGzasbdu2dLpRTI4P/UAczJ07d9CgQV27dsU7kMZDLBZTqVQi0VjaCWOJo0nZsGHDuXPnduzYgXcgjcQff/xBp9ONJ6kgr3Czbt06hULx22+/4R2IyVu+fPmwYcPwjuJ90A/E0/Xr11etWrV9+3bDLMrUyFRXVzMYjOLiYgOsF/ypoL3CU9euXY8dO/bTTz/B/cWf6tWrV3PnzlXPCYN3LBpAXuHM0tIyOjr6zZs3q1evxjsWU3LmzJlt27bhHUW9IK+Mwo8//tilS5fg4ODr16/jHYuxO3HiBELo+++/xzsQbSCvjEXHjh0fPHgQGxu7bt06vGMxXj179vTx8cE7io+DvDIu69ev9/X17dSpEzRc78nKylIoFNHR0X5+fnjH8nGQV0anX79+CQkJsbGxCxcuVCgUeIeDP7lcPmbMGKlUSiKRLCws8A6nQWCc3XhdunRp165do0aNGjx4MN6x4OnNmzcikahVq1Z4B/IJoL0yXr179z527Fh6evqkSZPevn2LdziG9ubNm4iICISQm5ubaSUVtFemISUlZe/evc2aNZs9ezbesWDvl19+Wb58+Yfbf/3111GjRnl6euIR1OeC9soEBAYGbty40cLCon///nWn++zWrVu/fv3S0tJwje6z3L1798GDB3369KndEhcXt2TJEoTQzz//bKJJBXllSsaOHXvs2LGEhITp06cXFhYOGzasqqqqpKRk/fr1eIemu82bNxcXF6uXX1HfmZaSkrJixQq84/pc0A80PXfv3l25cmVxcbH6s6PT6VOnTh0zZgzecX2yLVu2HDlypKamRr0084YNG8LCwvAOChvQXpme0NBQIpFY+4MoFotPnDiRn5+Pd1yf5sWLF3FxceqkUg+mr127Fu+gMAN5ZZLeWxo8Ly9v48aN+IWji3Xr1r33W9CYxjwhr0xPly5d1I1V7f8JBMLjx48vXLiAd2gNFR0dnZWVRSC8OwxRT0pBIBA6d+6Md2jYgPWvTM+NGzfWr19fUVFRWloqEAjkcrlMJpNJZWcP37FEocIKubBSTqYQhRVyvCP9D7YlRS5VsrgkjhXlckwKnU5nMBhMJpPBYJiZmVlYWNja2s6ZMwfvMLEB4xYmLztV9CSxouBljYUjk2JGJdNIFBqJQqUoVUYxM1EtAoEgk8jlEoVCqhALxZXFNc7ejMAvzN19GXiHhj3IKxOWm1l981QZlUk3Mzdj25jat1OFBCXV1eUipJB3+craoZlRzKOEFcgrU3VxX2lpgdTGw9KMg/80lJ+jukJS8pLn1Nys1yhrvGPBDOSVCVKh/aveWLhYsm1NrY2qX0VBlbhCOHJeI1mRGfLKxB1SK2UAAAXASURBVCjkqgOr3zq0tKWxKHjHgrHqCkllPn/UPGdk+jNtwzi7idn18yuXAIfGl1QIIYY5zdzFat/KN3gHggFor0zJiU35ZtbmTMtGdYj/HkGxiCivDp9sj3cgnwXaK5Px8Eo5hcVo3EmFEOLYMWVKSvqdSrwD+SyQV6ZBLlXdi+NzHTl4B2IIXEfuzZNleEfxWSCvTENiTJmDlyXeURgIkUSw9eDevcDDOxDdQV6ZAEm1Mv+lxMLZGBurew9i5v/cXiDAuHmxdrfISqk23WN/yCsT8Pq5iEhpYldyEhCBRMzNqMY7Dh1BXpmA7FQR07LxnAJuIIYFMzvVVNe2bGK/gqZJVKmw9mLqo2SpVHwxYevjJ/EymcTG2q1rp4jA1r0QQjeTjqSkJXQOG3UxYWtVVZmTo8/wLxfa2rirn5VfkHnmwoa3+c84bGsbK1d9BIYQ4tgyK/NNtb2CvDJ2MomSVyi21cPcyUqlcveheeXlhd07j2OxLF/mPDx4fIlEWtO+7SCEUG5e+o3bh4Z/uUihkJ84u+boqRWzpu5GCBWXvt66+1smw7x/r+9IRPLl67uwjwwhhBCZSix4CXkF9ENYqaCZ6eVjSnt27dXrlEXzznA5NgihNv59JNLqW3eOqfMKITQhYh2HbYUQ6hQ6IjZuk6i6ksngno//m0Agzpy6i8W0QAgRiMRTsfq5f56AKFSiWKSgM0l6KV+fIK+MXbVAzuDq5Yr155m3FUr56g1DarcolQozOqv2TxrVTP0PC3MHhJBAUEoh0zKz73YI+UqdVAghElGPXyG2FV0kgLwCekCmEiUivdz5WyXkcdjW0yZsrruRqClPyCSKOusEVWUKhdzSwkEf8XyopkpKppjkRbiQV8aOySFJxXrJK4YZRygqtzB3oFBoDXyKupkSCsv1Ec+HpDUKBsckv6Iwzm7sGByytEYvq4p4Ng9RKhVJySdrt0ikNdqfQqczra1cUp9ekctl+gipLqVChQiIQoX2CugBkYisnejSajmVgfGH1Tag370HZ87F/11eUejk4F1QlJX27PqPs45Rqdou7e3dbfLhE7/8/c/kdm3CCURi4h19rYwsEcns3Mz0VLi+QV6ZAMfm9OJCkbU7F9tiyWTKlHF/Xbi0+fGTS3fun7axcg1rN5RE+shXok1A35qaquu3D5279LedjYebi19pmV7umKoqETXzMdWL9+H+KxNQkCNOOFrmGmSg0QIjkXMvb/A0B0t7k5y9A9orE+DoQacziHKpkkyt93h4yaoeGre7ubR+81bDgiNMM+7CuacwDHLzzqmFxdkfbjfn2FUIijUEwDBf+P3JD7erSURySzuqiSYVtFcmI+NBVUqiyN7Hpr4d+OUFmh9QERBBw0dMIBAtzLG8J7dSUKpQaBjMkMtlZLKGWQO0B5CXVvTFQAu3lqZ6VSS0V6bBJ5idHM+XCGX1zWxhaeFo8KD+Q33RBiZE5WIqRWW6SQXj7Kak5yi7qmLTvju9gUSlgp6j7PCO4rNAXpkMRw96C39aSbYJ30XbEIXPSgK/YFnam/aEU5BXpiSgs7mdI7Eoi493IPpS8LysuS/dqw0b70A+F4xbmJ5bZ/n5r+R2XlZ4B4KxgmelLduaBXXF+DQdLiCvTNKDyxVZaTXWzawodNO71vtDEpGsJIsX2JnduqMxzuGhA8grU/X2Rc2lA8Usa4ZNc0siySQvokMIKWTK0pc8iVDSb5y9rWtDL/81fpBXpi3tVuWTWwIihUw3Z3BsGSSyaRwwy6UKQUm1uKJapVS06cZt2a6RNFO1IK8ag5wnopdPhK+fVxNJBDKNRKKQKWZUudS41muk0EjSaqlCqlAqlHKp0qM1s7k/y72VCZ+k0gLyqlER8OQigbxaIJdJVTKpca3XSKGQqHQCg0Nicslsi0Z+QQLkFQDYM43uOACmBfIKAOxBXgGAPcgrALAHeQUA9iCvAMDe/wBx/bjk573N5QAAAABJRU5ErkJggg==", "text/plain": ["<ai_data_science_team.multiagents.sql_data_analyst.SQLDataAnalyst object at 0x7fb8e8b32e60>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_data_analyst = SQLDataAnalyst(\n", "    model = llm,\n", "    sql_database_agent = SQLDatabaseAgent(\n", "        model = llm,\n", "        connection = conn,\n", "        n_samples = 1,\n", "        log = LOG,\n", "        log_path = LOG_PATH,\n", "        bypass_recommended_steps=False,\n", "    ),\n", "    data_visualization_agent = DataVisualizationAgent(\n", "        model = llm,\n", "        n_samples = 10,\n", "        log = LOG,\n", "        log_path = LOG_PATH,\n", "    )\n", ")\n", "\n", "sql_data_analyst"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Viewing Subagents With xray\n", "\n", "Keep in mind that this agent is actually a multi-agent that combines 2 subagents. We can view the subagents by running the following code:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sql_data_analyst.show(xray=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the Agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The main inputs to the SQL DB Agent are:\n", "\n", "- **user_instructions**: What actions you'd like to take on the SQL database query. \n", "\n", "Let's start with a simple question that a user might want to know about the database:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What tables are in the database?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---SQL DATABASE AGENT----\n", "    * RECOMMEND STEPS\n", "    * CREATE SQL QUERY CODE\n", "    * CREATE PYTHON FUNCTION TO RUN SQL CODE\n", "      File saved to: /Users/<USER>/Desktop/course_code/ai-data-science-team/logs/sql_database.py\n", "    * EXECUTING AGENT CODE ON SQL CONNECTION\n", "    * EXPLAIN AGENT CODE\n"]}], "source": ["sql_data_analyst.invoke_agent(\n", "    user_instructions = \"What tables are in the database?\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Response\n", "\n", "The response produced contains everything we need to understand the data cleaning decisions made and get the cleaned dataset. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["['messages',\n", " 'user_instructions',\n", " 'sql_query_code',\n", " 'sql_database_function',\n", " 'data_sql',\n", " 'data_raw',\n", " 'plot_required',\n", " 'data_visualization_function',\n", " 'plotly_graph']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_data_analyst.get_state_keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Not all fields will be filled if the agent did not need to use them.\n", "\n", "- **sql_query_code**: The SQL query code that was generated by the agent.\n", "- **sql_database_function**: The Python function that was generated by the SQL Database Agent.\n", "- **data_sql**: The Pandas data frame that was generated by the agent.\n", "- **data_visualization_function**: The Python function that was generated by the Data Visualization Agent.\n", "- **plotly_graph**: The Plotly graph that was generated by the Data Visualization Agent."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SQL Query Code"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/markdown": ["```sql\n", "SELECT name FROM sqlite_master WHERE type='table';\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_data_analyst.get_sql_query_code(markdown=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Pandas Data Frame From SQL Query"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Categories</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>sqlite_sequence</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CustomerCustomerDemo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CustomerDemographics</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Customers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Employees</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>EmployeeTerritories</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Order Details</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Orders</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Products</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Regions</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Shippers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Suppliers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Territories</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    name\n", "0             Categories\n", "1        sqlite_sequence\n", "2   CustomerCustomerDemo\n", "3   CustomerDemographics\n", "4              Customers\n", "5              Employees\n", "6    EmployeeTerritories\n", "7          Order Details\n", "8                 Orders\n", "9               Products\n", "10               Regions\n", "11              Shippers\n", "12             Suppliers\n", "13           Territories"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_data_analyst.get_data_sql()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Python Pipeline Function"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/markdown": ["```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: sql_database_agent\n", "# Time Created: 2025-01-12 20:13:41\n", "\n", "\n", "def sql_database_pipeline(connection):\n", "    import pandas as pd\n", "    import sqlalchemy as sql\n", "    \n", "    # Create a connection if needed\n", "    is_engine = isinstance(connection, sql.engine.base.Engine)\n", "    conn = connection.connect() if is_engine else connection\n", "\n", "    sql_query = '''\n", "    SELECT name FROM sqlite_master WHERE type='table';\n", "    '''\n", "    \n", "    return pd.read_sql(sql_query, connection)\n", "        \n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_data_analyst.get_sql_database_function(markdown=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Make a Plot of Sales Revenue By Week And Territory\n", "\n", "Once the user has knowledge of the tables in the database, they can ask more complex questions.\n", "\n", "Let's make a plot of sales revenue by week and territory. This will require a more complex SQL query and a Data Visualization. \n", "\n", "*Note: This is a more complex question, which may require a high performance model such as \"gpt-4o\". Results will vary by model.*"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---SQL DATABASE AGENT----\n", "    * RECOMMEND STEPS\n", "    * CREATE SQL QUERY CODE\n", "    * CREATE PYTHON FUNCTION TO RUN SQL CODE\n", "      File saved to: /Users/<USER>/Desktop/course_code/ai-data-science-team/logs/sql_database.py\n", "    * EXECUTING AGENT CODE ON SQL CONNECTION\n", "    * EXPLAIN AGENT CODE\n", "---DATA VISUALIZATION AGENT----\n", "    * CREATE CHART GENERATOR INSTRUCTIONS\n", "    * CREATE DATA VISUALIZATION CODE\n", "      File saved to: /Users/<USER>/Desktop/course_code/ai-data-science-team/logs/data_visualization.py\n", "    * EXECUTING AGENT CODE\n"]}], "source": ["sql_data_analyst.invoke_agent(\n", "    user_instructions = \"Make a plot of sales revenue by month by territory. Make a dropdown for the user to select the territory.\",\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": "#3381ff"}, "name": "British Isles", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [107411.80999999998, 357837.84, 223909.6700000001, 385088.33999999985, 234667.61999999994, 308253.4499999998, 116020.20000000001, 190417.24, 457859.28, 321751.4900000001, 273249.2900000001, 378770.7299999999, 480702.5799999999, 167209.18000000002, 195430.91999999998, 359334.71, 326914.23, 205919.29000000007, 293412.23, 144119.00999999998, 608302.1100000001, 315353.14, 166437.69999999998, 233901.10000000003, 296859.01, 520468.42999999976, 90690.48999999999, 232535.68000000002, 279704.82, 208428.90000000002, 164456.02, 169856.31000000006, 187329.69, 298554.26999999996, 441479.58999999985, 366004.62, 413168.4599999999, 398139.0399999999, 366972.8400000001, 210611.3000000001, 211749.19999999998, 611753.78, 316600.7900000001, 468235.5500000001, 267334.0100000001, 211042.21000000002, 356568.9099999999, 231677.57000000007, 282757.72000000003, 363397.0399999999, 47133.38, 259378.35, 342954.9600000002, 493018.17000000004, 118220.785, 211183.80999999997, 341686.6900000001, 168038.18000000005, 483765.28, 226752.65999999997, 285722.6599999999, 400140.55000000005, 267382.56000000006, 292174.28, 228398.3400000001, 258208.84499999994, 504646.36, 296365.2599999999, 356403.98999999993, 249927.48499999984, 192713.26000000004, 230396.25999999998, 356081.18999999994, 414538.95999999996, 348658.69999999984, 141901.95, 381305.7400000001, 373482.58000000013, 121062.92000000003, 225714.60000000003, 219247.72999999995, 259297.87999999998, 391120.29000000004, 171721.47, 283551.08, 170833.73, 322839.82999999996, 291127.54999999993, 360539.04000000004, 343660.68000000005, 377785.78999999986, 513874.3099999999, 290227.8499999999, 136456.84000000003, 225550.42000000004, 136307.34999999998, 120284.65999999999, 207526.94999999995, 365001.42000000004, 136615.81999999998, 238000.03, 286129.79000000004, 336592.81, 240671.76000000007, 293930.73, 256614.96000000002, 261752.45999999996, 175354.81999999998, 428863.38999999984, 127062.46999999997, 169191.59999999998, 212992.54000000004, 519719.89000000013, 375969.5999999999, 299436.13, 335185.81999999995, 309148.8800000001, 150975.17, 402160.61, 524418.8100000002, 365056.0900000001, 438944.47000000003, 217736.65, 338720.9799999999, 156292.74, 127546.90000000001, 257043.01000000004, 95019.79000000001, 223431.50999999998, 151948.81999999998, 247733.59999999998, 359269.2100000001, 98464.38, 310572.02000000014, 385028.43, 214977.36000000004]}, {"marker": {"color": "#3381ff"}, "name": "Central America", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [117527.23, 91136.20000000001, 207833.31000000003, 173911.77000000002, 142828.12, 187107.62999999998, 227320.46000000002, 85684.41, 285364.1099999999, 181555.80999999997, 125634.45999999998, 218356.86999999994, 84420.49, 121417.87000000001, 142343.08, 42099.58, 282823.9400000001, 77292.52000000002, 225157.99999999994, 120819.17000000003, 322226.4299999999, 160148.58, 286132.8999999999, 13220.9, 31229.050000000003, 97036.04, 250276.58999999988, 96341.61, 262441.38, 337607.5999999999, 306485.9100000001, 360017.5900000002, 118594.93000000001, 362405.48, 115323.28, 196939.4, 378148.6700000001, 157923.37999999995, 255149.91999999998, 202781.04999999996, 145836.02, 264915.27, 186365.4400000001, 212625.14999999997, 118170.05999999998, 170814.27999999997, 241123.46999999994, 281431.14, 261649.90000000002, 202634.85000000003, 32590.93, 167694.58999999997, 207822.88000000006, 241338.50999999998, 101168.27, 59715.53, 94417.83250000002, 195377.62, 97176.45, 90555.61000000002, 160173.18999999997, 307674.33499999996, 225291.30000000002, 305141.73000000004, 100799.44, 170673.90999999997, 187180.37000000002, 181978.06999999995, 227481.42000000004, 144020.66, 117270.6, 204721.76999999996, 286301.3799999999, 108705.01000000001, 145527.63000000003, 122857.56999999996, 315757.61000000004, 166292.43999999997, 389914, 292284.35, 186364.70999999996, 137022.21, 303331.3299999999, 207553.09999999998, 261322.30999999994, 267332.59, 215526.49, 166248.44999999998, 126424.27000000005, 241364.84999999995, 268586.20999999996, 258113.59999999998, 193577.43000000002, 221173.5, 97929.63999999998, 195544.05000000005, 39021.69, 342860.19999999995, 286443.6599999999, 183207.58, 53324.499999999985, 50901.51, 312444.0300000001, 307624.56999999995, 194168.68, 186881.15, 121962.78, 227393.39, 127909.13000000002, 121283.93000000005, 310643.82999999984, 219565.99999999997, 135620.75999999998, 197437.17000000013, 97664.83, 116070.06, 214315.17999999996, 116338.53000000001, 214386.5, 182121.03000000003, 133371.72999999998, 267777.04000000004, 119776.64, 95323.91, 127273.86999999998, 147765.33, 262404.02999999997, 87857.69999999998, 157370.54999999993, 68694.83, 140172.38000000006, 27049.68, 112271.4, 363631.61]}, {"marker": {"color": "#3381ff"}, "name": "Eastern Europe", "type": "bar", "x": ["2012-07", "2012-08", "2012-10", "2012-12", "2013-01", "2013-02", "2013-03", "2013-05", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2014-01", "2014-02", "2014-03", "2014-04", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-02", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-12", "2016-01", "2016-02", "2016-04", "2016-05", "2016-09", "2016-11", "2016-12", "2017-02", "2017-05", "2017-07", "2017-08", "2017-09", "2017-11", "2017-12", "2018-02", "2018-03", "2018-04", "2018-06", "2018-08", "2018-09", "2018-11", "2019-03", "2019-04", "2019-06", "2019-07", "2019-08", "2019-10", "2019-11", "2020-01", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-09", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-06", "2021-07", "2022-01", "2022-02", "2022-03", "2022-06", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [31950.799999999992, 37874.05, 16321.710000000001, 65415.68, 48829.049999999996, 27181.21, 144, 48942.14999999999, 15484.550000000001, 41344.06999999999, 26460.379999999997, 5369.4800000000005, 51833.83999999999, 8066.55, 16376.139999999998, 49134.920000000006, 28167.839999999997, 83716.32, 40501.12, 97176, 118229.08, 90085.77, 77197.11, 28665.249999999996, 16665.63, 115245.77, 104524.40999999997, 47013.399999999994, 51773.56, 43196.88, 64880.780000000006, 57495.35, 115607.90000000001, 85459.29000000001, 44825.630000000005, 47325.259999999995, 34069.4, 7500.56, 46665.03999999999, 22941.190000000002, 44959.08, 13724.23, 22774.420000000002, 123435.43999999999, 9276.7, 86329.38, 587.5, 26127.809999999998, 37203.100000000006, 58679.22, 78532.24999999999, 74953.05, 111287.9, 41681.45999999999, 61075.75000000001, 45467.119999999995, 63528.14999999999, 54511.60999999999, 52538.52, 128707.75, 88940.94000000002, 2951.5, 22165.629999999997, 8358.51, 24635.929999999997, 67970.54999999999, 101078.60999999999, 10641.5, 44758.70000000001, 3076.8, 36300.63, 58118.52000000001, 61675.91, 72786.52999999998, 83343.74999999999, 33644.24, 31075.859999999997, 71836.38, 61453.28, 3415.3, 2310.8999999999996, 85210.67000000001, 48408.369999999995, 63918.97, 51939.70999999999, 157778.88000000006, 45617.6, 96921.65000000001, 16226.66, 38123.83, 95897.13999999998, 29936.26, 22907.95]}, {"marker": {"color": "#3381ff"}, "name": "North America", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [244663.68999999997, 501785.12000000005, 555378.2100000001, 491789.2999999999, 439253.97, 532079.4299999998, 681912.1699999998, 594806.3600000002, 705910.02, 730560.8200000002, 799006.8399999999, 726887.28, 747315.8100000003, 439805.2800000002, 388629.3199999998, 453761.61000000016, 580994.7799999999, 702931.1599999999, 857797.2900000002, 469628.9, 573847.2099999998, 657303.3400000003, 563882.7599999999, 599900.9000000001, 646752.7100000001, 625714.2700000003, 695232.4200000003, 543056.2000000001, 574035.02, 671119.9500000002, 401681.0999999998, 319924.3799999999, 748905.0999999997, 772574.1800000005, 570676.08, 253692.35999999996, 776556.7300000006, 928501.2400000002, 597201.6699999999, 303138.79, 644052.4400000003, 381239.31, 505230.3800000001, 652779.99, 682676.01, 496964.6899999999, 549551.7400000001, 643922.1700000002, 591613.99, 822776.2000000001, 479426.11, 621342.1749999999, 488076.6099999999, 599901.9400000001, 419062.7099999999, 435467.64499999996, 507912.2549999998, 472362.4400000001, 697292.7200000001, 736326.1400000001, 477312.4899999999, 612940.7599999998, 413620.9499999999, 752158.1024999999, 871797.4599999998, 442651.1, 696009.8274999999, 718083.0699999998, 530097.9850000001, 914372.4199999999, 547167.9905, 419285.72000000003, 455388.41000000015, 485066.4799999999, 626239.99, 630339.05, 353322.35, 634889.4900000001, 735013.9000000001, 181748.8099999999, 500584.36999999965, 225492.92, 577153.77, 669006.3699999999, 670365.8799999997, 362385.1199999999, 635960.7099999996, 720881.8400000003, 533763.03, 430760.53999999986, 400872.1500000001, 745447.9900000007, 579639.7699999997, 246884.08, 806926.54, 460324.5900000005, 821957.3099999997, 747910.8400000001, 446322.4600000001, 360219.25999999995, 558656.3400000003, 375725.60000000015, 622239.2500000001, 391159.4899999999, 750859.5999999995, 732710.1500000005, 659330.2699999998, 669012.45, 566877.51, 372985.83999999997, 562185.2200000002, 533459.3600000003, 387174.57000000007, 641255.6400000006, 418695.8099999998, 394769.68, 357371.1499999999, 559790.4700000004, 539427.8599999998, 560478.2900000002, 651571.9700000001, 444899.12999999983, 426680.88, 393478.4700000001, 625504.6400000002, 591336.5599999998, 753418.5900000001, 655852.8800000005, 448332.4300000001, 619910.3899999997, 609979.0799999998, 272037.51999999996, 710319.6399999998, 463343.14000000013, 696070.67, 449394.43]}, {"marker": {"color": "#3381ff"}, "name": "Northern Europe", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [72739.33000000002, 257084.27999999997, 147128.85999999996, 155290.52, 177592.44000000003, 27102.110000000004, 116682.41000000003, 119448.18999999999, 172435.65, 180075.7499999999, 140498.01000000004, 81513.75000000001, 205602.69999999998, 351635.4000000001, 71578.9, 316028.46999999986, 227476.17999999996, 100797.06, 90800.54000000001, 85321.41, 87486.87999999999, 155436.56, 224449.13999999996, 174225.46000000005, 128013.07000000005, 69463.86, 69982.4, 199583.3599999999, 65418.43000000001, 320253.79, 78084.93000000001, 84199.96999999997, 187395.17, 88660.74999999999, 246700.80999999994, 274602.24000000005, 296411.4199999999, 184768.14999999997, 138475.36000000002, 150723.71, 175771.72999999998, 182141.28999999995, 56483.75, 76802.40000000002, 97009.9, 138357.82, 137916.81000000006, 166954.25, 77457.165, 9441.5, 165130.28999999998, 252311.21999999997, 127348.56999999999, 29587.969999999998, 177699.70999999996, 97707.20999999998, 171437.27000000002, 88125.57, 166431.55999999997, 42570.815, 275411.0800000001, 56216.549999999996, 64141.289999999986, 375833.1500000001, 30032.03, 174267.3275, 241339.84249999997, 144818.91999999998, 143039.72, 15844.687499999998, 67093.95500000003, 167956.80999999997, 158335.60000000003, 38081.15, 115966.09000000003, 78469.65, 202632.33, 114560.45000000003, 80299.41000000002, 279914.9699999999, 109399.58999999998, 162282.67999999996, 47188.899999999994, 83092.15, 228173.80000000005, 31039.190000000006, 63651.13000000002, 116824.62999999999, 77613.52999999998, 174305.78999999995, 50278.36, 91323.18999999999, 116513.03, 66855.27999999998, 67638.49999999997, 150909.01999999996, 41602.36, 323571.05000000005, 178471.07, 129866.93000000001, 95663.95, 257455.81999999995, 187418.72999999998, 175039.44000000003, 224114.88000000003, 204436.64999999994, 44436.35999999999, 196666.1200000001, 195701.09000000003, 167715.30000000008, 237309.71, 111286.99, 163142.05000000005, 206133.41, 159082.17000000004, 135763.62, 147543.82, 57469.979999999996, 56211.310000000005, 138387.12, 114482.40000000001, 113506.29000000001, 88977.47000000002, 57062.530000000006, 115478.48999999998, 258818.54, 53551.50000000001, 104082.56999999999, 75236.83999999998, 162352.61, 96463.12999999999, 11086.1, 57587.66, 371235.08999999997, 61951.46, 212513.25999999998]}, {"marker": {"color": "#3381ff"}, "name": "South America", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [497127.0100000001, 569472.88, 612618.3800000002, 648776.5399999999, 583382.03, 941342.2199999997, 419065.08999999985, 544865.1599999996, 470459.5100000001, 426038.9699999999, 644285.3700000001, 372542.98, 818855.7400000002, 810343.4400000002, 449491.3199999999, 447538.7600000002, 805711.4500000009, 821278, 298690.95999999996, 303351.68000000017, 519977.23, 696068.2200000003, 625168.5699999998, 683470.6299999998, 717152.5900000001, 606431.2999999999, 487141.0100000002, 529776.0299999999, 660551.3300000003, 648459.4100000004, 893810.0799999997, 573494.4899999999, 549208.56, 562855.2000000003, 436755.4199999999, 641017.3400000003, 475577.6399999999, 581146.8500000001, 585959.2000000002, 880401.6699999996, 577536.35, 911260.5699999997, 544387.2200000001, 227621.69999999995, 695758.38, 566263.9199999998, 585825.0500000002, 600536.44, 870178.5599999995, 522485.18000000005, 542378.1699999998, 639160.8800000007, 479798.8299999998, 875764.5899999999, 660679.7299999994, 464086.25000000023, 902460.1299999994, 404060.1300000001, 664775.5600000003, 402607.5000000001, 409758.89999999997, 480557.6825000002, 294111.195, 478568.2650000001, 589816.4600000001, 700291.654, 752978.8990000001, 431649.18500000006, 495618.88500000007, 257584.9099999999, 666868.8799999993, 782582.6799999998, 554727.8599999996, 660897.5100000002, 656844.4099999998, 422185.09999999974, 230751.64, 536336.5999999999, 758073.2500000002, 543320.78, 518181.86999999994, 759052.7600000004, 292764.32, 466851.19999999984, 525986.4500000001, 598878.42, 563553.8899999997, 492363.68, 419108.42, 554215.89, 661475.8799999998, 473395.0499999999, 517735.22999999975, 537723.7100000004, 330164.17999999993, 447747.43999999977, 672056.1699999998, 811080.0200000003, 512063.14999999997, 580208.8500000003, 664177.7900000003, 287039.0500000001, 608317.1999999998, 763763.2599999997, 346615.43999999994, 484393.8099999997, 427553.0099999999, 606030.8400000001, 719942.99, 952536.5300000004, 868662.33, 737385.5699999998, 692322.8100000005, 933226.8100000005, 631482.6399999997, 310277.30999999994, 510637.4300000001, 380267.7000000001, 706314.6799999998, 414519.92999999993, 427972.9599999998, 435390.25, 501123.30000000016, 997008.8399999994, 690061.49, 691712.1499999998, 482025.64999999997, 513505.6900000001, 800529.62, 836794.8300000003, 691997.3899999997, 646382.69, 438110.83, 687457.2999999999, 688437.8599999996, 470863.29999999976]}, {"marker": {"color": "#3381ff"}, "name": "Southern Europe", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [267124.44, 363652.55, 370648.77, 281261.21, 423071.83999999997, 364360.68, 330863.13999999996, 396330.38, 182878.7, 332920.6899999999, 366927.6499999999, 432798.3399999999, 426076.60999999987, 384792.8399999999, 359071.75000000006, 320839.9, 227287.99999999994, 254306.42, 169818.33000000007, 377924.0200000001, 431680.31, 368338.41000000015, 201936.31000000003, 344809.3499999999, 586068.8500000001, 337940.1300000001, 164712.57, 273231.65, 222041.3500000001, 311147.43000000005, 423564.8299999998, 446681.68000000005, 253138.66999999998, 332339.05, 347020.82000000007, 367164.01000000007, 231624.46999999997, 388350.37000000017, 449591.3800000002, 248189.30999999994, 350902.55, 350920.34, 445032.0999999999, 369130.68000000017, 478263.5100000001, 367689.13, 289494.12000000005, 471464.57, 466323.0200000001, 287953.63, 450208.8399999998, 446238.82, 580966.12, 271800.64999999997, 440037.0600000002, 219477.59, 373192.51499999996, 550739.9299999998, 429175.32999999967, 495517.18, 248262.90000000002, 397052.1249999999, 282929.87749999994, 223873.73, 433747.3799999997, 421575.9099999999, 495499.05000000005, 291134.8500000001, 343290.48000000016, 409357.5199999999, 361982.2, 233676.95, 164812.48000000007, 142419.97000000003, 389071.15, 118089.99, 328570.37999999983, 449128.72, 425656.41000000003, 228325.52000000002, 253853.04999999996, 427746.7, 475673.2, 264382.85, 304305.1600000001, 304600.3399999999, 404030.8000000001, 241009.26999999993, 438335.20999999996, 490202.4900000003, 482614.8000000001, 124452.18, 205175.47, 538012.58, 456396.36999999994, 489818.9899999998, 404828.89, 474470.83999999973, 272159.74999999994, 200964.58000000005, 230137.41999999998, 496621.1199999999, 563162.2899999999, 284024.50999999995, 272209.1, 324758.53, 377309.25000000006, 352956.35999999987, 417157.5100000001, 426753.7900000002, 329066.6900000002, 198606.13, 446658.1, 481016.0300000002, 206544.05000000008, 377997.47000000003, 373212.16, 444126.89999999985, 555495.0800000002, 234347.93000000008, 327644.89, 207482.21000000005, 318943.87999999983, 455354.05, 377116.67, 367543.12000000005, 273291.89, 186123.59000000003, 247954.30999999994, 257781.57000000007, 362580.24999999994, 279163.46, 542730.5700000001, 198251.44, 388194.87, 238786.63]}, {"marker": {"color": "#3381ff"}, "name": "Western Europe", "type": "bar", "x": ["2012-07", "2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-11", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-05", "2020-06", "2020-07", "2020-08", "2020-09", "2020-10", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [651753.2699999999, 1236821.8200000005, 1067630.9899999998, 843258.7899999998, 1050127.4700000004, 1140497.56, 1044497.3599999994, 701728.2099999997, 1129662.4700000004, 766551.7400000002, 868532.53, 755018.44, 1084936.0099999995, 523149.4599999999, 970354.8200000005, 906734.8500000002, 692970.7699999993, 683620.1099999999, 811770.1100000001, 769791.0800000003, 1187290.34, 728683.8300000001, 1196963.0399999993, 992857.5899999997, 633412.6199999999, 1013020.68, 785708.2800000005, 707308.95, 1044484.8600000005, 1064931.9600000002, 845173.8499999994, 903948.5500000004, 838145.9199999998, 873951.8300000004, 784142.0700000001, 660512.2299999996, 1152944.0099999995, 1318805.1999999997, 951932.9700000007, 1095002.439999999, 998830.9700000006, 1037556.3600000005, 1159540.1599999997, 1041554.7400000005, 1057726.6999999997, 940711.3700000006, 1158349.4000000001, 810472.889999999, 982996.4600000005, 734503.0350000003, 1140628.2999999993, 892979.2099999998, 804669.7949999998, 1048725.9, 1054801.7349999996, 965785.7300000003, 1223201.6899999995, 997096.3200000002, 1560503.22, 813759.5075000002, 870760.8375, 1017390.9125000002, 944761.0050000002, 1181490.6185000003, 933905.3664999998, 1038640.7720000005, 1090785.001500001, 724128.0825000001, 544064.4650000001, 921480.2600000008, 1156870.6350000005, 1122991.9400000002, 907946.8700000002, 1041880.0700000004, 956376.3599999993, 1001124.5900000005, 1239697.2199999997, 948322.3200000002, 635511.99, 883632.1000000002, 1048926.5299999998, 1286512.079999999, 1120273.0599999994, 1095578.5900000003, 792178.4699999995, 1195986.0599999991, 705721.99, 1063592.5800000005, 582048.6100000005, 1250206.1199999996, 1069217.8299999998, 1098825.4799999993, 919325.2500000001, 778114.4100000004, 943634.5800000002, 949112.7699999998, 1189119.3000000012, 962637.8299999997, 1341821.9999999993, 1183937.749999999, 979493.0100000001, 990459.0400000002, 982422.1799999998, 907986.6100000005, 963685.6199999996, 740904.1600000004, 1026532.0200000003, 1137804.3499999992, 1218056.0400000003, 950849.0800000008, 814908.6300000001, 1037002.1100000006, 697307.7699999998, 1399552.9900000002, 1491293.6500000022, 824972.8800000001, 1029549.2800000008, 782903.0900000004, 1090740.3699999996, 1085177.1199999994, 970618.7399999995, 1490923.7300000023, 677965.8999999999, 865699.9599999995, 1193608.7399999995, 1150622.6699999992, 1093051.76, 731624.4600000004, 1248580.890000001, 1073278.5299999996, 1318292.4999999998, 1166373.6699999995, 1201600.3200000003, 959138.1199999994, 904301.4099999999, 837669.0200000003]}, {"marker": {"color": "#3381ff"}, "name": "Scandinavia", "type": "bar", "x": ["2012-08", "2012-09", "2012-10", "2012-11", "2012-12", "2013-01", "2013-02", "2013-03", "2013-04", "2013-05", "2013-06", "2013-07", "2013-08", "2013-09", "2013-10", "2013-11", "2013-12", "2014-01", "2014-02", "2014-03", "2014-04", "2014-05", "2014-06", "2014-07", "2014-08", "2014-09", "2014-10", "2014-11", "2014-12", "2015-01", "2015-02", "2015-03", "2015-04", "2015-05", "2015-06", "2015-07", "2015-08", "2015-09", "2015-10", "2015-11", "2015-12", "2016-01", "2016-02", "2016-03", "2016-04", "2016-05", "2016-06", "2016-07", "2016-08", "2016-09", "2016-10", "2016-11", "2016-12", "2017-01", "2017-02", "2017-03", "2017-04", "2017-05", "2017-06", "2017-07", "2017-08", "2017-09", "2017-10", "2017-11", "2017-12", "2018-01", "2018-02", "2018-03", "2018-04", "2018-05", "2018-06", "2018-07", "2018-08", "2018-09", "2018-10", "2018-12", "2019-01", "2019-02", "2019-03", "2019-04", "2019-05", "2019-06", "2019-07", "2019-08", "2019-09", "2019-10", "2019-11", "2019-12", "2020-01", "2020-02", "2020-03", "2020-04", "2020-06", "2020-07", "2020-08", "2020-09", "2020-11", "2020-12", "2021-01", "2021-02", "2021-03", "2021-04", "2021-05", "2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10"], "y": [70376.52, 173354.40999999997, 68079.02, 54878.149999999994, 9664.7, 56597.189999999995, 97621.48000000004, 190403.33, 124955.62, 160755.46999999997, 19904.19, 178765.97999999998, 73690.68000000001, 37696.36, 42698.94, 42845.29999999999, 140796.72999999998, 40153.69999999999, 77688.76000000001, 251162.74, 45948.08999999999, 89736.48, 112184.15000000001, 82133.01999999999, 107286.71000000002, 117338.18999999999, 162507.31999999998, 137142.06999999995, 190366.98999999993, 196771.58999999997, 54210.49999999999, 119078.78999999998, 111241.20999999999, 85280.89, 53914.20999999999, 46237.29, 116305.33, 99340.20999999998, 140218.58999999997, 65998.01, 202542.11000000002, 87546.46, 43162.99, 72421.41999999998, 27819.420000000006, 86179.40000000001, 60892.21000000001, 70908.15, 154383, 170093.25000000006, 91038.67, 40564, 36428.100000000006, 236494.65999999997, 23894.01, 118663.81000000001, 193582.46, 31404.060000000005, 157613.99000000002, 152304.02000000005, 161516.86999999997, 109311.1, 322053.27999999997, 121838.2325, 104601.49000000002, 61776.03, 121648.71, 48913.8, 3675.35, 102689.99999999999, 74827.69, 30886.25, 134568.16999999998, 32277.4, 158567.51999999996, 91259.68999999999, 132098.11, 141167.78, 56178.39000000001, 97659.30000000005, 375878.22000000015, 142022.08, 61087.41000000001, 59629.48, 69437.30000000002, 34427.87, 40303.77, 12106.39, 51223.69, 19620.83, 90337.20999999999, 79471.72999999998, 61900.45999999999, 59664.92, 167722.78000000006, 74527.12999999999, 143078.34999999998, 120441.41999999997, 76040.40000000001, 104876.10000000002, 233163.94999999998, 34531.95, 266031.8, 124331.08000000002, 147467.19999999995, 148098.62999999998, 23633.890000000003, 49565.28, 50991.869999999995, 107719.95999999999, 127501.02000000002, 130616.62000000001, 221916.86000000002, 111403.80000000002, 124506.12000000001, 90735.33000000002, 193197.4700000001, 151909.34999999995, 153933.44, 227066.59000000003, 85206.61999999997, 110399.77000000002, 53825.56999999999, 180188.83000000007, 51328.31, 179077.53, 74719.7, 32775.75, 117570.31999999999, 183821.20999999996, 59443.80999999999]}], "layout": {"hoverlabel": {"font": {"size": 8.8}}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "white", "showlakes": true, "showland": true, "subunitcolor": "#C8D4E3"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "white", "polar": {"angularaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}, "bgcolor": "white", "radialaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "yaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "zaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "baxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "bgcolor": "white", "caxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}}}, "title": {"font": {"size": 13.2}, "text": "Monthly Sales Revenue by Region"}, "updatemenus": [{"active": 0, "buttons": [{"args": [{"visible": [true, true, true, true, true, true, true, true, true]}, {"title": "Monthly Sales Revenue by Region"}], "label": "All Regions", "method": "update"}, {"args": [{"visible": [true, false, false, false, false, false, false, false, false]}, {"title": "Monthly Sales Revenue for British Isles"}], "label": "British Isles", "method": "update"}, {"args": [{"visible": [false, true, false, false, false, false, false, false, false]}, {"title": "Monthly Sales Revenue for Central America"}], "label": "Central America", "method": "update"}, {"args": [{"visible": [false, false, true, false, false, false, false, false, false]}, {"title": "Monthly Sales Revenue for Eastern Europe"}], "label": "Eastern Europe", "method": "update"}, {"args": [{"visible": [false, false, false, true, false, false, false, false, false]}, {"title": "Monthly Sales Revenue for North America"}], "label": "North America", "method": "update"}, {"args": [{"visible": [false, false, false, false, true, false, false, false, false]}, {"title": "Monthly Sales Revenue for Northern Europe"}], "label": "Northern Europe", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, true, false, false, false]}, {"title": "Monthly Sales Revenue for South America"}], "label": "South America", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, true, false, false]}, {"title": "Monthly Sales Revenue for Southern Europe"}], "label": "Southern Europe", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, false, true, false]}, {"title": "Monthly Sales Revenue for Western Europe"}], "label": "Western Europe", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, false, false, true]}, {"title": "Monthly Sales Revenue for Scandinavia"}], "label": "Scandinavia", "method": "update"}]}], "xaxis": {"linecolor": "black", "linewidth": 0.65, "tickfont": {"size": 8.8}, "title": {"text": "Month"}}, "yaxis": {"linecolor": "black", "linewidth": 0.65, "tickfont": {"size": 8.8}, "title": {"text": "Sales Revenue"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["sql_data_analyst.get_plotly_graph()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Sales Trends](../img/sales_trends_by_territory.jpg)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)"]}], "metadata": {"kernelspec": {"display_name": "ds4b_301p_dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}