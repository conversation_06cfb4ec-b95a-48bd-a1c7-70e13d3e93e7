Question,Answer,Context
What was the very first programming language <PERSON> used when he began learning to program on the IBM 1401?,He used an early version of Fortran on the IBM 1401.,"The language we used was an early version of Fortran. You had to type programs on punch cards, then stack them in the card reader and press a button to load the program into memory and run it."
Which microcomputer did <PERSON>’s father finally agree to buy for him around 1980?,A TRS-80.,"Computers were expensive in those days and it took me years of nagging before I convinced my father to buy one, a TRS-80, in about 1980. The gold standard then was the Apple II, but a TRS-80 was good enough."
What was the name of the startup <PERSON> co-founded that built software to create online stores?,Viaweb.,"We started a new company we called Viaweb, after the fact that our software worked via the web, and we got $10,000 in seed funding from <PERSON><PERSON><PERSON>'s husband <PERSON>."
Which friend of <PERSON> was the person responsible for the 1988 Internet Worm?,<PERSON> (often referred to as “<PERSON> or “Rtm” in the text).,"I remember when my friend <PERSON> got kicked out of Cornell for writing the internet worm of 1988, I was envious that he'd found such a spectacular way to get out of grad school."
What was the title of the second Lisp book that <PERSON> wrote after finishing *On Lisp*?,*ANSI Common Lisp.*,"So with my unerring nose for financial opportunity, I decided to write another book on Lisp. This would be a popular book, the sort of book that could be used as a textbook. I imagined myself living frugally off the royalties and spending all my time painting. (The painting on the cover of this book, ANSI Common Lisp, is one that I painted around this time.)"
