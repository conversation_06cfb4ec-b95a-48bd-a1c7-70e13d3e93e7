import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';

/// Video translation page with recording and file upload support
class VideoTranslationPage extends ConsumerStatefulWidget {
  const VideoTranslationPage({super.key});

  @override
  ConsumerState<VideoTranslationPage> createState() => _VideoTranslationPageState();
}

class _VideoTranslationPageState extends ConsumerState<VideoTranslationPage>
    with AutomaticKeepAliveClientMixin {
  bool _isRecording = false;
  bool _hasVideoFile = false;
  bool _isProcessing = false;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final theme = Theme.of(context);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Recording Section
          _buildRecordingSection(theme),
          
          const SizedBox(height: 16),
          
          // File Upload Section
          _buildFileUploadSection(theme),
          
          const SizedBox(height: 16),
          
          // Coming Soon Notice
          _buildComingSoonNotice(theme),
        ],
      ),
    );
  }

  Widget _buildRecordingSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              _isRecording ? Icons.videocam : Icons.videocam_off,
              size: 64,
              color: _isRecording 
                  ? AppTheme.errorColor 
                  : AppTheme.primaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _isRecording ? 'Gravando Vídeo...' : 'Gravação de Vídeo',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: _isRecording ? AppTheme.errorColor : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _isRecording 
                  ? 'Toque para parar a gravação'
                  : 'Toque para começar a gravar',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: 120,
              height: 120,
              child: ElevatedButton(
                onPressed: _toggleRecording,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isRecording 
                      ? AppTheme.errorColor 
                      : AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  shape: const CircleBorder(),
                  elevation: _isRecording ? 8 : 4,
                ),
                child: Icon(
                  _isRecording ? Icons.stop : Icons.videocam,
                  size: 48,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileUploadSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.video_library,
              size: 48,
              color: AppTheme.primaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Upload de Arquivo de Vídeo',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Suporte para MP4, AVI, MOV, MKV, WebM',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _uploadVideoFile,
              icon: const Icon(Icons.folder_open),
              label: const Text('Selecionar Arquivo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoonNotice(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.warningColor.withOpacity(0.1),
            AppTheme.primaryColor.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.video_settings,
            size: 48,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Tradução de Vídeo Avançada',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'A tradução multimodal de vídeo com Gemma 3N está sendo desenvolvida. Recursos planejados:',
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFeatureItem('🎬 Gravação de vídeo em tempo real'),
              _buildFeatureItem('📹 Upload de arquivos de vídeo'),
              _buildFeatureItem('🎵 Extração e tradução de áudio'),
              _buildFeatureItem('📝 Geração automática de legendas'),
              _buildFeatureItem('🖼️ Análise de frames para contexto'),
              _buildFeatureItem('⏱️ Sincronização temporal precisa'),
              _buildFeatureItem('🌐 Tradução para múltiplos idiomas'),
              _buildFeatureItem('📊 Análise de conteúdo visual'),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.infoColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.infoColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.infoColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Esta funcionalidade requer processamento intensivo e será otimizada para diferentes dispositivos.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.infoColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }

  void _toggleRecording() {
    setState(() {
      _isRecording = !_isRecording;
    });
    
    if (_isRecording) {
      _showInfoSnackBar('Gravação de vídeo iniciada (simulação)');
    } else {
      _showInfoSnackBar('Gravação de vídeo parada (simulação)');
    }
  }

  void _uploadVideoFile() {
    _showInfoSnackBar('Seleção de arquivo de vídeo em desenvolvimento');
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
