import 'package:flutter/material.dart';
import '../../../../core/services/gemma_downloader_service.dart';

class ModelManagementPage extends StatefulWidget {
  const ModelManagementPage({super.key});

  @override
  State<ModelManagementPage> createState() => _ModelManagementPageState();
}

class _ModelManagementPageState extends State<ModelManagementPage> {
  final Map<String, GemmaDownloaderService> _downloaders = {};
  final Map<String, Map<String, dynamic>> _modelInfos = {};
  final Map<String, double> _downloadProgress = {};
  final Map<String, bool> _isDownloading = {};
  String _huggingFaceToken = '';

  @override
  void initState() {
    super.initState();
    _initializeDownloaders();
    _loadModelInfos();
  }

  void _initializeDownloaders() {
    for (final model in Gemma3NModels.availableModels) {
      _downloaders[model.modelFilename] = GemmaDownloaderService(model: model);
      _downloadProgress[model.modelFilename] = 0.0;
      _isDownloading[model.modelFilename] = false;
    }
  }

  Future<void> _loadModelInfos() async {
    for (final model in Gemma3NModels.availableModels) {
      final downloader = _downloaders[model.modelFilename]!;
      final info = await downloader.getModelInfo();
      setState(() {
        _modelInfos[model.modelFilename] = info;
      });
    }
  }

  Future<void> _downloadModel(Gemma3NModel model) async {
    if (_huggingFaceToken.isEmpty) {
      _showTokenDialog();
      return;
    }

    setState(() {
      _isDownloading[model.modelFilename] = true;
      _downloadProgress[model.modelFilename] = 0.0;
    });

    try {
      final downloader = _downloaders[model.modelFilename]!;
      
      await downloader.downloadModel(
        token: _huggingFaceToken,
        onProgress: (progress, downloadedMB, totalMB) {
          setState(() {
            _downloadProgress[model.modelFilename] = progress;
          });
        },
      );

      // Refresh model info
      final info = await downloader.getModelInfo();
      setState(() {
        _modelInfos[model.modelFilename] = info;
        _isDownloading[model.modelFilename] = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ ${model.modelName} downloaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isDownloading[model.modelFilename] = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Failed to download ${model.modelName}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteModel(Gemma3NModel model) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete ${model.modelName}?'),
        content: const Text('This will permanently delete the downloaded model. You can re-download it later.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final downloader = _downloaders[model.modelFilename]!;
        await downloader.deleteModel();
        
        // Refresh model info
        final info = await downloader.getModelInfo();
        setState(() {
          _modelInfos[model.modelFilename] = info;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('🗑️ ${model.modelName} deleted successfully'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Failed to delete ${model.modelName}: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showTokenDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hugging Face Token Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('To download Gemma 3N models, you need a Hugging Face access token.'),
            const SizedBox(height: 16),
            const Text('Steps:'),
            const Text('1. Go to huggingface.co'),
            const Text('2. Create an account or log in'),
            const Text('3. Go to Settings > Access Tokens'),
            const Text('4. Create a new token with "Read" permission'),
            const Text('5. Accept the Gemma license'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Hugging Face Token',
                hintText: 'hf_...',
              ),
              onChanged: (value) => _huggingFaceToken = value,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (_huggingFaceToken.isNotEmpty) {
                // Retry download with token
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Offline Models'),
        backgroundColor: theme.colorScheme.primaryContainer,
        foregroundColor: theme.colorScheme.onPrimaryContainer,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadModelInfos,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.key),
            onPressed: _showTokenDialog,
            tooltip: 'Set Hugging Face Token',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadModelInfos,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.offline_bolt,
                          color: theme.colorScheme.primary,
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Gemma 3N Offline Models',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Download and manage Gemma 3N models for offline translation. These models run entirely on your device without internet connection.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ...Gemma3NModels.availableModels.map((model) => _buildModelCard(model)),
          ],
        ),
      ),
    );
  }

  Widget _buildModelCard(Gemma3NModel model) {
    final theme = Theme.of(context);
    final info = _modelInfos[model.modelFilename];
    final isDownloaded = info?['isDownloaded'] ?? false;
    final isDownloading = _isDownloading[model.modelFilename] ?? false;
    final progress = _downloadProgress[model.modelFilename] ?? 0.0;
    final localSizeMB = info?['localSizeMB'] ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    model.modelName,
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                if (isDownloaded)
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 20,
                  )
                else if (isDownloading)
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      value: progress,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              model.description,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.storage,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  isDownloaded 
                    ? '$localSizeMB MB (Downloaded)'
                    : '~${model.sizeInMB} MB',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const Spacer(),
                if (isDownloading)
                  Text(
                    '${(progress * 100).toStringAsFixed(1)}%',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            if (isDownloading) ...[
              const SizedBox(height: 8),
              LinearProgressIndicator(value: progress),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                if (!isDownloaded && !isDownloading)
                  ElevatedButton.icon(
                    onPressed: () => _downloadModel(model),
                    icon: const Icon(Icons.download),
                    label: const Text('Download'),
                  )
                else if (isDownloaded)
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: () {
                          // TODO: Set as active model
                        },
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('Use Model'),
                      ),
                      const SizedBox(width: 8),
                      OutlinedButton.icon(
                        onPressed: () => _deleteModel(model),
                        icon: const Icon(Icons.delete),
                        label: const Text('Delete'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                    ],
                  )
                else if (isDownloading)
                  ElevatedButton.icon(
                    onPressed: null,
                    icon: const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    label: const Text('Downloading...'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
