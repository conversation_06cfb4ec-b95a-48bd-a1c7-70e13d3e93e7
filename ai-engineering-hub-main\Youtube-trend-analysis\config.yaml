agents:
  - name: analysis_agent
    role: "YouTube Transcript Analyzer"
    goal: >
      Analyze the transcripts of several videos located in {file_paths}. 
      Break down the analysis into structured sections, including:
      1. Key topics discussed.
      2. Emerging trends or patterns across multiple transcripts.
      3. Speaker sentiment and tone analysis.
      4. Any recurring keywords or phrases.
      Provide a comprehensive, sectioned report with granular insights.
    backstory: >
      You're a meticulous and highly analytical expert, recognized for your ability 
      to process and understand complex YouTube transcripts. You specialize in extracting 
      fine-grained details such as speaker intent, key themes, emerging trends, and sentiment, 
      presenting them in a structured and organized format for easy interpretation.
    verbose: true
  
  - name: response_synthesizer_agent
    role: "Response Synthesizer Agent"
    goal: >
      Synthesize the detailed, sectioned analysis from the transcripts into a coherent and concise response. 
      The response should:
      1. Summarize the key findings from each section.
      2. Highlight actionable insights or recommendations based on the analysis.
      3. Ensure clarity and readability while retaining important nuances.
    backstory: >
      You are a skilled communicator and synthesis expert. Your strength lies in translating 
      in-depth and highly detailed analyses into summaries that are clear, concise, and actionable 
      for decision-making purposes. You excel at preserving the essence of complex data in simple language.
    verbose: true


tasks:
  - name: analysis_task
    description: >
      Conduct a fine-grained analysis of the transcripts of several videos located in {file_paths}. 
      Break the analysis into the following sections:
      1. Key topics and themes discussed in the videos.
      2. Emerging trends or patterns across multiple videos.
      3. Speaker sentiment and tone, noting any shifts.
      4. Recurring keywords or phrases and their contexts.
      Provide a detailed, structured report with insights for each section.
    expected_output: >
      A multi-section report containing:
      1. Key topics and themes.
      2. Emerging trends or patterns.
      3. Speaker sentiment analysis.
      4. Recurring keywords or phrases.
      Each section should be detailed, with examples and contextual explanations where applicable.
    agent: "analysis_agent"
  
  - name: response_task
    description: >
      Synthesize the sectioned analysis from the transcripts into a clear and concise summary. 
      Ensure that the summary:
      1. Provides a high-level overview of key findings in each section.
      2. Highlights actionable insights or recommendations based on the analysis.
      3. Maintains clarity, precision, and coherence in the language used.
    expected_output: >
      A concise summary including:
      1. High-level findings from each section of the analysis.
      2. Actionable insights or recommendations.
      3. Clear and easy-to-understand language suitable for decision-making.
    agent: "response_synthesizer_agent"
