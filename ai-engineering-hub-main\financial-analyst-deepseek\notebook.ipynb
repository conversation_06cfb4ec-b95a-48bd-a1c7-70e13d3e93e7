{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import re\n", "import json\n", "import os\n", "import yfinance as yf\n", "from crewai import Agent, Task, Crew, Process, LLM\n", "from crewai_tools import CodeInterpreterTool, FileReadTool\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "from pydantic import BaseModel, Field\n", "\n", "class QueryAnalysisOutput(BaseModel):\n", "    \"\"\"Structured output for the query analysis task.\"\"\"\n", "    symbol: str = Field(..., description=\"Stock ticker symbol (e.g., TSLA, AAPL).\")\n", "    timeframe: str = Field(..., description=\"Time period (e.g., '1d', '1mo', '1y').\")\n", "    action: str = Field(..., description=\"Action to be performed (e.g., 'fetch', 'plot').\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["llm = LLM(\n", "    model=\"sambanova/DeepSeek-R1-Distill-Llama-70B\",\n", "    temperature=0.7\n", ")\n", "\n", "query_parser_agent = Agent(\n", "    role=\"Stock Data Analyst\",\n", "    goal=\"Extract stock details and fetch required data from this user query: {query}.\",\n", "    backstory=\"You are a financial analyst specializing in stock market data retrieval.\",\n", "    llm=llm,\n", "    verbose=True,\n", "    memory=True,\n", ")\n", "\n", "query_parsing_task = Task(\n", "    description=\"Analyze the user query and extract stock details.\",\n", "    expected_output=\"A dictionary with keys: 'symbol', 'timeframe', 'action'.\",\n", "    output_pydantic=QueryAnalysisOutput,\n", "    agent=query_parser_agent,\n", ")\n", "\n", "\n", "\n", "# 2) Code writer agent\n", "code_writer_agent = Agent(\n", "    role=\"Senior Python Developer\",\n", "    goal=\"Write Python code to visualize stock data.\",\n", "    backstory=\"\"\"You are a Senior Python developer specializing in stock market data visualization. \n", "                 You are also a Pandas, Matplotlib and yfinance library expert.\n", "                 You are skilled at writing production-ready Python code\"\"\",\n", "    llm=llm,\n", "    verbose=True,\n", ")\n", "\n", "code_writer_task = Task(\n", "    description=\"\"\"Write Python code to visualize stock data based on the inputs from the stock analyst\n", "                   where you would find stock symbol, timeframe and action.\"\"\",\n", "    expected_output=\"A clean and executable Python script file (.py) for stock visualization.\",\n", "    agent=code_writer_agent,\n", ")\n", "\n", "# 3) Code interpreter agent (uses code interpreter tool)\n", "\n", "code_interpreter_tool = CodeInterpreterTool()\n", "\n", "code_execution_agent = Agent(\n", "    role=\"Senior Code Execution Expert\",\n", "    goal=\"Review and execute the generated Python code by code writer agent to visualize stock data.\",\n", "    backstory=\"You are a code execution expert. You are skilled at executing Python code.\",\n", "    tools=[code_interpreter_tool],\n", "    allow_code_execution=True,\n", "    llm=llm,\n", "    verbose=True,\n", ")\n", "\n", "code_execution_task = Task(\n", "    description=\"\"\"Review and execute the generated Python code by code writer agent to visualize stock data.\"\"\",\n", "    expected_output=\"A clean and executable Python script file (.py) for stock visualization.\",\n", "    agent=code_execution_agent,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### --- CREW SETUP --- ###\n", "\n", "crew = Crew(\n", "    agents=[query_parser_agent, code_writer_agent, code_execution_agent],\n", "    tasks=[query_parsing_task, code_writer_task, code_execution_task],\n", "    process=Process.sequential\n", ")\n", "\n", "# Run the crew with an example query\n", "result = crew.kickoff(inputs={\"query\": \"Plot YTD stock gain of Tesla\"})"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}