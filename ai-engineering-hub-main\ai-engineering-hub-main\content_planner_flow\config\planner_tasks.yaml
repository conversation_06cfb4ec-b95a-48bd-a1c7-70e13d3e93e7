analyze_draft:
  description: |
    Analyze the markdown file at {draft_path} to create a developer-focused
    technical overview

    1. Map out the core idea that the blog discusses

    2. Identify key sections and what each section is about

    3. For each section, extract all URLs that appear inside image markdown syntax ![](image_url)

    4. You must associate these identified image urls to their correspoinding sections, so that we can use them with the tweets as media pieces

    Focus on details that are important for a comprehensive understanding of
    the blog.
  expected_output: |
    A technical analysis containing:
    - Blog title and core concept/idea
    - Key technical sections identified with their main points
    - Important code examples or technical concepts covered
    - Key takeaways for developers
    - Relevant urls to media that are associated with the key sections and can be associated with a tweet, this must be done

create_twitter_thread_plan:
  description: |
    Develop an engaging Twitter thread based on the draft analysis provided and closely follow the writing style prvided in the {path_to_example_threads}

    The thread should break down complex technical concepts into digestible, tweet-sized chunks
    that maintain technical accuracy while being accessible.

    Plan should include:
    - A strong hook tweet that captures attention, it should be under 10 words, it must be same as the title of the blog
    - Logical flow from basic to advanced concepts
    - Code snippets or key technical highlights that fit Twitter's format
    - Relevant urls to media that are associated with the key sections and must be associated with their corresponding tweets
    - Clear takeaways for engineering audience

    Make sure to cover:
    - close follow the writing style provided in the {path_to_example_threads}
    - The core problem being solved
    - Key technical innovations or approaches
    - Interesting implementation details
    - Real-world applications or benefits
    - Call to action for the conclusion
    - Add relevant urls to each tweet that can be associated with a tweet

    Focus on creating a narrative that technical audiences will find valuable
    while keeping each tweet concise, accessible and impactful.
  expected_output: |
    A Twitter thread with a list of tweets, where each tweet has the following:
    - content
    - urls to media that are associated with the tweet, whenever possible
    - is_hook: true if the tweet is a hook tweet, false otherwise


create_linkedin_post_plan:
  description: |
    Develop a comprehensive LinkedIn post based on the draft analysis provided 

    The post should present technical content in a professional, long-form format
    while maintaining engagement and readability.

    Plan should include:
    - An attention-grabbing opening statement, it should be same as the title of the blog
    - Well-structured body that breaks down the technical content
    - Professional tone suitable for LinkedIn's business audience
    - One main blog URL placed strategically at the end of the post
    - Strategic use of line breaks and formatting
    - Relevant hashtags (3-5 maximum)

    Make sure to cover:
    - The core technical problem and its business impact
    - Key solutions and technical approaches
    - Real-world applications and benefits
    - Professional insights or lessons learned
    - Clear call to action

    Focus on creating content that resonates with both technical professionals
    and business leaders while maintaining technical accuracy.
  expected_output: |
    A LinkedIn post plan containing:
      - content
      - a main blog url that is associated with the post
