{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.101\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.101\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.101\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.101\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "B16AC4F9476D3D3DB07C2992DF04D3FE0FEBD802AA7C195D4EAB2A3C5784B9E7"}, "extensions": {"ui": {"developer_mode": "6CC5B6DA929D7800E7A48CD82C2CA836DADF5D5255D5E9EC4E428402D83092B7"}}, "homepage": "F0AED58634B79A8A80E013CBF63E4C6DA5EA12872FC3A1F4DE48E3364EB79C9F", "homepage_is_newtabpage": "CF2558DE689BFAD026DCD9156595FECDD69F721C212518091845B7D0C3EE9C70", "session": {"restore_on_startup": "00E0902107EFBBF59CB7299E399D37E7B761AFEED489A329282DD35B96B0A1C3", "startup_urls": "0696C4F872E180925FF9A84883CCA8ABDC8661BDA520BA6610B7E13A6008754B"}}, "browser": {"show_home_button": "B0AD4D57462B1F2082816C8A8856C635D5B572584A6DECA208DEEC2EBC82E4D0"}, "default_search_provider_data": {"template_url_data": "5995BCB3EFAA0B624803E5B0457DD3C97101C44E8E1D9598FBE6F73FF8BD9BB5"}, "enterprise_signin": {"policy_recovery_token": "A4F9A50AF66A41C9BD0FEC8EE408F83674B39F8F8D977DC7C56D32F1EA192318"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "59F5DF75427464EADBF1CA5A2E87CD8E2B4A261C87D7FF9E3AF728245F568AC6", "fignfifoniblkonapihmkfakmlgkbkcf": "B40CC704039EC49BDD6306C933B037FE500E3663B502F16BA2DFF80E1A8C5E59", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "CAFBC445BE8F343E9714AB85E2852B2B274E704CF9D8D9083378EDD2B99B49EB", "nkeimhogjdpnpccoofpliimaahmaaome": "71E018EEF196DBC4A4C78E062A2CEF755F4B93035AB98C0A144058E2A6661722"}, "ui": {"developer_mode": "5122188161DD1615668B714F70AC042D7866DD4C5154E1CB38A62F71236C718F"}}, "google": {"services": {"account_id": "667C634EBD64F91D6B31AEDE68AACC86DBBBD00CA9D47FFE0DC16AE54AC59529", "last_signed_in_username": "00CFC8713CD592EDFB6A58E1BE27165FFCD994D0916CB1964CB5808F0D42FD8A", "last_username": "F4F5449EB76FF16FB77205A831DCCF3A4FA1D4CEA158D5614D0FC7005B56B043"}}, "homepage": "9445BAF39F0F10E4D880D06345C52CC803AE05ED6A94A3DC814FD7FBBA37DB11", "homepage_is_newtabpage": "DF84DEDD6C6AF73FAB8D2ED071CC06CCE241890BDE107DEE791AB5D87D398F69", "media": {"cdm": {"origin_data": "3658A271F1AAC58A0ACC18465539B0CBF4298B8D403237D6C0A1A5951E9A30C2"}, "storage_id_salt": "2CC933E8A2EEB8F50D43FAAD18AF8B08B05BBABEED4A34BD125C66FE8F46D8CF"}, "module_blocklist_cache_md5_digest": "28E48DF261F34BDE69730F2DF8945E1303385564EC95E9DC220AC0CD2A550139", "pinned_tabs": "14F833BDDE3082193892B1C0284FA4FD01611C09BE4B742FB3C44C002BD4FFFE", "prefs": {"preference_reset_time": "D163DFB5621AE1DF2A38B5E0FE6A8D1D9AC85801234B282D4D9E7A93AEF2EFA6"}, "safebrowsing": {"incidents_sent": "52C2297E35B380F68579C0A11A9AEFF7266EE3F6A033DDCD425E794FCF1E21A5"}, "search_provider_overrides": "D89276741B47F344AE80E8990123724DA089CD090303DB299DE2483BA286BB02", "session": {"restore_on_startup": "0EAC1DEB9C49766A0FF5FBAB1FAF11C27F0486CCCF42FBCE4E51C6F369FEC377", "startup_urls": "51049E623E977BB7E638F8210580BDB9D12B94757E18ADD60CB8A8C888F8A1DF"}}, "super_mac": "5429E4BE5C24EAA09151C4543C2A0BDC5D46736EEA69D778836CD38766A8806A"}, "safebrowsing": {"incidents_sent": {"1": {"account_values.browser.show_home_button": "*********", "account_values.extensions.ui.developer_mode": "**********", "account_values.homepage": "**********", "account_values.homepage_is_newtabpage": "**********", "account_values.session.restore_on_startup": "**********", "account_values.session.startup_urls": "**********", "browser.show_home_button": "**********", "extensions.settings": "**********", "extensions.ui.developer_mode": "**********", "google.services.account_id": "**********", "google.services.last_signed_in_username": "**********", "google.services.last_username": "**********", "homepage": "**********", "homepage_is_newtabpage": "**********", "media.cdm.origin_data": "**********", "pinned_tabs": "**********", "safebrowsing.incidents_sent": "**********", "session.restore_on_startup": "**********", "session.startup_urls": "**********"}}}}